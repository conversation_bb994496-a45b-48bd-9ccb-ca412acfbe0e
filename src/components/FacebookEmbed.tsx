import { useEffect, useRef, useState } from 'react';

const FacebookEmbed = () => {
  const embedRef = useRef<HTMLDivElement>(null);
  const isScriptAdded = useRef(false);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    // Check if the script is already added
    if (isScriptAdded.current) return;

    // Check if the script already exists in the document
    const existingScript = document.querySelector('script[src*="facebook.com/sdk.js"]');
    
    if (existingScript) {
      // If script exists, just parse the new content
      if (window.FB) {
        window.FB.XFBML.parse(embedRef.current);
        setIsLoading(false);
      }
      return;
    }

    // Initialize Facebook SDK
    window.fbAsyncInit = function() {
      window.FB.init({
        xfbml: true,
        version: 'v17.0'
      });
      
      // Parse any existing XFBML elements
      if (window.FB) {
        window.FB.XFBML.parse();
        setIsLoading(false);
      }
    };

    // Load the Facebook SDK script
    const script = document.createElement('script');
    script.async = true;
    script.defer = true;
    script.crossOrigin = 'anonymous';
    script.nonce = 'facebook-sdk';
    script.src = 'https://connect.facebook.net/en_US/sdk.js';
    
    script.onload = () => {
      console.log('Facebook SDK loaded');
      isScriptAdded.current = true;
      setIsLoading(false);
    };
    
    script.onerror = (error) => {
      console.error('Error loading Facebook SDK:', error);
      setHasError(true);
      setIsLoading(false);
    };
    
    document.body.appendChild(script);

    return () => {
      // Don't remove the script as it might be used by other components
      // Just clean up the window.FB reference if needed
      if (window.FB) {
        try {
          delete window.FB;
        } catch (e) {
          window.FB = undefined;
        }
      }
    };
  }, []);

  return (
    <div className="w-full max-w-lg mx-auto px-2 sm:px-4">
      <div ref={embedRef} className="facebook-embed-container w-full">
        {isLoading && (
          <div className="flex items-center justify-center p-8 bg-gray-50 rounded-lg border border-gray-200">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-600 text-sm sm:text-base">Loading Facebook feed...</p>
            </div>
          </div>
        )}
        
        {hasError && (
          <div className="flex items-center justify-center p-8 bg-red-50 rounded-lg border border-red-200">
            <div className="text-center">
              <div className="text-red-500 mb-2">
                <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <p className="text-red-700 text-sm sm:text-base">Could not load Facebook feed. Please check your connection.</p>
            </div>
          </div>
        )}
        
        <div className={`w-full bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden ${
          hasError ? 'hidden' : 'block'
        }`}>
          <div 
            className="fb-page w-full" 
            data-href="https://www.facebook.com/profile.php?id=100092532255106"
            data-tabs="timeline" 
            data-width="" 
            data-height="400" 
            data-small-header="true"
            data-adapt-container-width="true" 
            data-hide-cover="false" 
            data-show-facepile="true"
          >
            <blockquote 
              cite="https://www.facebook.com/profile.php?id=100092532255106" 
              className="fb-xfbml-parse-ignore p-4"
            >
              <a 
                href="https://www.facebook.com/profile.php?id=100092532255106"
                className="block w-full text-center text-gray-700 hover:text-blue-600 transition-colors duration-200 no-underline"
              >
                <div className="flex items-center justify-center space-x-2">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                  <span className="text-sm sm:text-base">Visit our Facebook page</span>
                </div>
              </a>
            </blockquote>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FacebookEmbed;

// Declare global FB to prevent TypeScript errors
declare global {
  interface Window {
    fbAsyncInit?: () => void;
    FB?: {
      init: (config: { xfbml: boolean; version: string }) => void;
      XFBML: {
        parse: (node?: HTMLElement) => void;
      };
    };
  }
}
