import { useState, useEffect } from 'react';

/**
 * Custom hook to detect if the current screen size is mobile
 * Uses CSS media query to determine mobile breakpoint (< 768px)
 * @returns boolean indicating if the screen is mobile size
 */
export function useIsMobile(): boolean {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Create media query for mobile breakpoint (matches Tailwind's md breakpoint)
    const mediaQuery = window.matchMedia('(max-width: 767px)');
    
    // Set initial value
    setIsMobile(mediaQuery.matches);
    
    // Create event listener function
    const handleMediaQueryChange = (event: MediaQueryListEvent) => {
      setIsMobile(event.matches);
    };
    
    // Add event listener
    mediaQuery.addEventListener('change', handleMediaQueryChange);
    
    // Cleanup function
    return () => {
      mediaQuery.removeEventListener('change', handleMediaQueryChange);
    };
  }, []);

  return isMobile;
}

export default useIsMobile;