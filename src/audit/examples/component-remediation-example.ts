/**
 * Component Remediation Example
 * 
 * This example demonstrates how to use the ComponentRemediationEngine to:
 * 1. Standardize component implementations
 * 2. Correct component state and variant handling
 * 3. Implement interaction pattern standardization
 * 4. Validate component compliance against design system standards
 */

import { ComponentRemediationEngine, ComponentStandard } from '../remediation/component-remediation';
import { FileInfo, Issue, IssueType, Severity, Category } from '../types';
import { DesignTokenSystem } from '../design-tokens/types';

// Mock design tokens for the example
const mockDesignTokens: DesignTokenSystem = {
  colors: {
    primary: {
      50: '#eff6ff', 100: '#dbeafe', 200: '#bfdbfe', 300: '#93c5fd', 400: '#60a5fa',
      500: '#3b82f6', 600: '#2563eb', 700: '#1d4ed8', 800: '#1e40af', 900: '#1e3a8a', 950: '#172554'
    },
    secondary: {
      50: '#f8fafc', 100: '#f1f5f9', 200: '#e2e8f0', 300: '#cbd5e1', 400: '#94a3b8',
      500: '#64748b', 600: '#475569', 700: '#334155', 800: '#1e293b', 900: '#0f172a', 950: '#020617'
    },
    semantic: {
      success: { light: '#dcfce7', default: '#16a34a', dark: '#15803d', contrast: '#ffffff' },
      warning: { light: '#fef3c7', default: '#d97706', dark: '#b45309', contrast: '#ffffff' },
      error: { light: '#fee2e2', default: '#dc2626', dark: '#b91c1c', contrast: '#ffffff' },
      info: { light: '#dbeafe', default: '#2563eb', dark: '#1d4ed8', contrast: '#ffffff' }
    },
    neutral: {
      50: '#f9fafb', 100: '#f3f4f6', 200: '#e5e7eb', 300: '#d1d5db', 400: '#9ca3af',
      500: '#6b7280', 600: '#4b5563', 700: '#374151', 800: '#1f2937', 900: '#111827', 950: '#030712'
    },
    surface: {
      background: '#ffffff', foreground: '#111827', card: '#ffffff', cardForeground: '#111827',
      popover: '#ffffff', popoverForeground: '#111827', muted: '#f3f4f6', mutedForeground: '#6b7280',
      accent: '#f3f4f6', accentForeground: '#111827', border: '#e5e7eb', input: '#ffffff', ring: '#3b82f6'
    }
  },
  typography: {
    fontFamilies: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      serif: ['Georgia', 'serif'],
      mono: ['Monaco', 'monospace'],
      display: ['Inter', 'system-ui', 'sans-serif']
    },
    fontSizes: {
      xs: '0.75rem', sm: '0.875rem', base: '1rem', lg: '1.125rem', xl: '1.25rem',
      '2xl': '1.5rem', '3xl': '1.875rem', '4xl': '2.25rem', '5xl': '3rem',
      '6xl': '3.75rem', '7xl': '4.5rem', '8xl': '6rem', '9xl': '8rem'
    },
    fontWeights: {
      thin: 100, extralight: 200, light: 300, normal: 400, medium: 500,
      semibold: 600, bold: 700, extrabold: 800, black: 900
    },
    lineHeights: { none: 1, tight: 1.25, snug: 1.375, normal: 1.5, relaxed: 1.625, loose: 2 },
    letterSpacing: {
      tighter: '-0.05em', tight: '-0.025em', normal: '0em',
      wide: '0.025em', wider: '0.05em', widest: '0.1em'
    },
    typeScale: {
      h1: { fontSize: '2.25rem', fontWeight: 700, lineHeight: 1.2, letterSpacing: '-0.025em', fontFamily: ['Inter', 'system-ui', 'sans-serif'] },
      h2: { fontSize: '1.875rem', fontWeight: 600, lineHeight: 1.3, letterSpacing: '-0.025em', fontFamily: ['Inter', 'system-ui', 'sans-serif'] },
      h3: { fontSize: '1.5rem', fontWeight: 600, lineHeight: 1.4, letterSpacing: '0em', fontFamily: ['Inter', 'system-ui', 'sans-serif'] },
      h4: { fontSize: '1.25rem', fontWeight: 500, lineHeight: 1.4, letterSpacing: '0em', fontFamily: ['Inter', 'system-ui', 'sans-serif'] },
      h5: { fontSize: '1.125rem', fontWeight: 500, lineHeight: 1.5, letterSpacing: '0em', fontFamily: ['Inter', 'system-ui', 'sans-serif'] },
      h6: { fontSize: '1rem', fontWeight: 500, lineHeight: 1.5, letterSpacing: '0em', fontFamily: ['Inter', 'system-ui', 'sans-serif'] },
      body: { fontSize: '1rem', fontWeight: 400, lineHeight: 1.5, letterSpacing: '0em', fontFamily: ['Inter', 'system-ui', 'sans-serif'] },
      bodyLarge: { fontSize: '1.125rem', fontWeight: 400, lineHeight: 1.5, letterSpacing: '0em', fontFamily: ['Inter', 'system-ui', 'sans-serif'] },
      bodySmall: { fontSize: '0.875rem', fontWeight: 400, lineHeight: 1.5, letterSpacing: '0em', fontFamily: ['Inter', 'system-ui', 'sans-serif'] },
      caption: { fontSize: '0.75rem', fontWeight: 400, lineHeight: 1.4, letterSpacing: '0.025em', fontFamily: ['Inter', 'system-ui', 'sans-serif'] },
      overline: { fontSize: '0.75rem', fontWeight: 600, lineHeight: 1.4, letterSpacing: '0.1em', fontFamily: ['Inter', 'system-ui', 'sans-serif'] }
    }
  },
  spacing: {
    scale: {
      0: '0px', px: '1px', 0.5: '0.125rem', 1: '0.25rem', 1.5: '0.375rem', 2: '0.5rem',
      2.5: '0.625rem', 3: '0.75rem', 3.5: '0.875rem', 4: '1rem', 5: '1.25rem', 6: '1.5rem',
      7: '1.75rem', 8: '2rem', 9: '2.25rem', 10: '2.5rem', 11: '2.75rem', 12: '3rem',
      14: '3.5rem', 16: '4rem', 20: '5rem', 24: '6rem', 28: '7rem', 32: '8rem',
      36: '9rem', 40: '10rem', 44: '11rem', 48: '12rem', 52: '13rem', 56: '14rem',
      60: '15rem', 64: '16rem', 72: '18rem', 80: '20rem', 96: '24rem'
    },
    semantic: {
      component: {
        padding: { xs: '0.5rem', sm: '0.75rem', md: '1rem', lg: '1.5rem', xl: '2rem' },
        margin: { xs: '0.5rem', sm: '0.75rem', md: '1rem', lg: '1.5rem', xl: '2rem' },
        gap: { xs: '0.25rem', sm: '0.5rem', md: '0.75rem', lg: '1rem', xl: '1.5rem' }
      },
      layout: { section: '4rem', container: '2rem', content: '1.5rem' }
    },
    layout: {
      containerMaxWidth: { sm: '640px', md: '768px', lg: '1024px', xl: '1280px', '2xl': '1536px' },
      containerPadding: { mobile: '1rem', tablet: '2rem', desktop: '3rem' }
    }
  },
  layout: {
    grid: {
      columns: { default: 12, sm: 4, md: 8, lg: 12, xl: 12 },
      gutters: { xs: '0.5rem', sm: '1rem', md: '1.5rem', lg: '2rem', xl: '3rem' },
      margins: { mobile: '1rem', tablet: '2rem', desktop: '3rem', wide: '4rem' },
      baseline: { unit: '0.25rem', scale: 4 }
    },
    breakpoints: {
      xs: '475px', sm: '640px', md: '768px', lg: '1024px', xl: '1280px', '2xl': '1536px',
      ranges: { mobile: '0-640px', tablet: '641px-1024px', desktop: '1025px-1536px', wide: '1537px+' }
    },
    containers: {
      maxWidths: { xs: '475px', sm: '640px', md: '768px', lg: '1024px', xl: '1280px', '2xl': '1536px', full: '100%' },
      padding: { mobile: '1rem', tablet: '2rem', desktop: '3rem' },
      centering: {
        auto: 'margin: 0 auto',
        flex: { horizontal: 'justify-content: center', vertical: 'align-items: center', both: 'justify-content: center; align-items: center' },
        grid: { horizontal: 'justify-items: center', vertical: 'align-items: center', both: 'place-items: center' }
      }
    },
    patterns: {
      stack: {
        gap: { xs: '0.25rem', sm: '0.5rem', md: '1rem', lg: '1.5rem', xl: '2rem' },
        alignment: { start: 'align-items: flex-start', center: 'align-items: center', end: 'align-items: flex-end', stretch: 'align-items: stretch' }
      },
      cluster: {
        gap: { xs: '0.25rem', sm: '0.5rem', md: '1rem', lg: '1.5rem', xl: '2rem' },
        justify: {
          start: 'justify-content: flex-start', center: 'justify-content: center', end: 'justify-content: flex-end',
          between: 'justify-content: space-between', around: 'justify-content: space-around', evenly: 'justify-content: space-evenly'
        },
        align: { start: 'align-items: flex-start', center: 'align-items: center', end: 'align-items: flex-end', baseline: 'align-items: baseline' }
      },
      sidebar: { sidebarWidth: { narrow: '200px', default: '300px', wide: '400px' }, gap: '1rem', breakpoint: '768px', contentMinWidth: '50%' },
      switcher: { threshold: '768px', gap: '1rem', limit: 4 },
      cover: {
        minHeight: { viewport: '100vh', container: '100%', content: 'auto' },
        padding: { top: '2rem', bottom: '2rem', sides: '1rem' }
      },
      grid: {
        minItemWidth: { xs: '200px', sm: '250px', md: '300px', lg: '350px' },
        gap: { xs: '0.5rem', sm: '1rem', md: '1.5rem', lg: '2rem', xl: '3rem' },
        autoFit: true, autoFill: false
      }
    },
    validation: {
      rules: [], patterns: [],
      accessibility: {
        focusManagement: { tabOrder: true, focusVisible: true, skipLinks: true },
        semanticStructure: { headingHierarchy: true, landmarkRoles: true, listStructure: true },
        responsiveDesign: { minTouchTarget: '44px', maxLineLength: '75ch', scalableText: true }
      }
    }
  },
  metadata: { version: '1.0.0', generatedAt: '2024-01-01T00:00:00Z', source: 'example' }
};

// Example component files with various issues
const exampleFiles: FileInfo[] = [
  {
    path: 'src/components/InconsistentButton.tsx',
    content: `import React from 'react';

// This button is missing required props and has inconsistent styling
export const InconsistentButton = ({ text, click }) => {
  return (
    <button 
      style={{ backgroundColor: '#3b82f6', color: 'white', padding: '8px 16px' }}
      onClick={click}
    >
      {text}
    </button>
  );
};`,
    extension: '.tsx',
    size: 400,
    lastModified: new Date()
  },
  {
    path: 'src/components/NonStandardInput.tsx',
    content: `import React, { useState } from 'react';

// This input doesn't follow the standard interface and lacks accessibility
export const NonStandardInput = ({ placeholder }) => {
  const [val, setVal] = useState('');
  
  return (
    <input 
      type="text"
      value={val}
      onChange={(e) => setVal(e.target.value)}
      placeholder={placeholder}
      style={{ border: '1px solid gray', padding: '4px' }}
    />
  );
};`,
    extension: '.tsx',
    size: 500,
    lastModified: new Date()
  },
  {
    path: 'src/components/MixedStylingCard.tsx',
    content: `import React from 'react';
import styles from './Card.module.css';

// This card mixes styling approaches and lacks proper variants
export const MixedStylingCard = ({ children, type }) => {
  const cardStyle = {
    padding: '16px',
    backgroundColor: type === 'primary' ? '#3b82f6' : '#f3f4f6'
  };

  return (
    <div 
      className={styles.card}
      style={cardStyle}
    >
      {children}
    </div>
  );
};`,
    extension: '.tsx',
    size: 600,
    lastModified: new Date()
  }
];

// Example issues that would be detected by analyzers
const exampleIssues: Issue[] = [
  {
    id: 'button-missing-required-props',
    type: IssueType.COMPONENT_INCONSISTENCY,
    severity: Severity.HIGH,
    category: Category.COMPONENT,
    description: "Component 'InconsistentButton' missing required props: children",
    location: {
      filePath: 'src/components/InconsistentButton.tsx',
      lineNumber: 4,
      columnNumber: 0,
      context: 'export const InconsistentButton = ({ text, click }) => {'
    },
    suggestion: 'Add required children prop and standardize prop names',
    autoFixable: true,
    impact: {
      userExperience: 7,
      maintenanceEffort: 5,
      implementationComplexity: 3
    }
  },
  {
    id: 'input-missing-required-props',
    type: IssueType.COMPONENT_INCONSISTENCY,
    severity: Severity.HIGH,
    category: Category.COMPONENT,
    description: "Component 'NonStandardInput' missing required props: value, onChange",
    location: {
      filePath: 'src/components/NonStandardInput.tsx',
      lineNumber: 4,
      columnNumber: 0,
      context: 'export const NonStandardInput = ({ placeholder }) => {'
    },
    suggestion: 'Add required value and onChange props for controlled input',
    autoFixable: true,
    impact: {
      userExperience: 8,
      maintenanceEffort: 6,
      implementationComplexity: 4
    }
  },
  {
    id: 'card-mixed-styling',
    type: IssueType.COMPONENT_INCONSISTENCY,
    severity: Severity.MEDIUM,
    category: Category.COMPONENT,
    description: "Component 'MixedStylingCard' uses mixed styling approaches",
    location: {
      filePath: 'src/components/MixedStylingCard.tsx',
      lineNumber: 5,
      columnNumber: 0,
      context: 'export const MixedStylingCard = ({ children, type }) => {'
    },
    suggestion: 'Standardize to use CSS modules only and implement proper variants',
    autoFixable: false,
    impact: {
      userExperience: 4,
      maintenanceEffort: 7,
      implementationComplexity: 6
    }
  }
];

/**
 * Example: Basic Component Remediation
 */
export async function basicComponentRemediationExample() {
  console.log('🔧 Component Remediation Example: Basic Usage\n');

  // Initialize the remediation engine
  const engine = new ComponentRemediationEngine(mockDesignTokens);

  // Remediate component issues
  const result = await engine.remediateComponentIssues(exampleIssues, exampleFiles);

  console.log('Remediation Results:');
  console.log(`✅ Success: ${result.success}`);
  console.log(`📁 Files Modified: ${result.filesModified.length}`);
  console.log(`🔄 Total Replacements: ${result.summary.totalReplacements}`);
  console.log(`🧩 Components Standardized: ${result.summary.componentsStandardized}`);
  console.log(`🎨 Variants Fixed: ${result.summary.variantsFixed}`);
  console.log(`⚡ Interaction Patterns Added: ${result.summary.interactionPatternsAdded}\n`);

  if (result.errors.length > 0) {
    console.log('❌ Errors encountered:');
    result.errors.forEach(error => {
      console.log(`  - ${error.filePath}:${error.lineNumber} - ${error.error}`);
    });
    console.log();
  }

  return result;
}

/**
 * Example: Component Implementation Standardization
 */
export async function componentStandardizationExample() {
  console.log('📐 Component Remediation Example: Implementation Standardization\n');

  const engine = new ComponentRemediationEngine(mockDesignTokens);

  // Standardize component implementations
  const replacements = await engine.standardizeComponentImplementations(exampleFiles);

  console.log('Standardization Results:');
  console.log(`🔄 Total Replacements: ${replacements.length}`);
  
  replacements.forEach((replacement, index) => {
    console.log(`\n${index + 1}. ${replacement.componentName} (${replacement.replacementType}):`);
    console.log(`   📁 File: ${replacement.filePath}:${replacement.lineNumber}`);
    console.log(`   🔄 Change: ${replacement.originalCode} → ${replacement.standardizedCode}`);
  });

  return replacements;
}

/**
 * Example: State and Variant Correction
 */
export async function stateAndVariantCorrectionExample() {
  console.log('🎛️ Component Remediation Example: State and Variant Correction\n');

  const engine = new ComponentRemediationEngine(mockDesignTokens);

  // Correct component state and variants
  const replacements = await engine.correctComponentStateAndVariants(exampleFiles);

  console.log('State and Variant Correction Results:');
  console.log(`🔄 Total Replacements: ${replacements.length}`);
  
  const stateReplacements = replacements.filter(r => r.replacementType === 'state-handling');
  const variantReplacements = replacements.filter(r => r.replacementType === 'variant-correction');

  console.log(`🎛️ State Handling Corrections: ${stateReplacements.length}`);
  console.log(`🎨 Variant Corrections: ${variantReplacements.length}`);

  return replacements;
}

/**
 * Example: Interaction Pattern Standardization
 */
export async function interactionPatternStandardizationExample() {
  console.log('⚡ Component Remediation Example: Interaction Pattern Standardization\n');

  const engine = new ComponentRemediationEngine(mockDesignTokens);

  // Standardize interaction patterns
  const replacements = await engine.standardizeInteractionPatterns(exampleFiles);

  console.log('Interaction Pattern Standardization Results:');
  console.log(`🔄 Total Replacements: ${replacements.length}`);
  
  replacements.forEach((replacement, index) => {
    console.log(`\n${index + 1}. ${replacement.componentName}:`);
    console.log(`   ⚡ Pattern: ${replacement.replacementType}`);
    console.log(`   📁 File: ${replacement.filePath}:${replacement.lineNumber}`);
    console.log(`   ➕ Added: ${replacement.standardizedCode.trim()}`);
  });

  return replacements;
}

/**
 * Example: Component Compliance Validation
 */
export async function componentComplianceValidationExample() {
  console.log('✅ Component Remediation Example: Compliance Validation\n');

  const engine = new ComponentRemediationEngine(mockDesignTokens);

  // Validate component compliance
  const validation = await engine.validateComponentCompliance(exampleFiles);

  console.log('Component Compliance Results:');
  console.log(`✅ Compliant Components: ${validation.compliantComponents}`);
  console.log(`❌ Non-Compliant Components: ${validation.nonCompliantComponents}`);
  console.log(`📊 Overall Score: ${validation.overallScore.toFixed(1)}%\n`);

  if (validation.complianceIssues.length > 0) {
    console.log('Compliance Issues:');
    validation.complianceIssues.forEach((issue, index) => {
      console.log(`\n${index + 1}. ${issue.componentName} (${issue.severity}):`);
      console.log(`   📁 File: ${issue.filePath}`);
      console.log(`   🔍 Issue: ${issue.description}`);
    });
  }

  return validation;
}

/**
 * Example: Custom Component Standard
 */
export async function customComponentStandardExample() {
  console.log('🎯 Component Remediation Example: Custom Component Standard\n');

  const engine = new ComponentRemediationEngine(mockDesignTokens);

  // Define a custom component standard
  const customStandard: ComponentStandard = {
    name: 'CustomAlert',
    requiredProps: [
      { name: 'message', type: 'string', required: true },
      { name: 'type', type: "'success' | 'warning' | 'error' | 'info'", required: true }
    ],
    optionalProps: [
      { name: 'dismissible', type: 'boolean', defaultValue: 'false', required: false },
      { name: 'onDismiss', type: '() => void', required: false }
    ],
    variants: [
      {
        name: 'success',
        props: { type: ['success'] },
        defaultProps: { type: 'success' },
        styling: {
          backgroundColor: 'var(--color-success-light)',
          color: 'var(--color-success-dark)',
          border: '1px solid var(--color-success-default)'
        }
      },
      {
        name: 'error',
        props: { type: ['error'] },
        defaultProps: { type: 'error' },
        styling: {
          backgroundColor: 'var(--color-error-light)',
          color: 'var(--color-error-dark)',
          border: '1px solid var(--color-error-default)'
        }
      }
    ],
    stylingApproach: 'css-modules',
    stateHandling: 'props-only',
    interactionPatterns: [
      {
        type: 'click',
        handler: 'onDismiss',
        implementation: 'const handleDismiss = () => { if (dismissible && onDismiss) onDismiss(); };',
        accessibility: [
          { attribute: 'role', value: 'alert', required: true },
          { attribute: 'aria-live', value: 'polite', required: true }
        ]
      }
    ],
    accessibilityRequirements: [
      { attribute: 'role', value: 'alert', required: true },
      { attribute: 'aria-live', value: 'polite', required: true }
    ]
  };

  // Add the custom standard
  engine.addComponentStandard(customStandard);

  console.log('Custom Component Standard Added:');
  console.log(`📦 Component: ${customStandard.name}`);
  console.log(`🔧 Required Props: ${customStandard.requiredProps.map(p => p.name).join(', ')}`);
  console.log(`⚙️ Optional Props: ${customStandard.optionalProps.map(p => p.name).join(', ')}`);
  console.log(`🎨 Variants: ${customStandard.variants.map(v => v.name).join(', ')}`);
  console.log(`💅 Styling: ${customStandard.stylingApproach}`);
  console.log(`⚡ Interactions: ${customStandard.interactionPatterns.map(p => p.type).join(', ')}\n`);

  // Retrieve and verify the standard
  const retrieved = engine.getComponentStandard('CustomAlert');
  console.log(`✅ Standard Retrieved: ${retrieved ? 'Yes' : 'No'}`);

  // List all standards
  const allStandards = engine.getAllComponentStandards();
  console.log(`📋 Total Standards: ${allStandards.length}`);
  console.log(`📦 Available Components: ${allStandards.map(s => s.name).join(', ')}`);

  return customStandard;
}

/**
 * Example: Complete Component Remediation Workflow
 */
export async function completeRemediationWorkflowExample() {
  console.log('🚀 Component Remediation Example: Complete Workflow\n');

  const engine = new ComponentRemediationEngine(mockDesignTokens);

  console.log('Step 1: Validate Current State');
  const initialValidation = await engine.validateComponentCompliance(exampleFiles);
  console.log(`Initial Compliance Score: ${initialValidation.overallScore.toFixed(1)}%\n`);

  console.log('Step 2: Remediate Issues');
  const remediationResult = await engine.remediateComponentIssues(exampleIssues, exampleFiles);
  console.log(`Remediation Applied: ${remediationResult.summary.totalReplacements} changes\n`);

  console.log('Step 3: Standardize Implementations');
  const standardizationReplacements = await engine.standardizeComponentImplementations(exampleFiles);
  console.log(`Standardization Applied: ${standardizationReplacements.length} changes\n`);

  console.log('Step 4: Correct State and Variants');
  const stateVariantReplacements = await engine.correctComponentStateAndVariants(exampleFiles);
  console.log(`State/Variant Corrections: ${stateVariantReplacements.length} changes\n`);

  console.log('Step 5: Standardize Interaction Patterns');
  const interactionReplacements = await engine.standardizeInteractionPatterns(exampleFiles);
  console.log(`Interaction Pattern Updates: ${interactionReplacements.length} changes\n`);

  console.log('Step 6: Final Validation');
  const finalValidation = await engine.validateComponentCompliance(exampleFiles);
  console.log(`Final Compliance Score: ${finalValidation.overallScore.toFixed(1)}%`);
  
  const improvement = finalValidation.overallScore - initialValidation.overallScore;
  console.log(`Improvement: ${improvement > 0 ? '+' : ''}${improvement.toFixed(1)}%\n`);

  return {
    initialScore: initialValidation.overallScore,
    finalScore: finalValidation.overallScore,
    improvement,
    totalChanges: remediationResult.summary.totalReplacements + 
                  standardizationReplacements.length + 
                  stateVariantReplacements.length + 
                  interactionReplacements.length
  };
}

// Export all examples for easy usage
export const componentRemediationExamples = {
  basicComponentRemediationExample,
  componentStandardizationExample,
  stateAndVariantCorrectionExample,
  interactionPatternStandardizationExample,
  componentComplianceValidationExample,
  customComponentStandardExample,
  completeRemediationWorkflowExample
};

// Example usage - uncomment to run when executing this file directly
/*
(async () => {
  try {
    await basicComponentRemediationExample();
    await componentStandardizationExample();
    await stateAndVariantCorrectionExample();
    await interactionPatternStandardizationExample();
    await componentComplianceValidationExample();
    await customComponentStandardExample();
    await completeRemediationWorkflowExample();
  } catch (error) {
    console.error('Example execution failed:', error);
  }
})();
*/