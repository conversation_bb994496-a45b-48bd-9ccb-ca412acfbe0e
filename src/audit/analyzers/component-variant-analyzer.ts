// Component variant analyzer for detecting inconsistent component styling patterns and mixed state implementations

import { 
  <PERSON><PERSON><PERSON>, 
  AnalyzerResult, 
  FileInfo, 
  Issue, 
  IssueType, 
  Severity, 
  Category, 
  FileLocation, 
  ImpactAssessment 
} from '../types';
import { auditLogger } from '../core/logger';

interface ComponentVariant {
  name: string;
  props: string[];
  stylingApproach: 'css-modules' | 'styled-components' | 'tailwind' | 'inline-styles' | 'css-in-js' | 'mixed';
  stateHandling: 'hooks' | 'class-state' | 'props-only' | 'mixed';
  location: FileLocation;
}

interface ComponentUsage {
  componentName: string;
  propsUsed: string[];
  variantPattern: string;
  location: FileLocation;
}

interface StylingPattern {
  type: string;
  frequency: number;
  files: string[];
  examples: string[];
}

export class ComponentVariantAnalyzer implements Analyzer {
  name = 'component-variant-analyzer';

  // Patterns to detect different styling approaches
  private stylingPatterns = {
    cssModules: /import\s+styles\s+from\s+['"`][^'"`]*\.module\.css['"`]/,
    styledComponents: /styled\.[a-zA-Z]+`|styled\([^)]+\)`|from\s+['"`]styled-components['"`]/,
    tailwind: /className\s*=\s*['"`][^'"`]*(?:bg-|text-|p-|m-|flex|grid)[^'"`]*['"`]/,
    inlineStyles: /style\s*=\s*\{\{[^}]+\}\}/,
    cssInJs: /css`|css\([^)]*\)|makeStyles|useStyles/
  };

  // Patterns to detect state handling approaches
  private statePatterns = {
    hooks: /useState|useEffect|useReducer|useContext/g,
    classState: /this\.state|this\.setState/g,
    propsOnly: /^(?!.*useState|.*useEffect|.*this\.state).*function\s+\w+\s*\([^)]*\)|const\s+\w+\s*=\s*\([^)]*\)\s*=>/g
  };

  // Common component prop patterns
  private propPatterns = {
    variant: /variant\s*[:=]\s*['"`]([^'"`]+)['"`]/g,
    size: /size\s*[:=]\s*['"`]([^'"`]+)['"`]/g,
    color: /color\s*[:=]\s*['"`]([^'"`]+)['"`]/g,
    disabled: /disabled\s*[:=]\s*(true|false|\{[^}]+\})/g,
    loading: /loading\s*[:=]\s*(true|false|\{[^}]+\})/g,
    className: /className\s*[:=]\s*['"`]([^'"`]+)['"`]|\{[^}]+\}/g
  };

  async analyze(files: FileInfo[]): Promise<AnalyzerResult> {
    const startTime = Date.now();
    auditLogger.info('Starting component variant analysis', { fileCount: files.length });

    const issues: Issue[] = [];
    const componentVariants = new Map<string, ComponentVariant[]>();
    const componentUsages = new Map<string, ComponentUsage[]>();
    const stylingPatternStats = new Map<string, StylingPattern>();

    // Filter React component files
    const componentFiles = files.filter(file => 
      ['.tsx', '.jsx'].includes(file.extension) &&
      !file.path.includes('test') &&
      !file.path.includes('spec') &&
      !file.path.includes('stories')
    );

    for (const file of componentFiles) {
      try {
        const fileAnalysis = await this.analyzeComponentFile(file);
        
        // Collect component variants
        fileAnalysis.variants.forEach(variant => {
          if (!componentVariants.has(variant.name)) {
            componentVariants.set(variant.name, []);
          }
          componentVariants.get(variant.name)!.push(variant);
        });

        // Collect component usages
        fileAnalysis.usages.forEach(usage => {
          if (!componentUsages.has(usage.componentName)) {
            componentUsages.set(usage.componentName, []);
          }
          componentUsages.get(usage.componentName)!.push(usage);
        });

        // Track styling patterns
        this.updateStylingPatternStats(file, fileAnalysis.stylingApproaches, stylingPatternStats);

      } catch (error) {
        auditLogger.error('Failed to analyze component file', error as Error, { 
          filePath: file.path 
        });
      }
    }

    // Analyze patterns and generate issues
    const variantIssues = this.analyzeVariantConsistency(componentVariants);
    const usageIssues = this.analyzeUsagePatterns(componentUsages);
    const stylingIssues = this.analyzeStylingConsistency(stylingPatternStats);

    issues.push(...variantIssues, ...usageIssues, ...stylingIssues);

    const executionTime = Math.max(1, Date.now() - startTime); // Ensure minimum 1ms
    
    const result: AnalyzerResult = {
      analyzerName: this.name,
      issues,
      summary: {
        totalIssues: issues.length,
        severityBreakdown: this.calculateSeverityBreakdown(issues),
        categoryBreakdown: this.calculateCategoryBreakdown(issues)
      },
      executionTime
    };

    auditLogger.info('Component variant analysis completed', { 
      issuesFound: issues.length,
      executionTime 
    });

    return result;
  }

  private async analyzeComponentFile(file: FileInfo): Promise<{
    variants: ComponentVariant[];
    usages: ComponentUsage[];
    stylingApproaches: string[];
  }> {
    const variants: ComponentVariant[] = [];
    const usages: ComponentUsage[] = [];
    const stylingApproaches: string[] = [];

    const lines = file.content.split('\n');
    
    // Detect styling approaches used in this file
    const detectedStyling = this.detectStylingApproaches(file.content);
    stylingApproaches.push(...detectedStyling);

    // Find component definitions
    const componentDefinitions = this.findComponentDefinitions(file.content, file.path);
    variants.push(...componentDefinitions);

    // Find component usages
    const componentUsagePatterns = this.findComponentUsages(file.content, file.path);
    usages.push(...componentUsagePatterns);

    return { variants, usages, stylingApproaches };
  }

  private detectStylingApproaches(content: string): string[] {
    const approaches: string[] = [];

    Object.entries(this.stylingPatterns).forEach(([approach, pattern]) => {
      if (pattern.test(content)) {
        approaches.push(approach);
      }
    });

    return approaches;
  }

  private findComponentDefinitions(content: string, filePath: string): ComponentVariant[] {
    const variants: ComponentVariant[] = [];
    const lines = content.split('\n');

    // Patterns to match component definitions
    const componentPatterns = [
      /^(?:export\s+)?(?:const|function)\s+([A-Z][a-zA-Z0-9]*)\s*[=:]/,
      /^(?:export\s+)?(?:default\s+)?function\s+([A-Z][a-zA-Z0-9]*)/,
      /^(?:export\s+)?class\s+([A-Z][a-zA-Z0-9]*)/
    ];

    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex];
      const lineNumber = lineIndex + 1;

      for (const pattern of componentPatterns) {
        const match = line.match(pattern);
        if (match) {
          const componentName = match[1];
          
          // Analyze the component's props and styling
          const componentCode = this.extractComponentCode(lines, lineIndex);
          const props = this.extractComponentProps(componentCode);
          const stylingApproach = this.determineStylingApproach(componentCode);
          const stateHandling = this.determineStateHandling(componentCode);

          variants.push({
            name: componentName,
            props,
            stylingApproach,
            stateHandling,
            location: {
              filePath,
              lineNumber,
              columnNumber: match.index || 0,
              context: line.trim()
            }
          });
        }
      }
    }

    return variants;
  }

  private findComponentUsages(content: string, filePath: string): ComponentUsage[] {
    const usages: ComponentUsage[] = [];
    const lines = content.split('\n');

    // Pattern to match JSX component usage
    const jsxPattern = /<([A-Z][a-zA-Z0-9]*)[^>]*>/g;

    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex];
      const lineNumber = lineIndex + 1;

      let match;
      while ((match = jsxPattern.exec(line)) !== null) {
        const componentName = match[1];
        const fullMatch = match[0];
        
        // Extract props from the JSX usage
        const propsUsed = this.extractUsageProps(fullMatch);
        const variantPattern = this.determineVariantPattern(propsUsed);

        usages.push({
          componentName,
          propsUsed,
          variantPattern,
          location: {
            filePath,
            lineNumber,
            columnNumber: match.index,
            context: line.trim()
          }
        });
      }
    }

    return usages;
  }

  private extractComponentCode(lines: string[], startIndex: number): string {
    // Extract component code by finding matching braces or return statement
    let braceCount = 0;
    let endIndex = startIndex;
    let foundOpenBrace = false;

    for (let i = startIndex; i < lines.length; i++) {
      const line = lines[i];
      
      for (const char of line) {
        if (char === '{') {
          braceCount++;
          foundOpenBrace = true;
        } else if (char === '}') {
          braceCount--;
          if (foundOpenBrace && braceCount === 0) {
            endIndex = i;
            break;
          }
        }
      }
      
      if (foundOpenBrace && braceCount === 0) {
        break;
      }
      
      // Limit extraction to reasonable component size
      if (i - startIndex > 100) {
        endIndex = i;
        break;
      }
    }

    return lines.slice(startIndex, endIndex + 1).join('\n');
  }

  private extractComponentProps(componentCode: string): string[] {
    const props: string[] = [];
    
    // Extract props from function parameters
    const functionParamMatch = componentCode.match(/\(([^)]*)\)/);
    if (functionParamMatch) {
      const params = functionParamMatch[1];
      
      // Handle destructured props
      const destructuredMatch = params.match(/\{\s*([^}]+)\s*\}/);
      if (destructuredMatch) {
        const propsList = destructuredMatch[1]
          .split(',')
          .map(prop => prop.trim().split(':')[0].trim())
          .filter(prop => prop && !prop.includes('...'));
        props.push(...propsList);
      }
    }

    // Also look for prop usage patterns in the component body
    Object.keys(this.propPatterns).forEach(propName => {
      if (componentCode.includes(propName)) {
        props.push(propName);
      }
    });

    return [...new Set(props)]; // Remove duplicates
  }

  private determineStylingApproach(componentCode: string): ComponentVariant['stylingApproach'] {
    const approaches: string[] = [];

    if (this.stylingPatterns.cssModules.test(componentCode)) approaches.push('css-modules');
    if (this.stylingPatterns.styledComponents.test(componentCode)) approaches.push('styled-components');
    if (this.stylingPatterns.tailwind.test(componentCode)) approaches.push('tailwind');
    if (this.stylingPatterns.inlineStyles.test(componentCode)) approaches.push('inline-styles');
    if (this.stylingPatterns.cssInJs.test(componentCode)) approaches.push('css-in-js');

    if (approaches.length === 0) return 'css-modules'; // Default assumption
    if (approaches.length === 1) return approaches[0] as ComponentVariant['stylingApproach'];
    return 'mixed';
  }

  private determineStateHandling(componentCode: string): ComponentVariant['stateHandling'] {
    const approaches: string[] = [];

    if (this.statePatterns.hooks.test(componentCode)) approaches.push('hooks');
    if (this.statePatterns.classState.test(componentCode)) approaches.push('class-state');
    if (!approaches.length && this.statePatterns.propsOnly.test(componentCode)) approaches.push('props-only');

    if (approaches.length === 0) return 'props-only';
    if (approaches.length === 1) return approaches[0] as ComponentVariant['stateHandling'];
    return 'mixed';
  }

  private extractUsageProps(jsxString: string): string[] {
    const props: string[] = [];
    
    // Extract prop names from JSX attributes
    const propPattern = /(\w+)(?:\s*=\s*(?:['"`][^'"`]*['"`]|\{[^}]*\}))?/g;
    let match;
    
    while ((match = propPattern.exec(jsxString)) !== null) {
      const propName = match[1];
      if (propName !== 'className' || !props.includes(propName)) {
        props.push(propName);
      }
    }

    return props;
  }

  private determineVariantPattern(props: string[]): string {
    // Determine the variant pattern based on props used
    const variantProps = props.filter(prop => 
      ['variant', 'size', 'color', 'type', 'appearance'].includes(prop)
    );
    
    if (variantProps.length === 0) return 'default';
    return variantProps.sort().join('-');
  }

  private updateStylingPatternStats(
    file: FileInfo, 
    approaches: string[], 
    stats: Map<string, StylingPattern>
  ): void {
    approaches.forEach(approach => {
      if (!stats.has(approach)) {
        stats.set(approach, {
          type: approach,
          frequency: 0,
          files: [],
          examples: []
        });
      }
      
      const pattern = stats.get(approach)!;
      pattern.frequency++;
      if (!pattern.files.includes(file.path)) {
        pattern.files.push(file.path);
      }
    });
  }

  private analyzeVariantConsistency(componentVariants: Map<string, ComponentVariant[]>): Issue[] {
    const issues: Issue[] = [];

    componentVariants.forEach((variants, componentName) => {
      if (variants.length <= 1) return;

      // Check for inconsistent styling approaches within the same component
      const stylingApproaches = new Set(variants.map(v => v.stylingApproach));
      if (stylingApproaches.size > 1) {
        const issue: Issue = {
          id: `component_styling_inconsistency_${componentName}`,
          type: IssueType.COMPONENT_INCONSISTENCY,
          severity: Severity.HIGH,
          category: Category.COMPONENT,
          description: `Component '${componentName}' uses inconsistent styling approaches: ${Array.from(stylingApproaches).join(', ')}`,
          location: variants[0].location,
          suggestion: `Standardize '${componentName}' to use a single styling approach across all variants`,
          autoFixable: false,
          impact: {
            userExperience: 6,
            maintenanceEffort: 8,
            implementationComplexity: 7
          }
        };
        issues.push(issue);
      }

      // Check for inconsistent state handling
      const stateHandling = new Set(variants.map(v => v.stateHandling));
      if (stateHandling.size > 1 && stateHandling.has('mixed')) {
        const issue: Issue = {
          id: `component_state_inconsistency_${componentName}`,
          type: IssueType.COMPONENT_INCONSISTENCY,
          severity: Severity.MEDIUM,
          category: Category.COMPONENT,
          description: `Component '${componentName}' uses mixed state handling approaches`,
          location: variants[0].location,
          suggestion: `Standardize state handling in '${componentName}' to use consistent patterns`,
          autoFixable: false,
          impact: {
            userExperience: 4,
            maintenanceEffort: 7,
            implementationComplexity: 6
          }
        };
        issues.push(issue);
      }

      // Check for inconsistent prop patterns
      const allProps = variants.flatMap(v => v.props);
      const propFrequency = new Map<string, number>();
      allProps.forEach(prop => {
        propFrequency.set(prop, (propFrequency.get(prop) || 0) + 1);
      });

      // Find props that are used inconsistently across variants
      propFrequency.forEach((count, prop) => {
        if (count < variants.length && count > 1) {
          const issue: Issue = {
            id: `component_prop_inconsistency_${componentName}_${prop}`,
            type: IssueType.COMPONENT_INCONSISTENCY,
            severity: Severity.MEDIUM,
            category: Category.COMPONENT,
            description: `Prop '${prop}' is used inconsistently across '${componentName}' variants (${count}/${variants.length} variants)`,
            location: variants[0].location,
            suggestion: `Ensure '${prop}' prop is consistently available across all '${componentName}' variants`,
            autoFixable: false,
            impact: {
              userExperience: 5,
              maintenanceEffort: 6,
              implementationComplexity: 4
            }
          };
          issues.push(issue);
        }
      });
    });

    return issues;
  }

  private analyzeUsagePatterns(componentUsages: Map<string, ComponentUsage[]>): Issue[] {
    const issues: Issue[] = [];

    componentUsages.forEach((usages, componentName) => {
      if (usages.length <= 2) return;

      // Analyze variant pattern consistency
      const variantPatterns = new Map<string, number>();
      usages.forEach(usage => {
        const pattern = usage.variantPattern;
        variantPatterns.set(pattern, (variantPatterns.get(pattern) || 0) + 1);
      });

      // Check for too many different variant patterns (might indicate inconsistency)
      if (variantPatterns.size > Math.ceil(usages.length / 3)) {
        const issue: Issue = {
          id: `component_usage_inconsistency_${componentName}`,
          type: IssueType.COMPONENT_INCONSISTENCY,
          severity: Severity.MEDIUM,
          category: Category.COMPONENT,
          description: `Component '${componentName}' has ${variantPatterns.size} different usage patterns across ${usages.length} instances`,
          location: usages[0].location,
          suggestion: `Review and standardize usage patterns for '${componentName}' component`,
          autoFixable: false,
          impact: {
            userExperience: 4,
            maintenanceEffort: 6,
            implementationComplexity: 5
          }
        };
        issues.push(issue);
      }

      // Check for missing common props
      const allPropsUsed = usages.flatMap(u => u.propsUsed);
      const propFrequency = new Map<string, number>();
      allPropsUsed.forEach(prop => {
        propFrequency.set(prop, (propFrequency.get(prop) || 0) + 1);
      });

      // Find props that are commonly used but not universally
      propFrequency.forEach((count, prop) => {
        const usagePercentage = count / usages.length;
        if (usagePercentage > 0.7 && usagePercentage < 1.0) {
          const issue: Issue = {
            id: `component_missing_prop_${componentName}_${prop}`,
            type: IssueType.COMPONENT_INCONSISTENCY,
            severity: Severity.LOW,
            category: Category.COMPONENT,
            description: `Prop '${prop}' is used in ${Math.round(usagePercentage * 100)}% of '${componentName}' instances but not all`,
            location: usages[0].location,
            suggestion: `Consider making '${prop}' a required prop or provide a default value for '${componentName}'`,
            autoFixable: false,
            impact: {
              userExperience: 3,
              maintenanceEffort: 4,
              implementationComplexity: 3
            }
          };
          issues.push(issue);
        }
      });
    });

    return issues;
  }

  private analyzeStylingConsistency(stylingPatternStats: Map<string, StylingPattern>): Issue[] {
    const issues: Issue[] = [];

    // Check for mixed styling approaches across the application
    const approaches = Array.from(stylingPatternStats.keys());
    if (approaches.length > 2) {
      const totalFiles = new Set(
        Array.from(stylingPatternStats.values()).flatMap(pattern => pattern.files)
      ).size;

      const issue: Issue = {
        id: 'mixed_styling_approaches',
        type: IssueType.COMPONENT_INCONSISTENCY,
        severity: Severity.HIGH,
        category: Category.COMPONENT,
        description: `Application uses ${approaches.length} different styling approaches: ${approaches.join(', ')}`,
        location: {
          filePath: 'project-wide',
          lineNumber: 0,
          columnNumber: 0,
          context: `${totalFiles} files affected`
        },
        suggestion: 'Standardize on 1-2 primary styling approaches for consistency',
        autoFixable: false,
        impact: {
          userExperience: 5,
          maintenanceEffort: 9,
          implementationComplexity: 8
        }
      };
      issues.push(issue);
    }

    // Check for underutilized styling approaches
    if (stylingPatternStats.size > 1) {
      const totalFrequency = Array.from(stylingPatternStats.values())
        .reduce((sum, pattern) => sum + pattern.frequency, 0);

      stylingPatternStats.forEach((pattern, approach) => {
        const usagePercentage = pattern.frequency / totalFrequency;
        // Lower threshold for underutilization detection
        if (usagePercentage < 0.4 && pattern.frequency >= 1 && totalFrequency > 2) {
          const issue: Issue = {
            id: `underutilized_styling_${approach}`,
            type: IssueType.COMPONENT_INCONSISTENCY,
            severity: Severity.MEDIUM,
            category: Category.COMPONENT,
            description: `Styling approach '${approach}' is underutilized (${Math.round(usagePercentage * 100)}% of usage)`,
            location: {
              filePath: pattern.files[0],
              lineNumber: 0,
              columnNumber: 0,
              context: `Used in ${pattern.files.length} files`
            },
            suggestion: `Consider consolidating '${approach}' usage or migrating to a more prevalent styling approach`,
            autoFixable: false,
            impact: {
              userExperience: 2,
              maintenanceEffort: 6,
              implementationComplexity: 5
            }
          };
          issues.push(issue);
        }
      });
    }

    return issues;
  }

  getSeverity(issue: Omit<Issue, 'severity'>): Severity {
    if (issue.type === IssueType.COMPONENT_INCONSISTENCY) {
      if (issue.description.includes('mixed styling approaches')) {
        return Severity.HIGH;
      }
      if (issue.description.includes('inconsistent styling approaches')) {
        return Severity.HIGH;
      }
      if (issue.description.includes('usage patterns')) {
        return Severity.MEDIUM;
      }
      if (issue.description.includes('missing prop')) {
        return Severity.LOW;
      }
    }
    
    return Severity.MEDIUM;
  }

  private calculateSeverityBreakdown(issues: Issue[]): Record<Severity, number> {
    const breakdown: Record<Severity, number> = {
      [Severity.LOW]: 0,
      [Severity.MEDIUM]: 0,
      [Severity.HIGH]: 0,
      [Severity.CRITICAL]: 0
    };

    issues.forEach(issue => {
      breakdown[issue.severity]++;
    });

    return breakdown;
  }

  private calculateCategoryBreakdown(issues: Issue[]): Record<Category, number> {
    const breakdown: Record<Category, number> = {
      [Category.VISUAL]: 0,
      [Category.CONTENT]: 0,
      [Category.COMPONENT]: 0,
      [Category.ACCESSIBILITY]: 0
    };

    issues.forEach(issue => {
      breakdown[issue.category]++;
    });

    return breakdown;
  }
}