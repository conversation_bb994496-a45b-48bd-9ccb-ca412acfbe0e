// Content standardization tools for automated content revision and terminology standardization

import { 
  FileInfo, 
  Issue, 
  IssueType, 
  Severity, 
  Category, 
  FileLocation 
} from '../types';
import { auditLogger } from '../core/logger';

export interface ContentRevision {
  original: string;
  revised: string;
  reason: string;
  confidence: number; // 0-1 scale
  type: 'tone' | 'terminology' | 'messaging' | 'grammar';
}

export interface TerminologyStandardization {
  term: string;
  standardTerm: string;
  category: 'technical' | 'business' | 'ui' | 'action';
  context: string;
  locations: FileLocation[];
}

export interface MessagingPattern {
  type: 'error' | 'success' | 'warning' | 'info' | 'validation';
  pattern: string;
  template: string;
  examples: string[];
}

export class ContentStandardizationTools {
  // Tone guidelines for different content types
  private toneGuidelines = {
    error_message: {
      tone: 'helpful',
      formality: 'formal',
      sentiment: 'neutral',
      guidelines: [
        'Be clear and specific about what went wrong',
        'Provide actionable steps to resolve the issue',
        'Avoid blame or negative language',
        'Use "please" and "try" to soften the message'
      ]
    },
    success_message: {
      tone: 'positive',
      formality: 'friendly',
      sentiment: 'positive',
      guidelines: [
        'Acknowledge the user\'s success',
        'Be encouraging but not overly enthusiastic',
        'Provide next steps if applicable'
      ]
    },
    help_text: {
      tone: 'instructional',
      formality: 'formal',
      sentiment: 'neutral',
      guidelines: [
        'Be concise and clear',
        'Use active voice',
        'Provide specific examples when helpful'
      ]
    },
    button_text: {
      tone: 'action-oriented',
      formality: 'informal',
      sentiment: 'neutral',
      guidelines: [
        'Use action verbs',
        'Be concise (1-3 words)',
        'Make the action clear and specific'
      ]
    }
  };

  // Standard terminology dictionary with preferred terms
  private terminologyStandards = {
    technical: {
      'log in': {
        preferred: 'log in',
        alternatives: ['login', 'sign in', 'sign-in', 'signin'],
        context: 'Use "log in" as a verb, "login" as a noun'
      },
      'log out': {
        preferred: 'log out',
        alternatives: ['logout', 'sign out', 'sign-out', 'signout'],
        context: 'Use "log out" as a verb, "logout" as a noun'
      },
      'username': {
        preferred: 'username',
        alternatives: ['user name', 'user-name', 'login name'],
        context: 'Single word, lowercase'
      },
      'email address': {
        preferred: 'email address',
        alternatives: ['email', 'e-mail', 'e-mail address'],
        context: 'Use full term for clarity'
      },
      'website': {
        preferred: 'website',
        alternatives: ['web site', 'web-site', 'site'],
        context: 'Single word, lowercase'
      }
    },
    business: {
      'customer': {
        preferred: 'customer',
        alternatives: ['client', 'user', 'member'],
        context: 'Use consistently throughout the application'
      },
      'purchase': {
        preferred: 'purchase',
        alternatives: ['buy', 'order', 'transaction'],
        context: 'Use for the act of buying'
      },
      'subscription': {
        preferred: 'subscription',
        alternatives: ['plan', 'membership', 'service'],
        context: 'Use for recurring services'
      }
    },
    ui: {
      'button': {
        preferred: 'button',
        alternatives: ['btn', 'link', 'action'],
        context: 'Use "button" in user-facing text'
      },
      'dropdown': {
        preferred: 'dropdown',
        alternatives: ['select', 'picker', 'menu'],
        context: 'Single word, lowercase'
      },
      'checkbox': {
        preferred: 'checkbox',
        alternatives: ['check box', 'tick box'],
        context: 'Single word, lowercase'
      }
    },
    action: {
      'click': {
        preferred: 'click',
        alternatives: ['press', 'tap', 'select'],
        context: 'Use "click" for desktop, "tap" for mobile'
      },
      'submit': {
        preferred: 'submit',
        alternatives: ['send', 'save', 'confirm'],
        context: 'Use for form submissions'
      },
      'delete': {
        preferred: 'delete',
        alternatives: ['remove', 'erase', 'clear'],
        context: 'Use "delete" for permanent removal'
      }
    }
  };

  // Standard messaging patterns and templates
  private messagingPatterns: Record<MessagingPattern['type'], MessagingPattern> = {
    error: {
      type: 'error',
      pattern: 'Error: {description}. Please {action} and try again.',
      template: 'Error: {description}. Please {action} and try again.',
      examples: [
        'Error: Invalid email address. Please check your email and try again.',
        'Error: Password is too short. Please use at least 8 characters and try again.',
        'Error: Connection failed. Please check your internet connection and try again.'
      ]
    },
    success: {
      type: 'success',
      pattern: '{action} successful! {next_step}',
      template: '{action} successful! {next_step}',
      examples: [
        'Account created successfully! Please check your email to verify your account.',
        'Password updated successfully! You can now log in with your new password.',
        'File uploaded successfully! It will be processed shortly.'
      ]
    },
    warning: {
      type: 'warning',
      pattern: 'Warning: {description}. {recommendation}',
      template: 'Warning: {description}. {recommendation}',
      examples: [
        'Warning: Your session will expire in 5 minutes. Please save your work.',
        'Warning: This action cannot be undone. Please confirm to continue.',
        'Warning: File size is large. Upload may take several minutes.'
      ]
    },
    info: {
      type: 'info',
      pattern: 'Info: {description}',
      template: 'Info: {description}',
      examples: [
        'Info: Your changes have been saved automatically.',
        'Info: New features are available in the settings menu.',
        'Info: Maintenance is scheduled for tonight at 2 AM EST.'
      ]
    },
    validation: {
      type: 'validation',
      pattern: '{field} {requirement}',
      template: '{field} {requirement}',
      examples: [
        'Email address is required.',
        'Password must be at least 8 characters.',
        'Phone number format is invalid.'
      ]
    }
  };

  /**
   * Generate content revision suggestions based on tone guidelines
   */
  generateContentRevisions(text: string, contentType: keyof typeof this.toneGuidelines): ContentRevision[] {
    const revisions: ContentRevision[] = [];
    const guidelines = this.toneGuidelines[contentType];
    
    if (!guidelines) {
      return revisions;
    }

    // Check for tone violations and suggest improvements
    const toneRevisions = this.analyzeToneViolations(text, guidelines);
    revisions.push(...toneRevisions);

    // Check for grammar and style improvements
    const grammarRevisions = this.analyzeGrammarIssues(text);
    revisions.push(...grammarRevisions);

    // Check for messaging pattern compliance
    const messagingRevisions = this.analyzeMessagingPatterns(text, contentType);
    revisions.push(...messagingRevisions);

    return revisions.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Standardize terminology in text content
   */
  standardizeTerminology(text: string): TerminologyStandardization[] {
    const standardizations: TerminologyStandardization[] = [];

    Object.entries(this.terminologyStandards).forEach(([category, terms]) => {
      Object.entries(terms).forEach(([preferred, config]) => {
        config.alternatives.forEach(alternative => {
          const regex = new RegExp(`\\b${this.escapeRegex(alternative)}\\b`, 'gi');
          const matches = text.match(regex);
          
          if (matches && matches.length > 0) {
            standardizations.push({
              term: alternative,
              standardTerm: preferred,
              category: category as TerminologyStandardization['category'],
              context: config.context,
              locations: [] // Will be populated by the caller with actual file locations
            });
          }
        });
      });
    });

    return standardizations;
  }

  /**
   * Enforce consistent messaging patterns
   */
  enforceMessagingPatterns(text: string, messageType: MessagingPattern['type']): ContentRevision[] {
    const revisions: ContentRevision[] = [];
    const pattern = this.messagingPatterns[messageType];
    
    if (!pattern) {
      return revisions;
    }

    // Check if the text follows the standard pattern
    const followsPattern = this.checkPatternCompliance(text, pattern);
    
    if (!followsPattern) {
      const suggestedRevision = this.generatePatternCompliantMessage(text, pattern);
      
      if (suggestedRevision) {
        revisions.push({
          original: text,
          revised: suggestedRevision,
          reason: `Message doesn't follow standard ${messageType} pattern. Consider using: ${pattern.template}`,
          confidence: 0.8,
          type: 'messaging'
        });
      }
    }

    return revisions;
  }

  /**
   * Apply automatic fixes to content based on standardization rules
   */
  applyAutomaticFixes(text: string, contentType: keyof typeof this.toneGuidelines): string {
    let fixedText = text;

    // Apply terminology standardization
    const terminologyFixes = this.standardizeTerminology(text);
    terminologyFixes.forEach(fix => {
      const regex = new RegExp(`\\b${this.escapeRegex(fix.term)}\\b`, 'gi');
      fixedText = fixedText.replace(regex, fix.standardTerm);
    });

    // Apply basic grammar fixes
    fixedText = this.applyBasicGrammarFixes(fixedText);

    // Apply tone adjustments
    fixedText = this.applyToneAdjustments(fixedText, contentType);

    return fixedText;
  }

  /**
   * Validate content against all standardization rules
   */
  validateContent(text: string, contentType: keyof typeof this.toneGuidelines): Issue[] {
    const issues: Issue[] = [];

    // Check terminology compliance
    const terminologyIssues = this.validateTerminology(text);
    issues.push(...terminologyIssues);

    // Check tone compliance
    const toneIssues = this.validateTone(text, contentType);
    issues.push(...toneIssues);

    // Check messaging pattern compliance
    const messagingIssues = this.validateMessagingPatterns(text, contentType);
    issues.push(...messagingIssues);

    return issues;
  }

  private analyzeToneViolations(text: string, guidelines: any): ContentRevision[] {
    const revisions: ContentRevision[] = [];
    const lowerText = text.toLowerCase();

    // Check for inappropriate tone words
    const negativeWords = ['terrible', 'awful', 'horrible', 'stupid', 'dumb', 'idiotic'];
    const foundNegative = negativeWords.filter(word => lowerText.includes(word));

    if (foundNegative.length > 0 && guidelines.sentiment === 'neutral') {
      const revisedText = this.replaceNegativeWords(text, foundNegative);
      revisions.push({
        original: text,
        revised: revisedText,
        reason: 'Contains inappropriate negative language for neutral tone',
        confidence: 0.9,
        type: 'tone'
      });
    }

    // Check for overly casual language in formal contexts
    const casualWords = ['hey', 'gonna', 'wanna', 'yeah', 'nope'];
    const foundCasual = casualWords.filter(word => lowerText.includes(word));

    if (foundCasual.length > 0 && guidelines.formality === 'formal') {
      const revisedText = this.replaceCasualWords(text, foundCasual);
      revisions.push({
        original: text,
        revised: revisedText,
        reason: 'Contains casual language inappropriate for formal tone',
        confidence: 0.8,
        type: 'tone'
      });
    }

    return revisions;
  }

  private analyzeGrammarIssues(text: string): ContentRevision[] {
    const revisions: ContentRevision[] = [];

    // Check for common grammar issues
    const grammarFixes = [
      { pattern: /\bi\b/g, replacement: 'I', reason: 'Capitalize pronoun "I"' },
      { pattern: /\s+/g, replacement: ' ', reason: 'Remove extra whitespace' },
      { pattern: /\.{2,}/g, replacement: '.', reason: 'Use single period' },
      { pattern: /\?{2,}/g, replacement: '?', reason: 'Use single question mark' },
      { pattern: /!{2,}/g, replacement: '!', reason: 'Use single exclamation mark' }
    ];

    grammarFixes.forEach(fix => {
      if (fix.pattern.test(text)) {
        const revisedText = text.replace(fix.pattern, fix.replacement);
        if (revisedText !== text) {
          revisions.push({
            original: text,
            revised: revisedText,
            reason: fix.reason,
            confidence: 0.7,
            type: 'grammar'
          });
        }
      }
    });

    return revisions;
  }

  private analyzeMessagingPatterns(text: string, contentType: string): ContentRevision[] {
    const revisions: ContentRevision[] = [];

    // Map content types to message types
    const messageTypeMap: Record<string, MessagingPattern['type']> = {
      'error_message': 'error',
      'success_message': 'success',
      'help_text': 'info'
    };

    const messageType = messageTypeMap[contentType];
    if (messageType) {
      const patternRevisions = this.enforceMessagingPatterns(text, messageType);
      revisions.push(...patternRevisions);
    }

    return revisions;
  }

  private checkPatternCompliance(text: string, pattern: MessagingPattern): boolean {
    // Simple pattern matching - in a real implementation, this would be more sophisticated
    const patternKeywords = pattern.pattern.toLowerCase().split(/[{}]/);
    const textLower = text.toLowerCase();

    // Check if text contains key elements of the pattern
    return patternKeywords.some(keyword => 
      keyword.length > 2 && textLower.includes(keyword.trim())
    );
  }

  private generatePatternCompliantMessage(text: string, pattern: MessagingPattern): string | null {
    // This is a simplified implementation - in practice, this would use NLP
    // to extract key information and restructure the message
    
    if (pattern.type === 'error' && !text.toLowerCase().startsWith('error:')) {
      return `Error: ${text}. Please check and try again.`;
    }
    
    if (pattern.type === 'success' && !text.toLowerCase().includes('successful')) {
      return `${text} successful!`;
    }

    return null;
  }

  private replaceNegativeWords(text: string, negativeWords: string[]): string {
    let result = text;
    const replacements: Record<string, string> = {
      'terrible': 'problematic',
      'awful': 'incorrect',
      'horrible': 'invalid',
      'stupid': 'incorrect',
      'dumb': 'invalid',
      'idiotic': 'incorrect'
    };

    negativeWords.forEach(word => {
      const replacement = replacements[word] || 'incorrect';
      const regex = new RegExp(`\\b${word}\\b`, 'gi');
      result = result.replace(regex, replacement);
    });

    return result;
  }

  private replaceCasualWords(text: string, casualWords: string[]): string {
    let result = text;
    const replacements: Record<string, string> = {
      'hey': 'hello',
      'gonna': 'going to',
      'wanna': 'want to',
      'yeah': 'yes',
      'nope': 'no'
    };

    casualWords.forEach(word => {
      const replacement = replacements[word] || word;
      const regex = new RegExp(`\\b${word}\\b`, 'gi');
      result = result.replace(regex, replacement);
    });

    return result;
  }

  private applyBasicGrammarFixes(text: string): string {
    return text
      .replace(/\bi\b/g, 'I') // Capitalize "I"
      .replace(/\s+/g, ' ') // Remove extra whitespace
      .replace(/\.{2,}/g, '.') // Single periods
      .replace(/\?{2,}/g, '?') // Single question marks
      .replace(/!{2,}/g, '!') // Single exclamation marks
      .trim();
  }

  private applyToneAdjustments(text: string, contentType: keyof typeof this.toneGuidelines): string {
    const guidelines = this.toneGuidelines[contentType];
    if (!guidelines) return text;

    let adjustedText = text;

    // Add "please" to error messages for politeness
    if (contentType === 'error_message' && !text.toLowerCase().includes('please')) {
      adjustedText = adjustedText.replace(/\. Try/, '. Please try');
      adjustedText = adjustedText.replace(/\. Check/, '. Please check');
    }

    return adjustedText;
  }

  private validateTerminology(text: string): Issue[] {
    const issues: Issue[] = [];
    const standardizations = this.standardizeTerminology(text);

    standardizations.forEach(std => {
      issues.push({
        id: `terminology_${std.term}_${Date.now()}`,
        type: IssueType.CONTENT_INCONSISTENCY,
        severity: Severity.MEDIUM,
        category: Category.CONTENT,
        description: `Non-standard terminology: "${std.term}" should be "${std.standardTerm}"`,
        location: {
          filePath: 'content',
          lineNumber: 0,
          columnNumber: 0,
          context: text
        },
        suggestion: `Replace "${std.term}" with "${std.standardTerm}". ${std.context}`,
        autoFixable: true,
        impact: {
          userExperience: 5,
          maintenanceEffort: 3,
          implementationComplexity: 2
        }
      });
    });

    return issues;
  }

  private validateTone(text: string, contentType: keyof typeof this.toneGuidelines): Issue[] {
    const issues: Issue[] = [];
    const guidelines = this.toneGuidelines[contentType];
    
    if (!guidelines) return issues;

    const revisions = this.analyzeToneViolations(text, guidelines);
    
    revisions.forEach(revision => {
      issues.push({
        id: `tone_${contentType}_${Date.now()}`,
        type: IssueType.CONTENT_INCONSISTENCY,
        severity: Severity.MEDIUM,
        category: Category.CONTENT,
        description: `Tone violation in ${contentType}: ${revision.reason}`,
        location: {
          filePath: 'content',
          lineNumber: 0,
          columnNumber: 0,
          context: text
        },
        suggestion: `Consider revising to: "${revision.revised}"`,
        autoFixable: true,
        impact: {
          userExperience: 6,
          maintenanceEffort: 4,
          implementationComplexity: 3
        }
      });
    });

    return issues;
  }

  private validateMessagingPatterns(text: string, contentType: keyof typeof this.toneGuidelines): Issue[] {
    const issues: Issue[] = [];
    
    // Don't validate empty text
    if (!text.trim()) return issues;
    
    const messageTypeMap: Record<string, MessagingPattern['type']> = {
      'error_message': 'error',
      'success_message': 'success',
      'help_text': 'info'
    };

    const messageType = messageTypeMap[contentType];
    if (!messageType) return issues;

    const pattern = this.messagingPatterns[messageType];
    const followsPattern = this.checkPatternCompliance(text, pattern);

    if (!followsPattern) {
      issues.push({
        id: `messaging_pattern_${messageType}_${Date.now()}`,
        type: IssueType.CONTENT_INCONSISTENCY,
        severity: Severity.MEDIUM,
        category: Category.CONTENT,
        description: `Message doesn't follow standard ${messageType} pattern`,
        location: {
          filePath: 'content',
          lineNumber: 0,
          columnNumber: 0,
          context: text
        },
        suggestion: `Consider using pattern: ${pattern.template}`,
        autoFixable: false,
        impact: {
          userExperience: 7,
          maintenanceEffort: 5,
          implementationComplexity: 4
        }
      });
    }

    return issues;
  }

  private escapeRegex(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }
}