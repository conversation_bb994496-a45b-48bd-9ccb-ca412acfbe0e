import React from "react";
import { Button } from "@/components/ui/button";

import useNavigation from "@/hooks/useNavigation";
import { cn } from "@/lib/utils";
import { getResponsiveTextSize } from "@/utils/responsive-utilities";

interface NavigationButtonProps {
  to: string;
  children: React.ReactNode;
  className?: string;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" | "navy";
  size?: "default" | "sm" | "lg" | "icon";
  loadingTime?: number;
  mobileVariant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" | "navy";
  mobileSize?: "default" | "sm" | "lg" | "icon";
  showLoadingText?: boolean;
}

/**
 * A responsive button component that adapts behavior and styling based on screen size
 */
const NavigationButton = ({
  to,
  children,
  className = "",
  variant = "default",
  size = "default",
  loadingTime = 500,
  mobileVariant,
  mobileSize,
  showLoadingText = true
}: NavigationButtonProps) => {
  const { isNavigating, navigateTo, currentDestination } = useNavigation();

  
  // Check if this specific button's destination is currently loading
  const isThisButtonLoading = isNavigating && currentDestination === to;

  /**
   * Handle button click with navigation
   */
  const handleClick = () => {
    navigateTo(to, loadingTime);
  };

  /**
   * Generate responsive loading indicator
   */
  const renderLoadingIndicator = () => {
    return (
      <div className="flex items-center justify-center">
        <div className={cn(
          "border-2 border-white border-t-transparent rounded-full animate-spin",
          "h-4 w-4 md:h-3 md:w-3", // Responsive spinner size
          showLoadingText ? "mr-3 md:mr-2" : "" // Responsive spacing
        )}></div>
        {showLoadingText && (
          <span className={cn(getResponsiveTextSize("sm", "xs", "xs"))}>
            Loading...
          </span>
        )}
      </div>
    );
  };

  // Enhanced responsive button styling
  const responsiveClassName = cn(
    className,
    // Ensure proper touch targets on mobile
    "min-h-[44px] min-w-[44px] md:min-h-0 md:min-w-0",
    // Enhanced focus states
    "focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
  );

  // Use responsive variants and sizes - mobile first approach
  const activeVariant = mobileVariant || variant;
  const activeSize = mobileSize || size;

  return (
    <Button
      variant={activeVariant}
      size={activeSize}
      className={responsiveClassName}
      onClick={handleClick}
      disabled={isNavigating}
      aria-disabled={isNavigating}
      aria-label={isThisButtonLoading ? `Loading ${children}` : undefined}
    >
      {isThisButtonLoading ? renderLoadingIndicator() : children}
    </Button>
  );
};

export default NavigationButton;
