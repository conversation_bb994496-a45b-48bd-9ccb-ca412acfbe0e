// Example demonstrating typography standardization utilities integration

import { TypographyAnalyzer } from '../analyzers/typography-analyzer';
import { TypographyStandardizationUtilities } from '../analyzers/typography-standardization';
import { FileInfo } from '../types';

/**
 * Example demonstrating the complete typography audit and standardization workflow
 */
export async function demonstrateTypographyStandardization() {
  // Sample files with typography inconsistencies
  const sampleFiles: FileInfo[] = [
    {
      path: 'components/Header.tsx',
      content: `
        import React from 'react';
        
        const Header = () => {
          return (
            <header style={{ fontSize: '24px', fontWeight: '700', lineHeight: '1.33' }}>
              <h1 style={{ fontSize: '32px', fontWeight: '800' }}>Welcome</h1>
              <p style={{ fontSize: '16px', lineHeight: '1.67' }}>Subtitle text</p>
            </header>
          );
        };
        
        export default Header;
      `,
      extension: '.tsx',
      size: 400,
      lastModified: new Date()
    },
    {
      path: 'styles/typography.css',
      content: `
        .title {
          font-size: 28px;
          font-weight: 600;
          line-height: 1.2;
          font-family: "Inter", sans-serif;
        }
        
        .subtitle {
          font-size: 18px;
          font-weight: 500;
          line-height: 1.45;
        }
        
        .body-text {
          font-size: 14px;
          line-height: 1.6;
          font-family: "Roboto", Arial, sans-serif;
        }
      `,
      extension: '.css',
      size: 300,
      lastModified: new Date()
    }
  ];

  console.log('=== Typography Audit and Standardization Demo ===\n');

  // Step 1: Analyze existing typography inconsistencies
  console.log('1. Analyzing typography inconsistencies...');
  const analyzer = new TypographyAnalyzer();
  const analysisResult = await analyzer.analyze(sampleFiles);
  
  console.log(`   Found ${analysisResult.issues.length} typography issues:`);
  analysisResult.issues.forEach((issue, index) => {
    console.log(`   ${index + 1}. ${issue.description} (${issue.severity})`);
    console.log(`      Location: ${issue.location.filePath}:${issue.location.lineNumber}`);
    console.log(`      Suggestion: ${issue.suggestion}\n`);
  });

  // Step 2: Generate standardization utilities
  console.log('2. Initializing typography standardization utilities...');
  const standardizationUtils = new TypographyStandardizationUtilities();

  // Step 3: Convert hardcoded values to tokens
  console.log('3. Converting hardcoded typography to design tokens...');
  const tokenFixes = await standardizationUtils.convertHardcodedToTokens(sampleFiles);
  
  console.log(`   Generated ${tokenFixes.length} token conversion fixes:`);
  tokenFixes.forEach((fix, index) => {
    console.log(`   ${index + 1}. ${fix.filePath}:${fix.lineNumber}`);
    console.log(`      Replace: "${fix.originalValue}" → "${fix.replacementValue}"`);
    console.log(`      Token: ${fix.tokenName}\n`);
  });

  // Step 4: Validate typography hierarchy
  console.log('4. Validating typography hierarchy...');
  const hierarchyValidation = await standardizationUtils.validateTypographyHierarchy(sampleFiles);
  
  console.log(`   Hierarchy validation: ${hierarchyValidation.isValid ? 'PASSED' : 'FAILED'}`);
  if (!hierarchyValidation.isValid) {
    console.log(`   Found ${hierarchyValidation.violations.length} hierarchy violations:`);
    hierarchyValidation.violations.forEach((violation, index) => {
      console.log(`   ${index + 1}. ${violation.element} ${violation.property}: "${violation.currentValue}" should be "${violation.expectedValue}"`);
      console.log(`      Location: ${violation.location.filePath}:${violation.location.lineNumber}\n`);
    });
  }

  // Step 5: Standardize line heights
  console.log('5. Standardizing line heights...');
  const lineHeightFixes = await standardizationUtils.standardizeLineHeights(sampleFiles);
  
  console.log(`   Generated ${lineHeightFixes.length} line-height standardization fixes:`);
  lineHeightFixes.forEach((fix, index) => {
    console.log(`   ${index + 1}. ${fix.filePath}:${fix.lineNumber}`);
    console.log(`      Standardize: "${fix.originalValue}" → "${fix.replacementValue}"`);
    console.log(`      Token: ${fix.tokenName}\n`);
  });

  // Step 6: Generate comprehensive design token system
  console.log('6. Generating design token system...');
  const tokenSystem = standardizationUtils.generateDesignTokenSystem(sampleFiles);
  
  console.log('   Generated design token system:');
  console.log(`   Font Sizes: ${Object.keys(tokenSystem.fontSize).length} tokens`);
  Object.entries(tokenSystem.fontSize).forEach(([name, value]) => {
    console.log(`     --font-size-${name}: ${value}`);
  });
  
  console.log(`   Font Families: ${Object.keys(tokenSystem.fontFamily).length} tokens`);
  Object.entries(tokenSystem.fontFamily).forEach(([name, value]) => {
    console.log(`     --font-family-${name}: ${value}`);
  });
  
  console.log(`   Font Weights: ${Object.keys(tokenSystem.fontWeight).length} tokens`);
  Object.entries(tokenSystem.fontWeight).forEach(([name, value]) => {
    console.log(`     --font-weight-${name}: ${value}`);
  });

  // Step 7: Apply fixes (simulation)
  console.log('\n7. Applying typography fixes...');
  const allFixes = [...tokenFixes, ...lineHeightFixes];
  const applyResult = await standardizationUtils.applyTypographyFixes(allFixes);
  
  console.log(`   Applied ${applyResult.appliedFixes} fixes successfully`);
  if (applyResult.errors.length > 0) {
    console.log(`   Encountered ${applyResult.errors.length} errors:`);
    applyResult.errors.forEach(error => console.log(`     - ${error}`));
  }

  console.log('\n=== Typography Standardization Complete ===');
  
  return {
    analysisResult,
    tokenFixes,
    hierarchyValidation,
    lineHeightFixes,
    tokenSystem,
    applyResult
  };
}

// Example usage
if (require.main === module) {
  demonstrateTypographyStandardization()
    .then(() => {
      console.log('\nDemo completed successfully!');
    })
    .catch((error) => {
      console.error('Demo failed:', error);
    });
}