import React from 'react';
import SEO from './SEO';
import { CourseSchema, ProductSchema } from '@/utils/schemaExtensions';

interface TrainingProgramSEOProps {
  title: string;
  programName: string;
  programDescription: string;
  programImage?: string;
  programType: 'individual' | 'group' | 'goalie' | 'player-skills' | 'pop-up';
  price?: number;
  slug: string;
  reviews?: Array<{
    reviewRating: number;
    author: string;
    reviewBody?: string;
    datePublished?: string;
  }>;
}

/**
 * Specialized SEO component for training program pages
 * Implements both Course and Product schemas for better search visibility
 */
const TrainingProgramSEO: React.FC<TrainingProgramSEOProps> = ({
  title,
  programName,
  programDescription,
  programImage = '/images/facility.jpg',
  programType,
  price,
  slug,
  reviews = []
}) => {
  // Generate SEO-friendly keywords based on program type
  const keywordsMap = {
    'individual': 'one-on-one hockey training, private hockey lessons, personal hockey coach, Las Vegas hockey training',
    'group': 'hockey team training, small group hockey training, hockey practice Las Vegas, team hockey development',
    'goalie': 'hockey goalie training, goaltender development, goalie coaching Las Vegas, professional goalie trainer',
    'player-skills': 'hockey player development, skating skills, stickhandling training, hockey shooting techniques, Las Vegas hockey skills',
    'pop-up': 'hockey clinics Las Vegas, pop-up hockey training, special hockey events, hockey skills workshop'
  };
  
  const keywords = `ELEV802, 702Hockey, Las Vegas hockey, ${keywordsMap[programType]}`;
  
  // Full canonical URL for the program
  const canonical = `/${slug}`;
  
  // More detailed program description optimized for SEO
  const enhancedDescription = `${programDescription} Train with expert coaches at ELEV802's state-of-the-art Las Vegas hockey facility. Year-round training available with custom ice surface. ELEV802 | 702HOCKEY - the premier hockey training destination in Las Vegas.`;
  
  return (
    <>
      <SEO 
        title={title}
        description={enhancedDescription}
        keywords={keywords}
        canonical={canonical}
        image={programImage}
      />
      
      {/* Course schema for training programs */}
      <CourseSchema
        name={programName}
        description={programDescription}
        provider="ELEV802 | 702HOCKEY"
        url={`https://702hockey.com/${slug}`}
        image={`https://702hockey.com${programImage}`}
        courseCode={`ELEV802-${programType}`}
        hasCourseInstance={[
          {
            name: `${programName} - Ongoing Enrollment`,
            description: `${programDescription} Available year-round with flexible scheduling.`,
            courseMode: 'https://schema.org/OnlineOnlyEventAttendanceMode',
            price: price || 99,
            priceCurrency: 'USD'
          }
        ]}
      />
      
      {/* Product schema for training programs */}
      <ProductSchema
        name={programName}
        description={programDescription}
        url={`https://702hockey.com/${slug}`}
        image={`https://702hockey.com${programImage}`}
        reviews={reviews.length > 0 ? reviews : [
          {
            reviewRating: 5,
            author: "Hockey Parent",
            reviewBody: "Excellent training program that significantly improved my child's hockey skills."
          }
        ]}
        offers={price ? {
          price: price,
          priceCurrency: 'USD',
          availability: 'https://schema.org/InStock'
        } : undefined}
      />
    </>
  );
};

export default TrainingProgramSEO;
