// SECTION: Imports
// ==================================================================================
import { useEffect, useRef, useState } from "react";

// SECTION: GoogleMap Component
// ==================================================================================
const GoogleMap = () => {
  // --- Hooks ---
  const mapRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Effect to dynamically load Google Map iframe
  useEffect(() => {
    if (mapRef.current) {
      const iframe = document.createElement('iframe');
      iframe.src = `https://www.google.com/maps/embed/v1/place?key=AIzaSyDP_uuwyr-GBmIMSOdVpKq46XQ8YoB69Rs&q=5031+Wagon+Trail+Ave+STE+100,+Las+Vegas,+NV+89118`;
      iframe.width = "100%";
      iframe.height = "100%";
      iframe.frameBorder = "0";
      iframe.style.border = "0";
      iframe.style.borderRadius = "0.5rem";
      iframe.allowFullscreen = true;
      iframe.loading = "lazy";
      
      iframe.onload = () => {
        setIsLoading(false);
      };
      
      iframe.onerror = () => {
        setHasError(true);
        setIsLoading(false);
      };
      
      mapRef.current.appendChild(iframe);
    }
  }, []);

  // --- Component JSX ---
  return (
    <div className="w-full bg-white py-8 sm:py-12">
      <div className="container-responsive">
        <div className="text-center mb-6 sm:mb-8">
          <h2 className="heading-responsive-h2 text-elev-navy mb-4">Our Location</h2>
          <p className="text-responsive-base text-elev-text-primary max-w-3xl mx-auto px-4">
            Conveniently located in the heart of Las Vegas, our facility is easily accessible from major freeways 
            and centrally positioned to serve hockey players throughout the valley. We're conveniently located next door to Pure Hockey, 
            making it easy to grab gear and get right to training. Whether you're coming from 
            Henderson, Summerlin, or anywhere in between, you'll find us just minutes away.
          </p>
        </div>
        
        <div className="relative w-full">
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-elev-background-muted rounded-lg border border-elev-background-accent z-10">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-elev-navy mx-auto mb-4"></div>
                <p className="text-elev-text-secondary text-sm sm:text-base">Loading map...</p>
              </div>
            </div>
          )}
          
          {hasError && (
            <div className="flex items-center justify-center p-8 bg-elev-background-muted rounded-lg border border-red-200">
              <div className="text-center">
                <div className="text-red-500 mb-2">
                  <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <p className="text-elev-text-secondary text-sm sm:text-base mb-4">Could not load map. Please check your connection.</p>
                <a 
                  href="https://maps.google.com/?q=5031+Wagon+Trail+Ave+STE+100,+Las+Vegas,+NV+89118" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center space-x-2 text-elev-navy hover:text-elev-navy-dark transition-colors duration-200"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                  <span>Open in Google Maps</span>
                </a>
              </div>
            </div>
          )}
          
          <div 
            ref={mapRef} 
            className={`w-full h-64 sm:h-80 md:h-96 lg:h-[450px] rounded-lg overflow-hidden shadow-lg ${
              hasError ? 'hidden' : 'block'
            }`} 
          />
        </div>
      </div>
    </div>
  );
};

export default GoogleMap;
