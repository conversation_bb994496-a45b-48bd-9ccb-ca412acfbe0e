import { cn } from "@/lib/utils";

interface LoadingSpinnerProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  text?: string;
  showText?: boolean;
  variant?: 'default' | 'overlay' | 'inline';
}

/**
 * Enhanced LoadingSpinner component with responsive design
 * Supports multiple sizes, variants, and responsive behavior
 */
export const LoadingSpinner = ({ 
  size = 'md',
  className,
  text = 'Loading...',
  showText = false,
  variant = 'default'
}: LoadingSpinnerProps) => {
  const sizeClasses = {
    xs: 'h-4 w-4 border-2',
    sm: 'h-6 w-6 border-2',
    md: 'h-8 w-8 sm:h-12 sm:w-12 border-2 sm:border-3',
    lg: 'h-12 w-12 sm:h-16 sm:w-16 border-3 sm:border-4',
    xl: 'h-16 w-16 sm:h-20 sm:w-20 border-4 sm:border-[5px]',
  };

  const textSizeClasses = {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-sm sm:text-base',
    lg: 'text-base sm:text-lg',
    xl: 'text-lg sm:text-xl',
  };

  const containerClasses = {
    default: 'flex flex-col items-center justify-center p-4 sm:p-8',
    overlay: 'fixed inset-0 bg-white/80 backdrop-blur-sm flex flex-col items-center justify-center z-50',
    inline: 'flex items-center justify-center'
  };

  const spinnerClasses = cn(
    "animate-spin rounded-full border-t-transparent border-elev-navy",
    sizeClasses[size]
  );

  return (
    <div className={cn(containerClasses[variant], className)}>
      <div
        className={spinnerClasses}
        role="status"
        aria-label={text}
      />
      {showText && (
        <p className={cn(
          "mt-2 sm:mt-4 text-elev-navy font-medium",
          textSizeClasses[size]
        )}>
          {text}
        </p>
      )}
    </div>
  );
};

/**
 * Responsive page loading spinner for full-page loading states
 */
export const PageLoadingSpinner = ({ 
  text = 'Loading page...'
}: { text?: string }) => (
  <LoadingSpinner 
    size="lg" 
    variant="overlay" 
    showText 
    text={text}
    className="min-h-screen"
  />
);

/**
 * Responsive inline loading spinner for buttons and small components
 */
export const InlineLoadingSpinner = ({ 
  size = 'sm',
  className
}: { size?: 'xs' | 'sm'; className?: string }) => (
  <LoadingSpinner 
    size={size} 
    variant="inline" 
    className={className}
  />
);
