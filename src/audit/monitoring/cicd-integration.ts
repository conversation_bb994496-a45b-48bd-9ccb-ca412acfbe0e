import { MonitoringConfig, MonitoringResult, Issue } from './types';
import { ConsistencyMonitor } from './consistency-monitor';
import { AuditLogger } from '../core/logger';
import * as fs from 'fs/promises';
import * as path from 'path';

export interface CICDResult {
  success: boolean;
  exitCode: number;
  message: string;
  reportPath?: string;
  summary: {
    totalIssues: number;
    criticalIssues: number;
    highIssues: number;
    complianceScore: number;
  };
}

export class CICDIntegration {
  private monitor: ConsistencyMonitor;
  private config: MonitoringConfig;
  private logger: AuditLogger;

  constructor(monitor: ConsistencyMonitor, config: MonitoringConfig) {
    this.monitor = monitor;
    this.config = config;
    this.logger = new AuditLogger();
  }

  /**
   * Run design system compliance check for CI/CD pipeline
   */
  public async runComplianceCheck(projectPath: string = process.cwd()): Promise<CICDResult> {
    this.logger.info('Running CI/CD compliance check...');

    try {
      const result = await this.monitor.performCheck(projectPath);
      const summary = this.createSummary(result);
      
      // Generate report if configured
      let reportPath: string | undefined;
      if (this.config.cicd.generateReports) {
        reportPath = await this.generateCICDReport(result, summary);
      }

      // Determine if build should fail
      const shouldFail = this.shouldFailBuild(summary);
      const exitCode = shouldFail ? 1 : 0;
      
      const cicdResult: CICDResult = {
        success: !shouldFail,
        exitCode,
        message: this.createStatusMessage(summary, shouldFail),
        reportPath,
        summary
      };

      // Log results
      this.logResults(cicdResult);

      return cicdResult;
    } catch (error) {
      this.logger.error('CI/CD compliance check failed:', error);
      return {
        success: false,
        exitCode: 2,
        message: `Compliance check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        summary: {
          totalIssues: 0,
          criticalIssues: 0,
          highIssues: 0,
          complianceScore: 0
        }
      };
    }
  }

  /**
   * Create summary from monitoring result
   */
  private createSummary(result: MonitoringResult) {
    return {
      totalIssues: result.totalIssues,
      criticalIssues: result.severityBreakdown.critical || 0,
      highIssues: result.severityBreakdown.high || 0,
      complianceScore: result.complianceScore
    };
  }

  /**
   * Determine if build should fail based on configuration
   */
  private shouldFailBuild(summary: { totalIssues: number; criticalIssues: number; highIssues: number; }): boolean {
    if (this.config.cicd.failOnErrors && summary.totalIssues > 0) {
      return true;
    }

    if (this.config.cicd.failOnWarnings && (summary.criticalIssues > 0 || summary.highIssues > 0)) {
      return true;
    }

    return false;
  }

  /**
   * Create status message for CI/CD output
   */
  private createStatusMessage(summary: any, shouldFail: boolean): string {
    const status = shouldFail ? '❌ FAILED' : '✅ PASSED';
    const messages = [
      `Design System Compliance Check ${status}`,
      `Total Issues: ${summary.totalIssues}`,
      `Critical: ${summary.criticalIssues}, High: ${summary.highIssues}`,
      `Compliance Score: ${summary.complianceScore}%`
    ];

    if (shouldFail) {
      messages.push('Build failed due to design system violations');
    }

    return messages.join('\n');
  }

  /**
   * Generate CI/CD report
   */
  private async generateCICDReport(result: MonitoringResult, summary: any): Promise<string> {
    const reportDir = path.resolve(this.config.cicd.reportPath);
    await fs.mkdir(reportDir, { recursive: true });

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportPath = path.join(reportDir, `design-system-report-${timestamp}.json`);

    const report = {
      timestamp: result.timestamp,
      summary,
      issues: result.newIssues.length > 0 ? result.newIssues : [],
      resolvedIssues: result.resolvedIssues,
      complianceScore: result.complianceScore,
      trend: result.trend,
      severityBreakdown: result.severityBreakdown
    };

    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

    // Also generate HTML report for better readability
    const htmlReportPath = path.join(reportDir, `design-system-report-${timestamp}.html`);
    await this.generateHTMLReport(report, htmlReportPath);

    this.logger.info(`Reports generated: ${reportPath}, ${htmlReportPath}`);
    return reportPath;
  }

  /**
   * Generate HTML report for better visualization
   */
  private async generateHTMLReport(report: any, htmlPath: string): Promise<void> {
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Design System Compliance Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { border-bottom: 2px solid #eee; padding-bottom: 20px; margin-bottom: 20px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric { background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; }
        .metric-value { font-size: 2em; font-weight: bold; color: #333; }
        .metric-label { color: #666; margin-top: 5px; }
        .compliance-score { color: ${report.complianceScore >= 80 ? '#28a745' : report.complianceScore >= 60 ? '#ffc107' : '#dc3545'}; }
        .issues { margin-top: 30px; }
        .issue { background: #fff; border: 1px solid #ddd; border-radius: 4px; padding: 15px; margin-bottom: 10px; }
        .issue-header { display: flex; justify-content: between; align-items: center; margin-bottom: 10px; }
        .severity { padding: 4px 8px; border-radius: 4px; color: white; font-size: 0.8em; font-weight: bold; }
        .severity-critical { background: #dc3545; }
        .severity-high { background: #fd7e14; }
        .severity-medium { background: #ffc107; color: #000; }
        .severity-low { background: #28a745; }
        .trend { padding: 4px 8px; border-radius: 4px; font-weight: bold; }
        .trend-improving { background: #d4edda; color: #155724; }
        .trend-degrading { background: #f8d7da; color: #721c24; }
        .trend-stable { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Design System Compliance Report</h1>
            <p>Generated: ${new Date(report.timestamp).toLocaleString()}</p>
            <span class="trend trend-${report.trend}">${report.trend.toUpperCase()}</span>
        </div>

        <div class="summary">
            <div class="metric">
                <div class="metric-value compliance-score">${report.complianceScore}%</div>
                <div class="metric-label">Compliance Score</div>
            </div>
            <div class="metric">
                <div class="metric-value">${report.summary.totalIssues}</div>
                <div class="metric-label">Total Issues</div>
            </div>
            <div class="metric">
                <div class="metric-value">${report.summary.criticalIssues}</div>
                <div class="metric-label">Critical Issues</div>
            </div>
            <div class="metric">
                <div class="metric-value">${report.summary.highIssues}</div>
                <div class="metric-label">High Priority Issues</div>
            </div>
        </div>

        <div class="issues">
            <h2>Issues Found (${report.issues.length})</h2>
            ${report.issues.map((issue: Issue) => `
                <div class="issue">
                    <div class="issue-header">
                        <span class="severity severity-${issue.severity}">${issue.severity.toUpperCase()}</span>
                        <span>${issue.type}</span>
                    </div>
                    <p><strong>Description:</strong> ${issue.description}</p>
                    <p><strong>Location:</strong> ${issue.location.filePath}:${issue.location.lineNumber}</p>
                </div>
            `).join('')}
        </div>

        ${report.resolvedIssues.length > 0 ? `
        <div class="issues">
            <h2>Resolved Issues (${report.resolvedIssues.length})</h2>
            ${report.resolvedIssues.map((issue: Issue) => `
                <div class="issue" style="background: #d4edda;">
                    <div class="issue-header">
                        <span class="severity severity-${issue.severity}">${issue.severity.toUpperCase()}</span>
                        <span>${issue.type}</span>
                    </div>
                    <p><strong>Description:</strong> ${issue.description}</p>
                    <p><strong>Location:</strong> ${issue.location.filePath}:${issue.location.lineNumber}</p>
                </div>
            `).join('')}
        </div>
        ` : ''}
    </div>
</body>
</html>`;

    await fs.writeFile(htmlPath, html);
  }

  /**
   * Log results to console with appropriate formatting
   */
  private logResults(result: CICDResult): void {
    console.log('\n' + '='.repeat(60));
    console.log('DESIGN SYSTEM COMPLIANCE CHECK RESULTS');
    console.log('='.repeat(60));
    console.log(result.message);
    
    if (result.reportPath) {
      console.log(`\nReport generated: ${result.reportPath}`);
    }
    
    console.log('='.repeat(60) + '\n');

    if (result.success) {
      this.logger.info('CI/CD compliance check passed');
    } else {
      this.logger.error('CI/CD compliance check failed');
    }
  }

  /**
   * Create GitHub Actions output
   */
  public createGitHubActionsOutput(result: CICDResult): void {
    if (process.env.GITHUB_ACTIONS) {
      // Set outputs for GitHub Actions
      console.log(`::set-output name=success::${result.success}`);
      console.log(`::set-output name=total-issues::${result.summary.totalIssues}`);
      console.log(`::set-output name=critical-issues::${result.summary.criticalIssues}`);
      console.log(`::set-output name=compliance-score::${result.summary.complianceScore}`);
      
      if (result.reportPath) {
        console.log(`::set-output name=report-path::${result.reportPath}`);
      }

      // Create annotations for issues
      if (!result.success) {
        console.log(`::error::Design system compliance check failed with ${result.summary.totalIssues} issues`);
      }
    }
  }

  /**
   * Create configuration for popular CI/CD platforms
   */
  public static generateCICDConfigs(): Record<string, string> {
    return {
      'github-actions': `
name: Design System Compliance
on: [push, pull_request]

jobs:
  design-system-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run design-system:check
        id: compliance-check
      - uses: actions/upload-artifact@v3
        if: always()
        with:
          name: design-system-report
          path: reports/
`,
      'gitlab-ci': `
design-system-check:
  stage: test
  script:
    - npm ci
    - npm run design-system:check
  artifacts:
    reports:
      junit: reports/design-system-report.xml
    paths:
      - reports/
  allow_failure: false
`,
      'jenkins': `
pipeline {
    agent any
    stages {
        stage('Design System Check') {
            steps {
                sh 'npm ci'
                sh 'npm run design-system:check'
            }
            post {
                always {
                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: 'reports',
                        reportFiles: '*.html',
                        reportName: 'Design System Report'
                    ])
                }
            }
        }
    }
}
`
    };
  }
}