// SECTION: Imports
// ==================================================================================
import { useState, useEffect, useRef } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import NavigationLink from "@/components/ui/NavigationLink";
import { cn } from "@/lib/utils";
import elevLogo from "@/assets/elev802-vegas.jpg";
import ResponsiveImage from "@/components/ui/responsive-image";

// SECTION: Header Component
// ==================================================================================
const Header = () => {
  // --- Hooks & State ---
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const mobileMenuRef = useRef<HTMLDivElement>(null);
  const toggleButtonRef = useRef<HTMLButtonElement>(null);

  // --- Effect for scroll handling and focus management ---
  useEffect(() => {
    const handleScroll = () => {
      if (mobileMenuOpen) {
        setMobileMenuOpen(false);
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && mobileMenuOpen) {
        setMobileMenuOpen(false);
        toggleButtonRef.current?.focus();
      }
    };

    const handleClickOutside = (event: MouseEvent) => {
      if (mobileMenuOpen && mobileMenuRef.current && !mobileMenuRef.current.contains(event.target as Node)) {
        setMobileMenuOpen(false);
      }
    };

    if (mobileMenuOpen) {
      window.addEventListener('scroll', handleScroll);
      document.addEventListener('keydown', handleEscape);
      document.addEventListener('mousedown', handleClickOutside);
      // Prevent body scroll when mobile menu is open
      document.body.style.overflow = 'hidden';
      document.body.classList.add('prevent-scroll');
    } else {
      document.body.style.overflow = 'unset';
      document.body.classList.remove('prevent-scroll');
    }

    return () => {
      window.removeEventListener('scroll', handleScroll);
      document.removeEventListener('keydown', handleEscape);
      document.removeEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'unset';
      document.body.classList.remove('prevent-scroll');
    };
  }, [mobileMenuOpen]);

  // --- Helper Functions ---
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const handleNavClick = (_item: string) => {
    // Analytics tracking can be re-implemented here if needed
  };

  const handleContactScrollOrNavigate = (navItemName: string) => {
    handleNavClick(navItemName);
    if (location.pathname === "/") {
      document.getElementById('contact-form')?.scrollIntoView({ behavior: 'smooth' });
    } else {
      navigate("/", { state: { scrollToContact: true } });
    }
  };

  const closeMobileMenu = () => {
    setMobileMenuOpen(false);
    toggleButtonRef.current?.focus();
  };

  // --- Navigation Items ---
  const navigationItems = [
    { to: "/", label: "Home", key: "Home" },
    { to: "/scheduling", label: "Scheduling", key: "Scheduling" },
    { to: "/coaches", label: "Coaches", key: "Coaches" },
    { to: "/goalie-training", label: "Goalie Training", key: "Goalie Training" },
    { to: "/player-skills-training", label: "Player Training", key: "Player Training" },
    { to: "/pop-up-clinics", label: "Camps + Clinics", key: "Camps + Clinics" },
    { to: "/marketing-package-builder", label: "Marketing", key: "Marketing" },
    { to: "/faq", label: "FAQ", key: "FAQ" }
  ];

  // --- Component JSX ---
  return (
    <header className="bg-white text-black relative z-50 shadow-sm">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* --- Header Content --- */}
        <div className="flex justify-between items-center w-full py-3 sm:py-4">
          {/* --- Logo --- */}
          <div className="flex-shrink-0">
            <Link 
              to="/" 
              className="flex items-center focus:outline-none focus:ring-2 focus:ring-elev-blue focus:ring-offset-2 rounded-md p-1 -m-1" 
              onClick={() => handleNavClick("Logo")}
            >
              <ResponsiveImage
                src={elevLogo}
                alt="ELEV802 Vegas"
                className="h-8 sm:h-10 w-auto"
                loading="eager"
                priority
              />
            </Link>
          </div>

          {/* --- Desktop Navigation --- */}
          <nav className="hidden md:flex items-center justify-center space-x-1 md:space-x-2 lg:space-x-4 xl:space-x-6" role="navigation" aria-label="Main navigation">
            {navigationItems.map((item) => (
              item.to === "/" ? (
                <Link 
                  key={item.key}
                  to={item.to}
                  className="text-sm md:text-sm lg:text-base xl:text-lg text-elev-text-primary hover:text-elev-navy transition-colors focus:outline-none focus:ring-2 focus:ring-elev-blue focus:ring-offset-2 rounded-md px-2 md:px-3 lg:px-4 py-2 font-medium min-h-[44px] flex items-center"
                  onClick={() => handleNavClick(item.key)}
                >
                  {item.label}
                </Link>
              ) : (
                <NavigationLink 
                  key={item.key}
                  to={item.to}
                  className="text-sm md:text-sm lg:text-base xl:text-lg text-elev-text-primary hover:text-elev-navy transition-colors focus:outline-none focus:ring-2 focus:ring-elev-blue focus:ring-offset-2 rounded-md px-2 md:px-3 lg:px-4 py-2 font-medium min-h-[44px] flex items-center"
                  onClick={() => handleNavClick(item.key)}
                  showLoadingText={false}
                >
                  {item.label}
                </NavigationLink>
              )
            ))}
            <a
              onClick={() => handleContactScrollOrNavigate("Contact")}
              className="text-sm md:text-sm lg:text-base xl:text-lg text-elev-text-primary hover:text-elev-navy transition-colors cursor-pointer focus:outline-none focus:ring-2 focus:ring-elev-blue focus:ring-offset-2 rounded-md px-2 md:px-3 lg:px-4 py-2 font-medium min-h-[44px] flex items-center"
              role="button"
              tabIndex={0}
              onKeyDown={(e) => e.key === 'Enter' && handleContactScrollOrNavigate("Contact")}
            >
              Contact
            </a>
          </nav>

          {/* --- Mobile Menu Button --- */}
          <div className="md:hidden flex items-center">
            <button 
              ref={toggleButtonRef}
              className="text-black p-2 rounded-md focus:outline-none focus:ring-2 focus:ring-elev-blue focus:ring-offset-2 min-h-[44px] min-w-[44px] flex items-center justify-center transition-colors hover:bg-elev-background-muted"
              onClick={toggleMobileMenu}
              aria-label={mobileMenuOpen ? "Close navigation menu" : "Open navigation menu"}
              aria-expanded={mobileMenuOpen}
              aria-controls="mobile-menu"
            >
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                fill="none" 
                viewBox="0 0 24 24" 
                stroke="currentColor" 
                className={cn("h-7 w-7 transition-transform duration-300 ease-in-out", mobileMenuOpen && "rotate-180")}
                aria-hidden="true"
              >
                {mobileMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* --- Mobile Navigation Menu (Overlay) --- */}
      <div 
        ref={mobileMenuRef}
        id="mobile-menu"
        className={cn(
          "mobile-menu-overlay z-50 md:hidden bg-white transition-all duration-300 ease-in-out overflow-y-auto",
          mobileMenuOpen ? "opacity-100 translate-x-0" : "opacity-0 -translate-x-full pointer-events-none"
        )}
        role="dialog"
        aria-modal="true"
        aria-label="Navigation menu"
      >
        {/* Header with logo and close button */}
        <div className="flex justify-between items-center px-4 sm:px-6 py-3 sm:py-4 border-b border-elev-background-accent">
          <Link 
            to="/" 
            className="flex items-center focus:outline-none focus:ring-2 focus:ring-elev-blue focus:ring-offset-2 rounded-md p-1 -m-1" 
            onClick={() => {
              handleNavClick("Mobile Logo");
              closeMobileMenu();
            }}
          >
            <ResponsiveImage
              src={elevLogo}
              alt="ELEV802 Vegas"
              className="h-8 sm:h-10 w-auto"
              loading="eager"
            />
          </Link>
          <button 
            onClick={closeMobileMenu}
            className="p-2 rounded-md text-elev-text-primary hover:bg-elev-background-muted focus:outline-none focus:ring-2 focus:ring-elev-blue focus:ring-offset-2 transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center"
            aria-label="Close menu"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor" 
              className="h-7 w-7"
              aria-hidden="true"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {/* Navigation content */}
        <div className="px-4 sm:px-6 py-6">
          <nav 
            className="flex flex-col space-y-3"
            role="navigation"
            aria-label="Mobile navigation"
          >
            {navigationItems.map((item) => (
              item.to === "/" ? (
                <Link 
                  key={item.key}
                  to={item.to}
                  className="text-lg sm:text-xl text-elev-text-primary hover:text-elev-navy transition-all duration-200 py-4 px-6 focus:outline-none focus:ring-2 focus:ring-elev-blue focus:ring-offset-2 rounded-xl font-semibold hover:bg-elev-background-muted active:bg-elev-background-accent min-h-[56px] flex items-center justify-center border border-transparent hover:border-elev-background-accent"
                  onClick={() => {
                    handleNavClick(`Mobile - ${item.key}`);
                    closeMobileMenu();
                  }}
                >
                  {item.label}
                </Link>
              ) : (
                <NavigationLink 
                  key={item.key}
                  to={item.to}
                  className="text-lg sm:text-xl text-elev-text-primary hover:text-elev-navy transition-all duration-200 py-4 px-6 focus:outline-none focus:ring-2 focus:ring-elev-blue focus:ring-offset-2 rounded-xl font-semibold hover:bg-elev-background-muted active:bg-elev-background-accent min-h-[56px] flex items-center justify-center border border-transparent hover:border-elev-background-accent"
                  onClick={() => {
                    handleNavClick(`Mobile - ${item.key}`);
                    closeMobileMenu();
                  }}
                >
                  {item.label}
                </NavigationLink>
              )
            ))}
            <a
              onClick={() => {
                handleContactScrollOrNavigate("Mobile - Contact");
                closeMobileMenu();
              }}
              className="text-lg sm:text-xl text-elev-text-primary hover:text-elev-navy transition-all duration-200 py-4 px-6 cursor-pointer focus:outline-none focus:ring-2 focus:ring-elev-blue focus:ring-offset-2 rounded-xl font-semibold hover:bg-elev-background-muted active:bg-elev-background-accent min-h-[56px] flex items-center justify-center border border-transparent hover:border-elev-background-accent"
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleContactScrollOrNavigate("Mobile - Contact");
                  closeMobileMenu();
                }
              }}
            >
              Contact
            </a>
            
            {/* CTA Button */}
            <div className="pt-8 border-t border-elev-background-accent mt-8">
              <Button 
                onClick={() => {
                  handleContactScrollOrNavigate("Mobile - Request Personal Assessment");
                  closeMobileMenu();
                }}
                variant="navy"
                size="lg"
                className="w-full min-h-[56px] text-lg sm:text-xl font-semibold focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 py-4"
              >
                Request Personal Assessment
              </Button>
            </div>
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Header;
