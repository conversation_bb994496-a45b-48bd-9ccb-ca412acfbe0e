import { Skeleton } from "@/components/ui/skeleton";
import ProgramCardSkeleton from "./ProgramCardSkeleton";

/**
 * Skeleton loader for the entire Training Programs section
 * Used during content loading states to provide visual feedback
 */
export function TrainingProgramsSkeleton() {
  return (
    <section className="section-padding bg-gradient-to-b from-gray-50 to-white">
      <div className="container mx-auto">
        {/* Section header skeleton */}
        <div className="text-center mb-12">
          <Skeleton className="h-8 w-64 mx-auto mb-4" />
          <Skeleton className="h-4 w-full max-w-3xl mx-auto mb-2" />
          <Skeleton className="h-4 w-5/6 max-w-2xl mx-auto mb-8" />
          
          {/* Stats badges */}
          <div className="flex flex-wrap justify-center gap-6 mb-8">
            <Skeleton className="h-12 w-40 rounded-lg" />
            <Skeleton className="h-12 w-40 rounded-lg" />
            <Skeleton className="h-12 w-40 rounded-lg" />
          </div>
        </div>

        {/* Program cards grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mx-auto">
          <ProgramCardSkeleton />
          <ProgramCardSkeleton />
        </div>
      </div>
    </section>
  );
}

export default TrainingProgramsSkeleton;
