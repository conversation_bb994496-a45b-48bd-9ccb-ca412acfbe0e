// SECTION: Imports
// ==================================================================================
import { Toaster } from "@/components/ui/toaster";
import { StagewiseToolbar } from "@stagewise/toolbar-react";
import ReactPlugin from "@stagewise-plugins/react";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, useLocation } from "react-router-dom";
import React, { Suspense, lazy, useEffect, useRef, useState } from "react";
import { HelmetProvider } from "react-helmet-async";
import NavigationProvider from "@/context/NavigationProvider";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { LoadingSpinner } from "@/components/ui/loading/LoadingSpinner";
import { queryClient } from "@/lib/queryClient";
import { usePrefetchRoutes } from "@/hooks/usePrefetchRoutes";

// Define the data types that will be prefetched
type Coach = { id: string; name: string };
type Program = { id: string; title: string };
// import Index from "./pages/Index"; // Removed static import

// import ClientManagement from "./pages/ClientManagement"; // Removed static import
// import CoachesPage from "./pages/CoachesPage"; // Removed static import
// import NotFound from "./pages/NotFound"; // Removed static import
// import GoalieTraining from "./pages/GoalieTraining"; // Removed static import
// import PlayerSkillsTraining from "./pages/PlayerSkillsTraining"; // Removed static import
// import MarketingPackageBuilderPage from "./pages/MarketingPackageBuilderPage"; // Removed static import
// import WixCalendar from "./components/WixCalendar"; // Removed static import
import { initGA, trackPageView } from "./utils/analytics.js";

import crowdSound from "@/assets/crowd-large-outrage-then-booing-reaction-hockey-game-2011-25915.mp3";



import { lazyWithPreload, type PreloadableComponent } from '@/utils/routeUtils';

// Lazy load page components with preloading support and chunk naming for better caching
// Using webpackChunkName comment to name chunks in a way that's compatible with both webpack and vite
const Index = lazyWithPreload(() => import(/* webpackChunkName: "Index" */ "./pages/Index"));
const ClientManagement = lazyWithPreload(() => import(/* webpackChunkName: "ClientManagement" */ "./pages/ClientManagement"));
const CoachesPage = lazyWithPreload(() => import(/* webpackChunkName: "CoachesPage" */ "./pages/CoachesPage"));
const NotFound = lazyWithPreload(() => import(/* webpackChunkName: "NotFound" */ "./pages/NotFound"));
const GoalieTraining = lazyWithPreload(() => import(/* webpackChunkName: "GoalieTraining" */ "./pages/GoalieTraining"));
const HockeyTraining = lazyWithPreload(() => import(/* webpackChunkName: "HockeyTraining" */ "./pages/HockeyTraining"));
const PlayerSkillsTraining = lazyWithPreload(() => import(/* webpackChunkName: "PlayerSkillsTraining" */ "./pages/PlayerSkillsTraining"));
const PopUpClinics = lazyWithPreload(() => import(/* webpackChunkName: "PopUpClinics" */ "./pages/PopUpClinics"));
const MarketingPackageBuilderPage = lazyWithPreload(() => import(/* webpackChunkName: "MarketingPackageBuilder" */ "./pages/MarketingPackageBuilderPage"));
const Dashboard = lazyWithPreload(() => import(/* webpackChunkName: "Dashboard" */ "./pages/admin/Dashboard"));
const SchedulingPage = lazyWithPreload(() => import(/* webpackChunkName: "SchedulingPage" */ "./pages/SchedulingPage"));
const FAQ = lazyWithPreload(() => import(/* webpackChunkName: "FAQ" */ "./pages/FAQ"));

type RouteComponent = {
  path: string;
  component: PreloadableComponent<React.ComponentType>;
};

// Define all route components
const routeComponents: RouteComponent[] = [
  { path: '/', component: Index },
  { path: '/client-management', component: ClientManagement },
  { path: '/coaches', component: CoachesPage },
  { path: '/goalie-training', component: GoalieTraining },
  { path: '/hockey-training', component: HockeyTraining },
  { path: '/player-skills-training', component: PlayerSkillsTraining },
  { path: '/pop-up-clinics', component: PopUpClinics },
  { path: '/marketing-package-builder', component: MarketingPackageBuilderPage },
  { path: '/admin/dashboard', component: Dashboard },
  { path: '/scheduling', component: SchedulingPage },
  { path: '/faq', component: FAQ },
];

// Function to preload a specific route component with error handling
const preloadRouteComponent = (path: string) => {
  try {
    const route = routeComponents.find(route => route.path === path);
    if (route) {
      // Use Promise.resolve to handle both synchronous and asynchronous preloading
      return Promise.resolve(route.component.preload()).catch(error => {
        // Silent catch to prevent errors from breaking the UI
        console.error(`Error preloading route ${path}:`, error);
        return null;
      });
    }
    return null;
  } catch (error) {
    console.error(`Error in preloadRouteComponent for ${path}:`, error);
    return null;
  }
};

// Preload critical routes in the background after initial render
const preloadCriticalRoutes = () => {
  // Use requestIdleCallback if available, otherwise setTimeout
  const schedulePreload = window.requestIdleCallback || ((cb) => setTimeout(cb, 1000));
  
  schedulePreload(() => {
    // Preload the most commonly accessed routes
    ['/', '/coaches', '/hockey-training'].forEach(path => {
      preloadRouteComponent(path);
    });
  });
};

// SECTION: Route Configuration
// ==================================================================================
// This configuration is used for route-based prefetching
const routeConfigs = [
  {
    path: '/coaches',
    prefetchKey: ['coaches'],
    prefetchQuery: async (): Promise<Coach[]> => {
      // Replace with actual API call when available
      // return fetch('/api/coaches').then(res => res.json());
      return Promise.resolve([]);
    }
  },
  {
    path: '/programs',
    prefetchKey: ['programs'],
    prefetchQuery: async (): Promise<Program[]> => {
      // Replace with actual API call when available
      // return fetch('/api/programs').then(res => res.json());
      return Promise.resolve([]);
    }
  }
  // Add more routes as needed
] as const satisfies Array<{
  path: string;
  prefetchKey: readonly string[];
  prefetchQuery: () => Promise<unknown>;
}>;

// SECTION: Google Analytics Configuration
// ==================================================================================
// Google Analytics measurement ID
const GA_MEASUREMENT_ID = "G-5HDKYBGC86";

// SECTION: AnalyticsTracker Component
// ==================================================================================
// Analytics tracker component with route preloading
const AnalyticsTracker = () => {
  const location = useLocation();
  const lastPath = useRef(location.pathname);
  
  useEffect(() => {

    
    // Track page views when location changes
    trackPageView(location.pathname + location.search);
    lastPath.current = location.pathname;
    
    // Preload the component for the next route when the user hovers over a link
    const handleMouseEnter = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const link = target.closest('a');
      
      if (link && link.pathname && link.pathname !== lastPath.current) {
        preloadRouteComponent(link.pathname);
      }
    };
    
    // Add event listeners to all internal links for preloading
    const links = document.querySelectorAll<HTMLAnchorElement>('a[href^="/"]');
    links.forEach(link => {
      // Only add listener if the link is an internal route
      if (routeComponents.some(route => route.path === link.pathname)) {
        link.addEventListener('mouseenter', handleMouseEnter, { once: true });
      }
    });
    
    return () => {
      links.forEach(link => {
        link.removeEventListener('mouseenter', handleMouseEnter);
      });
    };
  }, [location]);
  
  return null;
};

// SECTION: App Component (Main)
// ==================================================================================
// Main App component with Google Analytics initialization
const App = () => {
  // Initialize route prefetching
  usePrefetchRoutes(routeConfigs);
  
  // Effect for Google Analytics Initialization
  useEffect(() => {
    // Initialize Google Analytics once when the app loads
    initGA(GA_MEASUREMENT_ID);
    
    // Preload critical routes after initial render
    preloadCriticalRoutes();
  }, []);
  
  // --- Component JSX ---
  return (
    <HelmetProvider>
      {import.meta.env.MODE === 'development' && <StagewiseToolbar config={{ plugins: [ReactPlugin] }} />}
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          <NavigationProvider>
            <TooltipProvider>
              <ErrorBoundary
                fallback={
                  <div className="min-h-screen flex items-center justify-center p-6">
                    <div className="text-center max-w-md">
                      <h2 className="text-2xl font-bold text-elev-navy mb-4">Something went wrong</h2>
                      <p className="text-elev-text-secondary mb-6">
                        We're having trouble loading the page. Please try refreshing or come back later.
                      </p>
                      <button
                        onClick={() => window.location.reload()}
                        className="px-6 py-2 bg-elev-navy text-white rounded-md hover:bg-elev-navy/90 transition-colors"
                      >
                        Refresh Page
                      </button>
                    </div>
                  </div>
                }
              >
                <Suspense fallback={<LoadingSpinner />}>
                  <Routes>
                    {/* Map routes from configuration to avoid repetition */}
                    {routeComponents.map(({ path, component: Component }) => (
                      <Route 
                        key={path} 
                        path={path} 
                        element={
                          <Suspense fallback={<LoadingSpinner />}>
                            <Component />
                          </Suspense>
                        } 
                      />
                    ))}
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                </Suspense>
              </ErrorBoundary>
              <Toaster />
              <Sonner />
            </TooltipProvider>
            <AnalyticsTracker />
          </NavigationProvider>
        </BrowserRouter>
      </QueryClientProvider>
    </HelmetProvider>
  );
}

export default App;
