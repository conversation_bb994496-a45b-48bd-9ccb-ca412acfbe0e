/**
 * Design Token System Example
 * Demonstrates how to use the design token generator and validator
 */

import { DesignTokenGenerator } from '../design-tokens/generator.js';
import { DesignTokenValidator } from '../design-tokens/token-validator.js';

// Example usage of the design token system
export function demonstrateDesignTokenSystem() {
  console.log('🎨 Design Token System Demo\n');

  // 1. Generate comprehensive design token system
  const generator = new DesignTokenGenerator();
  const tokens = generator.generateTokenSystem();

  console.log('✅ Generated design token system with:');
  console.log(`   - ${Object.keys(tokens.colors.primary).length} primary color shades`);
  console.log(`   - ${Object.keys(tokens.typography.fontSizes).length} font sizes`);
  console.log(`   - ${Object.keys(tokens.spacing.scale).length} spacing values`);
  console.log(`   - Generated at: ${tokens.metadata.generatedAt}\n`);

  // 2. Export tokens in different formats
  console.log('📄 Exporting tokens in multiple formats:');
  
  // CSS Custom Properties
  const cssTokens = generator.exportTokens(tokens, 'css');
  console.log('   - CSS Custom Properties (first 3 lines):');
  console.log(cssTokens.split('\n').slice(0, 3).map(line => `     ${line}`).join('\n'));
  
  // JavaScript Module
  const jsTokens = generator.exportTokens(tokens, 'js');
  console.log('   - JavaScript Module (first line):');
  console.log(`     ${jsTokens.split('\n')[0]}\n`);

  // 3. Validate CSS against design tokens
  console.log('🔍 Validating CSS against design tokens:');
  
  const validator = new DesignTokenValidator(tokens);
  
  // Example CSS with violations
  const exampleCSS = `
    .button {
      background-color: #3b82f6;
      color: #ffffff;
      font-size: 14px;
      padding: 8px 16px;
      margin: 12px;
      font-weight: 600;
    }
    
    .card {
      background: rgb(255, 255, 255);
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 24px;
    }
  `;

  const colorViolations = validator.validateColorUsage(exampleCSS, 'example.css');
  const typographyViolations = validator.validateTypographyUsage(exampleCSS, 'example.css');
  const spacingViolations = validator.validateSpacingUsage(exampleCSS, 'example.css');

  const allViolations = [...colorViolations, ...typographyViolations, ...spacingViolations];
  
  console.log(`   Found ${allViolations.length} design token violations:`);
  allViolations.slice(0, 3).forEach((violation, index) => {
    console.log(`   ${index + 1}. ${violation.type.toUpperCase()}: ${violation.message}`);
    console.log(`      Suggestion: ${violation.suggestion}`);
  });

  // 4. Generate compliance report
  const complianceReport = validator.generateComplianceReport(allViolations, 10);
  console.log('\n📊 Compliance Report:');
  console.log(`   - Overall Compliance: ${complianceReport.overallCompliance}%`);
  console.log(`   - Color Compliance: ${complianceReport.colorCompliance}%`);
  console.log(`   - Typography Compliance: ${complianceReport.typographyCompliance}%`);
  console.log(`   - Spacing Compliance: ${complianceReport.spacingCompliance}%`);
  console.log(`   - Total Issues: ${complianceReport.totalIssues}`);
  console.log(`   - Critical Issues: ${complianceReport.criticalIssues}\n`);

  // 5. Show design token structure
  console.log('🏗️  Design Token Structure:');
  console.log('   Colors:');
  console.log(`     - Primary: ${Object.keys(tokens.colors.primary).length} shades`);
  console.log(`     - Semantic: ${Object.keys(tokens.colors.semantic).length} types`);
  console.log(`     - Surface: ${Object.keys(tokens.colors.surface).length} variants`);
  
  console.log('   Typography:');
  console.log(`     - Font Families: ${Object.keys(tokens.typography.fontFamilies).length} types`);
  console.log(`     - Type Scale: ${Object.keys(tokens.typography.typeScale).length} levels`);
  
  console.log('   Spacing:');
  console.log(`     - Scale: ${Object.keys(tokens.spacing.scale).length} values`);
  console.log(`     - Semantic: Component + Layout spacing`);

  return {
    tokens,
    violations: allViolations,
    compliance: complianceReport
  };
}

// Example of how to integrate with existing audit system
export function integrateWithAuditSystem() {
  const generator = new DesignTokenGenerator();
  const tokens = generator.generateTokenSystem();
  
  // This would be called by the main audit engine
  return {
    designTokens: tokens,
    validator: new DesignTokenValidator(tokens),
    exportFormats: ['json', 'css', 'scss', 'js'] as const
  };
}

// Run the demo if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  demonstrateDesignTokenSystem();
}