// Centralized program data for the website

export type SkillLevel = 'Beginner' | 'Intermediate' | 'Advanced' | 'All Levels';

export interface Testimonial {
  quote: string;
  author?: string; // Optional: Not all programs in current data have author/role
  role?: string;   // Optional
}

export interface Program {
  id: string;
  title: string;
  description: string;
  image: string; // Assuming string URLs as per current implementation
  features: string[];
  ageGroup: string;
  skillLevel: SkillLevel;
  priceInfo: string;
  testimonial?: Testimonial;
  // 'delay' will be generated dynamically in the component
}

const programs: Program[] = [
  {
    id: "individual-training",
    title: "Individual Training",
    description: "One-on-one sessions designed to accelerate skill development with highly personalized coaching tailored to your specific needs and goals.",
    image: "https://unlv-hockey.myshopify.com/cdn/shop/products/50785473752_54e2ccb132_c_1200x1200.jpg?v=1669231808",
    features: [
      "Highly personalized coaching and immediate feedback",
      "Accelerated skill development in specific areas",
      "Goal-specific training and confidence building",
      "Flexible scheduling to fit your availability"
    ],
    ageGroup: "All Ages",
    skillLevel: "All Levels",
    priceInfo: "Custom Pricing",
    testimonial: {
      quote: "After just three individual sessions, my daughter's skating technique improved dramatically. The personalized attention made all the difference!"
      // No author/role in original data for this one
    }
  },
  {
    id: "goalie-training",
    title: "Goalie Training",
    description: "Specialized training focused on developing elite goaltenders with technical skill development, positional awareness, and mental preparation techniques.",
    image: "https://cdn.hockeycanada.ca/hockey-canada/Hockey-Programs/Players/Goaltending/goaltending-01.jpg",
    features: [
      "Specialized goaltending techniques and movement patterns",
      "Rebound control and puck tracking drills",
      "Game situation simulation and decision making",
      "Mental preparation and confidence building",
      "Video analysis and technical feedback"
    ],
    ageGroup: "All Ages",
    skillLevel: "All Levels",
    priceInfo: "From $84.99/session",
    testimonial: {
      quote: "The goalie-specific training has transformed my son's confidence between the pipes. The technical focus on positioning and movement has made a huge difference in his save percentage.",
      // No author/role in original data
    }
  },
  {
    id: "small-group-skills",
    title: "Small Group Skills",
    description: "Our small group skills sessions offer personalized attention in a collaborative environment, with a maximum of 10 skaters to ensure quality instruction.",
    image: "https://images.unsplash.com/photo-1706844587032-5fce18891d59?q=80&w=2340&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    features: [
      "Specialized skill development",
      "Position-specific training",
      "Progressive skill advancement",
      "Performance tracking",
      "All skill levels welcome"
    ],
    ageGroup: "All Ages",
    skillLevel: "All Levels",
    priceInfo: "From $49.99/session",
    testimonial: {
      quote: "The small group format is perfect - enough individual attention while keeping the costs low. My son is still learning the basics of hockey. Great value!",
      // No author/role
    }
  },
  {
    id: "adult-hockey-skills",
    title: "Adult Hockey Skills",
    description: "Designed for adult players looking to improve their game, these sessions focus on skill development and conditioning in a supportive environment.",
    image: "https://images.unsplash.com/photo-1632570700553-2ee85fe474d9?q=80&w=2340&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    features: [
      "Focus on fundamentals and game situations",
      "Small groups designed to allow for individualized instruction",
      "Conditioning and injury prevention",
      "Flexible scheduling for working adults",
      "All skill levels welcome"
    ],
    ageGroup: "Adults 18+",
    skillLevel: "All Levels",
    priceInfo: "From $49.99/session",
    testimonial: {
      quote: "I started playing hockey later in life and these sessions have been perfect for developing my skills. The coaches understand how to teach adults effectively.",
      // No author/role
    }
  },
  {
    id: "team-training",
    title: "Team Training",
    description: "Customized training programs for entire teams, focusing on systems implementation, position-specific skills, and team cohesion.",
    image: "https://media.lasvegassun.com/media/img/photos/2025/03/12/20250306_sun_HighSchoolHockey_BGFaith07_t650.JPG?5711a3b57decb389a12ba40e20471e031ff69545",
    features: [
      "Customized practice plans",
      "Systems implementation",
      "Team-building exercises",
      "Special teams training",
      "Game situation simulations"
    ],
    ageGroup: "All Teams",
    skillLevel: "All Levels",
    priceInfo: "Custom Pricing",
    testimonial: {
      quote: "Our team chemistry improved dramatically. The coaches built a program that addressed our specific weaknesses while building on our strengths.",
      // No author/role
    }
  },
  {
    id: "private-rentals",
    title: "Private Rentals",
    description: "Book our facility for private training sessions, team practices, or special events with access to our state-of-the-art equipment.",
    image: "https://images.unsplash.com/photo-1515703407324-5f753afd8be8?q=80&w=2000",
    features: [
      "Exclusive facility access",
      "Professional equipment available",
      "Flexible scheduling options",
      "Optional coaching add-ons",
      "Perfect for teams or groups"
    ],
    ageGroup: "All Ages",
    skillLevel: "All Levels",
    priceInfo: "From $249/hour",
    testimonial: {
      quote: "We rented the facility for our son's birthday, and it was incredible. The staff went above and beyond to make it special.",
      // No author/role
    }
  }
];

export default programs;
