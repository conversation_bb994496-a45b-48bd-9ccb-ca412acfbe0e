import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { CalendarDays, Clock, Edit, Plus, RefreshCw, Users, ChevronDown, ChevronUp } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { getAllCoaches, getGoalieCoaches, getPlayerCoaches } from "@/data/coaches";

const Dashboard = () => {
  const [loading, setLoading] = useState(false);
  const [collapsedSections, setCollapsedSections] = useState<Record<string, boolean>>({});
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  const refreshData = () => {
    setLoading(true);
    // Simulate data refresh
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  const toggleSection = (sectionId: string) => {
    setCollapsedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  const coaches = getAllCoaches();
  const playerCoaches = getPlayerCoaches();
  const goalieCoaches = getGoalieCoaches();

  // Mock data for dashboard stats
  const stats = [
    {
      title: "Total Coaches",
      value: coaches.length,
      icon: <Users className="h-5 w-5 text-blue-600" />,
      change: "+2 this month",
      color: "bg-blue-50 text-blue-600",
    },
    {
      title: "Player Coaches",
      value: playerCoaches.length,
      icon: <Users className="h-5 w-5 text-green-600" />,
      change: `${((playerCoaches.length / coaches.length) * 100).toFixed(0)}% of total`,
      color: "bg-green-50 text-green-600",
    },
    {
      title: "Goalie Coaches",
      value: goalieCoaches.length,
      icon: <Users className="h-5 w-5 text-purple-600" />,
      change: `${((goalieCoaches.length / coaches.length) * 100).toFixed(0)}% of total`,
      color: "bg-purple-50 text-purple-600",
    },
    {
      title: "Training Sessions",
      value: 48,
      icon: <CalendarDays className="h-5 w-5 text-amber-600" />,
      change: "Next 7 days",
      color: "bg-amber-50 text-amber-600",
    },
  ];

  // Mock data for recent activity
  const recentActivity = [
    {
      action: "Coach Added",
      subject: "John Smith",
      timestamp: "Today, 10:30 AM",
      user: "Admin",
    },
    {
      action: "Session Updated",
      subject: "Goalie Training - May 6",
      timestamp: "Yesterday, 3:45 PM",
      user: "Chris Cobb",
    },
    {
      action: "Schedule Modified",
      subject: "Team Training Sessions",
      timestamp: "May 3, 2025, 11:20 AM",
      user: "Admin",
    },
  ];

  return (
    <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
          <Button
            variant="outline"
            size="sm"
            onClick={refreshData}
            disabled={loading}
          >
            {loading ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Refresh Data
          </Button>
        </div>

        {/* Stats Cards - Responsive Grid: 1 col mobile → 2 col tablet → 4 col desktop */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-4 md:p-6">
                <div className="flex justify-between items-start">
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-500 truncate">
                      {stat.title}
                    </p>
                    <h3 className="text-2xl md:text-3xl font-bold mt-1">{stat.value}</h3>
                    <p className="text-xs text-gray-500 mt-1 truncate">{stat.change}</p>
                  </div>
                  <div className={`p-2 rounded-full ${stat.color} flex-shrink-0 ml-2`}>
                    {stat.icon}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content Tabs - Optimized for smaller screens */}
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3 md:w-auto md:grid-cols-none md:flex">
            <TabsTrigger value="overview" className="text-xs md:text-sm">Overview</TabsTrigger>
            <TabsTrigger value="activity" className="text-xs md:text-sm">Activity</TabsTrigger>
            <TabsTrigger value="quickActions" className="text-xs md:text-sm">Actions</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* Collapsible section for mobile - Upcoming Training Sessions */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Upcoming Training Sessions</CardTitle>
                    <CardDescription className="hidden md:block">
                      Training sessions scheduled for the next 7 days
                    </CardDescription>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="md:hidden"
                    onClick={() => toggleSection('upcomingSessions')}
                  >
                    {collapsedSections['upcomingSessions'] ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronUp className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </CardHeader>
              {(!collapsedSections['upcomingSessions'] || !isMobile) && (
                <CardContent>
                  {/* Horizontal scrolling container for mobile */}
                  <div className="overflow-x-auto">
                    <div className="space-y-4 min-w-full">
                      {/* Mock upcoming sessions */}
                      {[1, 2, 3].map((_, index) => (
                        <div
                          key={index}
                          className="flex justify-between items-center p-3 bg-gray-50 rounded-md min-w-0"
                        >
                          <div className="flex items-center min-w-0 flex-1">
                            <div className="bg-blue-100 p-2 rounded-full mr-3 flex-shrink-0">
                              <Clock className="h-5 w-5 text-blue-600" />
                            </div>
                            <div className="min-w-0 flex-1">
                              <h4 className="font-medium truncate">
                                {index === 0
                                  ? "Individual Training"
                                  : index === 1
                                  ? "Goalie Training"
                                  : "Team Training"}
                              </h4>
                              <p className="text-sm text-gray-500 truncate">
                                {index === 0
                                  ? "Today, 4:00 PM - John Siemer"
                                  : index === 1
                                  ? "Tomorrow, 10:00 AM - Chris Cobb"
                                  : "May 8, 6:00 PM - Gabe Testa"}
                              </p>
                            </div>
                          </div>
                          <Button variant="ghost" size="sm" className="flex-shrink-0 ml-2">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Collapsible Coach Availability Card */}
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle>Coach Availability</CardTitle>
                      <CardDescription className="hidden md:block">
                        Current coach availability status
                      </CardDescription>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="md:hidden"
                      onClick={() => toggleSection('coachAvailability')}
                    >
                      {collapsedSections['coachAvailability'] ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronUp className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </CardHeader>
                {(!collapsedSections['coachAvailability'] || !isMobile) && (
                  <CardContent>
                    {/* Horizontal scrolling for mobile data table */}
                    <div className="overflow-x-auto">
                      <div className="space-y-3 min-w-[280px]">
                        {coaches.slice(0, 5).map((coach, index) => (
                          <div
                            key={index}
                            className="flex justify-between items-center min-w-0"
                          >
                            <div className="flex items-center min-w-0 flex-1">
                              <div
                                className={`w-2 h-2 rounded-full ${
                                  index % 3 === 0
                                    ? "bg-green-500"
                                    : index % 3 === 1
                                    ? "bg-amber-500"
                                    : "bg-red-500"
                                } mr-2 flex-shrink-0`}
                              ></div>
                              <span className="truncate">{coach.name}</span>
                            </div>
                            <span className="text-sm text-gray-500 flex-shrink-0 ml-2">
                              {index % 3 === 0
                                ? "Available"
                                : index % 3 === 1
                                ? "Limited"
                                : "Unavailable"}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                )}
              </Card>

              {/* Collapsible Session Statistics Card */}
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle>Session Statistics</CardTitle>
                      <CardDescription className="hidden md:block">
                        Training session breakdown by type
                      </CardDescription>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="md:hidden"
                      onClick={() => toggleSection('sessionStats')}
                    >
                      {collapsedSections['sessionStats'] ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronUp className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </CardHeader>
                {(!collapsedSections['sessionStats'] || !isMobile) && (
                  <CardContent>
                    {/* Horizontal scrolling for mobile data */}
                    <div className="overflow-x-auto">
                      <div className="space-y-4 min-w-[250px]">
                        {/* Mock session stats */}
                        {[
                          {
                            type: "Individual Training",
                            count: 24,
                            percentage: 50,
                            color: "bg-blue-500",
                          },
                          {
                            type: "Goalie Training",
                            count: 12,
                            percentage: 25,
                            color: "bg-red-500",
                          },
                          {
                            type: "Team Training",
                            count: 8,
                            percentage: 17,
                            color: "bg-green-500",
                          },
                          {
                            type: "Skills Development",
                            count: 4,
                            percentage: 8,
                            color: "bg-purple-500",
                          },
                        ].map((stat, index) => (
                          <div key={index} className="space-y-1">
                            <div className="flex justify-between text-sm">
                              <span className="truncate">{stat.type}</span>
                              <span className="flex-shrink-0 ml-2">
                                {stat.count} ({stat.percentage}%)
                              </span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className={`${stat.color} h-2 rounded-full`}
                                style={{ width: `${stat.percentage}%` }}
                              ></div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                )}
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="activity">
            {/* Collapsible Recent Activity Card */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Recent Activity</CardTitle>
                    <CardDescription className="hidden md:block">
                      Latest actions performed in the admin system
                    </CardDescription>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="md:hidden"
                    onClick={() => toggleSection('recentActivity')}
                  >
                    {collapsedSections['recentActivity'] ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronUp className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </CardHeader>
              {(!collapsedSections['recentActivity'] || !isMobile) && (
                <CardContent>
                  {/* Horizontal scrolling for mobile activity list */}
                  <div className="overflow-x-auto">
                    <div className="space-y-4 min-w-[320px]">
                      {recentActivity.map((activity, index) => (
                        <div
                          key={index}
                          className="flex items-start border-b border-gray-100 pb-4 last:border-0 last:pb-0 min-w-0"
                        >
                          <div
                            className={`p-2 rounded-full mr-3 flex-shrink-0 ${
                              activity.action.includes("Added")
                                ? "bg-green-100 text-green-600"
                                : activity.action.includes("Updated") ||
                                  activity.action.includes("Modified")
                                ? "bg-blue-100 text-blue-600"
                                : "bg-amber-100 text-amber-600"
                            }`}
                          >
                            {activity.action.includes("Coach") ? (
                              <Users className="h-4 w-4" />
                            ) : (
                              <CalendarDays className="h-4 w-4" />
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex justify-between items-start">
                              <h4 className="font-medium truncate pr-2">{activity.action}</h4>
                              <span className="text-xs text-gray-500 flex-shrink-0">
                                {activity.timestamp}
                              </span>
                            </div>
                            <p className="text-sm truncate">{activity.subject}</p>
                            <p className="text-xs text-gray-500">
                              by {activity.user}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
          </TabsContent>

          <TabsContent value="quickActions">
            {/* Collapsible Quick Actions Card */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Quick Actions</CardTitle>
                    <CardDescription className="hidden md:block">
                      Common administrative tasks
                    </CardDescription>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="md:hidden"
                    onClick={() => toggleSection('quickActions')}
                  >
                    {collapsedSections['quickActions'] ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronUp className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </CardHeader>
              {(!collapsedSections['quickActions'] || !isMobile) && (
                <CardContent>
                  {/* Responsive grid for quick action buttons */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Button className="h-auto py-4 justify-start w-full">
                      <div className="flex flex-col items-start text-left w-full">
                        <div className="flex items-center">
                          <Plus className="h-5 w-5 mr-2 flex-shrink-0" />
                          <span className="font-medium">Add New Coach</span>
                        </div>
                        <span className="text-xs mt-1 pl-7 text-left">
                          Create a new coach profile
                        </span>
                      </div>
                    </Button>

                    <Button className="h-auto py-4 justify-start w-full">
                      <div className="flex flex-col items-start text-left w-full">
                        <div className="flex items-center">
                          <CalendarDays className="h-5 w-5 mr-2 flex-shrink-0" />
                          <span className="font-medium">Add Training Session</span>
                        </div>
                        <span className="text-xs mt-1 pl-7 text-left">
                          Schedule a new training session
                        </span>
                      </div>
                    </Button>

                    <Button className="h-auto py-4 justify-start w-full" variant="outline">
                      <div className="flex flex-col items-start text-left w-full">
                        <div className="flex items-center">
                          <Edit className="h-5 w-5 mr-2 flex-shrink-0" />
                          <span className="font-medium">Edit Schedule Template</span>
                        </div>
                        <span className="text-xs mt-1 pl-7 text-left">
                          Modify the default schedule template
                        </span>
                      </div>
                    </Button>

                    <Button className="h-auto py-4 justify-start w-full" variant="outline">
                      <div className="flex flex-col items-start text-left w-full">
                        <div className="flex items-center">
                          <Users className="h-5 w-5 mr-2 flex-shrink-0" />
                          <span className="font-medium">Manage Permissions</span>
                        </div>
                        <span className="text-xs mt-1 pl-7 text-left">
                          Update user roles and permissions
                        </span>
                      </div>
                    </Button>
                  </div>
                </CardContent>
              )}
            </Card>
          </TabsContent>
        </Tabs>
      </div>
  );
};

export default Dashboard;
