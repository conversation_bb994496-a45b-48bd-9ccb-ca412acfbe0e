import LoadingLink from "@/components/ui/LoadingLink";

interface BookNowButtonProps {
  className?: string;
  children?: React.ReactNode;
  variant?: "default" | "green" | "white";
}

/**
 * A specialized button component for booking/scheduling actions
 * that shows a loading indicator when clicked before navigating
 */
const BookNowButton = ({ 
  className = "", 
  children = "BOOK NOW", 
  variant = "default" 
}: BookNowButtonProps) => {
  // Determine the appropriate styling based on the variant
  let buttonStyle = "";
  
  switch(variant) {
    case "green":
      buttonStyle = "bg-[#0ab43b] text-white hover:bg-[#099a33] transition-all transform hover:scale-105";
      break;
    case "white":
      buttonStyle = "bg-white text-blue-900 hover:bg-elev-background-muted transition-colors";
      break;
    default:
      buttonStyle = "bg-elev-navy hover:bg-elev-navy/80 text-white";
      break;
  }

  return (
    <LoadingLink
      to="/scheduling"
      className={`${buttonStyle} px-8 py-3 rounded-lg font-semibold inline-block ${className}`}
    >
      {children}
    </LoadingLink>
  );
};

export default BookNowButton;
