.glitch-wrapper {
  position: relative;
  display: inline-block;
}

.glitch {
  position: relative;
  color: white;
  font-weight: bold;
  z-index: 1;
  /* Add black outline for text */
  -webkit-text-stroke: 2px #000;
  text-stroke: 2px #000;
  paint-order: stroke fill;
}

.glitch::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 55%;
  width: 60%;
  height: 40%;
  transform: translate(-50%, -50%) scaleY(0.8);
  background: none;
  pointer-events: none;
  z-index: 2;
  background-image: url('data:image/svg+xml;utf8,<svg width="120" height="40" viewBox="0 0 120 40" fill="none" xmlns="http://www.w3.org/2000/svg"><polyline points="5,5 30,20 15,20 45,35 35,20 65,20 50,35 80,5 60,17 100,5" stroke="yellow" stroke-width="4" stroke-linejoin="round" stroke-linecap="round" filter="url(%23flicker)"/><filter id="flicker"><feGaussianBlur stdDeviation="1.5" result="blur"/><feMerge><feMergeNode in="blur"/><feMergeNode in="SourceGraphic"/></feMerge></filter></svg>');
  background-repeat: no-repeat;
  background-size: contain;
  opacity: 0.85;
  animation: lightning-flash 2.5s infinite cubic-bezier(0.4,0,0.2,1);
}

@keyframes lightning-flash {
  0%, 97%, 100% {
    opacity: 0;
    filter: blur(0px) brightness(1);
    transform: translate(-50%, -50%) scaleY(0.8) scaleX(1);
  }
  2% {
    opacity: 1;
    filter: blur(2px) brightness(2);
    transform: translate(-50%, -50%) scaleY(1.05) scaleX(1.1) rotate(-2deg);
  }
  3% {
    opacity: 0.8;
    filter: blur(1px) brightness(1.8);
    transform: translate(-50%, -50%) scaleY(0.95) scaleX(1.05) rotate(2deg);
  }
  4% {
    opacity: 1;
    filter: blur(1.5px) brightness(2.2);
    transform: translate(-50%, -50%) scaleY(1.1) scaleX(1.12) rotate(-1deg);
  }
  5% {
    opacity: 0;
    filter: blur(0px) brightness(1);
    transform: translate(-50%, -50%) scaleY(0.8) scaleX(1);
  }
}
