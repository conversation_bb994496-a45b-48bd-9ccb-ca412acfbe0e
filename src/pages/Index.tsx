
import React, { useEffect } from "react";
import type { <PERSON> } from 'react';
import { Helmet } from 'react-helmet-async';
import Hero from "@/components/Hero";
import WhyChooseUs from "@/components/WhyChooseUs";
import Facility from "@/components/Facility";
import TrainingPrograms from "@/components/TrainingPrograms";
import Coaches from "@/components/Coaches";
import Testimonials from "@/components/Testimonials";
import ContactForm from "@/components/ContactForm";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import GoogleMap from "@/components/GoogleMap";
import LazyInstagramEmbed from "@/components/InstagramEmbed";
import FacebookEmbed from "@/components/FacebookEmbed";
import { useLocation, useNavigate } from "react-router-dom";
import { LocalBusinessSchema } from "@/utils/schema";
import SEO from "@/components/SEO";

const Index: FC = () => {
  const location = useLocation();
  const navigate = useNavigate(); // Initialize useNavigate

  useEffect(() => {
    // Page title is now managed by <PERSON><PERSON><PERSON>

    // Scroll to contact form if state is present
    if (location.state?.scrollToContact) {
      const contactForm = document.getElementById('contact-form');
      if (contactForm) {
        contactForm.scrollIntoView({ behavior: 'smooth' });
        // Clear the state after scrolling to prevent it on subsequent loads/refreshes
        navigate(location.pathname, { replace: true, state: { ...location.state, scrollToContact: false } });
      }
    }
    // Removed general IntersectionObserver for ".reveal" elements.
    // Components like Coaches, TrainingPrograms, ContactForm now use the
    // useRevealOnScroll hook to manage their own reveal animations.
    // Other page sections (WhyChooseUs, Facility, Testimonials, etc.)
    // will need to be individually updated with useRevealOnScroll if
    // reveal animations are desired for them, promoting a more consistent
    // and component-scoped animation approach.
  }, [location.state, location.pathname, navigate]); // Add location.pathname and navigate to dependencies

  return (
    <div className="flex flex-col min-h-screen">
      <SEO
        title="ELEV802 | 702HOCKEY - Premier Hockey Training in Las Vegas"
        description="ELEV802's premier hockey training facility in Las Vegas offers expert coaching, individualized programs, and a state-of-the-art facility with year-round custom ice designed for hockey athletes."
        keywords="ELEV802, hockey training, Las Vegas hockey, ice hockey coaching, hockey skills development, 702Hockey, youth hockey training"
        canonical="/"
        image="/images/facility.jpg"
      />
      
      {/* JSON-LD Schema for Local Business */}
      <LocalBusinessSchema
        name="ELEV802 | 702HOCKEY"
        description="ELEV802's premier hockey training facility in Las Vegas with expert coaching, individualized programs, and a state-of-the-art facility."
        telephone="+***********"
        address={{
          streetAddress: "5031 Wagon Trail Ave STE 100",
          addressLocality: "Las Vegas",
          addressRegion: "NV",
          postalCode: "89118",
          addressCountry: "US"
        }}
        geo={{
          latitude: 36.215337,
          longitude: -115.122916
        }}
        image="/images/facility.jpg"
        url="https://702hockey.com"
        priceRange="$$$"
        openingHours={["Mo-Su 07:00-22:00"]}
      />
      
      <Header />
      <main id="main-content" className="flex-grow" tabIndex={-1}>
        <Hero />
        <section className="bg-gradient-to-b from-gray-100 via-gray-50 to-white" aria-labelledby="why-choose-us-heading" id="why-choose-us">
          <WhyChooseUs />
        </section>
        <section className="bg-gradient-to-b from-gray-100 via-gray-50 to-white" aria-labelledby="facility-heading" id="facility">
          <Facility />
        </section>
        <section className="bg-gradient-to-b from-gray-100 via-gray-50 to-white" aria-label="Location Map">
          <GoogleMap />
        </section>
        <section className="bg-gradient-to-b from-gray-100 via-gray-50 to-white" aria-labelledby="programs-heading" id="programs">
          <TrainingPrograms />
        </section>
        <section className="bg-gradient-to-b from-gray-100 via-gray-50 to-white" aria-labelledby="coaches-heading" id="coaches">
          <Coaches />
        </section>
        <section className="bg-gradient-to-b from-gray-100 via-gray-50 to-white" aria-labelledby="testimonials-heading" id="testimonials">
          <Testimonials />
        </section>
        <section className="bg-gradient-to-b from-gray-100 via-gray-50 to-white py-16 mt-8 md:mt-10 lg:mt-12" aria-labelledby="social-media-heading">
          <div className="container mx-auto px-4 text-center">
            <h2 id="social-media-heading" className="text-3xl font-bold mb-8">Follow Us on Social Media</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
              <div className="flex flex-col items-center">
                <h3 id="instagram-heading" className="text-2xl font-semibold mb-4">Instagram</h3>
                <div className="flex justify-center" aria-labelledby="instagram-heading">
                  <LazyInstagramEmbed />
                </div>
              </div>
              <div className="flex flex-col items-center">
                <h3 id="facebook-heading" className="text-2xl font-semibold mb-4">Facebook</h3>
                <div className="flex justify-center" aria-labelledby="facebook-heading">
                  <FacebookEmbed />
                </div>
              </div>
            </div>
          </div>
        </section>
        <section className="bg-gradient-to-b from-gray-100 via-gray-50 to-white" aria-labelledby="contact-form-heading" id="contact-form">
          <ContactForm />
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default Index;
