// Typography standardization utilities for converting hardcoded typography to design tokens
// and implementing typography hierarchy validation and correction

import { FileInfo, Issue, IssueType, Severity, Category, FileLocation } from '../types';
import { auditLogger } from '../core/logger';

export interface TypographyToken {
  name: string;
  value: string;
  category: 'fontSize' | 'fontFamily' | 'fontWeight' | 'lineHeight' | 'letterSpacing';
  description?: string;
}

export interface TypographyScale {
  fontSize: Record<string, string>;
  fontFamily: Record<string, string>;
  fontWeight: Record<string, string>;
  lineHeight: Record<string, string>;
  letterSpacing: Record<string, string>;
}

export interface TypographyHierarchyRule {
  element: string;
  fontSize: string;
  lineHeight: string;
  fontWeight: string;
  letterSpacing?: string;
}

export interface TypographyFix {
  filePath: string;
  lineNumber: number;
  columnStart: number;
  columnEnd: number;
  originalValue: string;
  replacementValue: string;
  tokenName: string;
  fixType: 'hardcoded-to-token' | 'hierarchy-correction' | 'line-height-standardization';
}

export interface TypographyValidationResult {
  isValid: boolean;
  violations: TypographyHierarchyViolation[];
  suggestions: string[];
}

export interface TypographyHierarchyViolation {
  element: string;
  property: string;
  currentValue: string;
  expectedValue: string;
  location: FileLocation;
  severity: Severity;
}

export class TypographyStandardizationUtilities {
  private typographyScale: TypographyScale;
  private hierarchyRules: TypographyHierarchyRule[];
  private commonLineHeights: number[] = [1, 1.2, 1.25, 1.3, 1.4, 1.5, 1.6, 1.75, 2];
  private standardFontSizes: Record<string, string> = {
    'xs': '0.75rem',
    'sm': '0.875rem',
    'base': '1rem',
    'lg': '1.125rem',
    'xl': '1.25rem',
    '2xl': '1.5rem',
    '3xl': '1.875rem',
    '4xl': '2.25rem',
    '5xl': '3rem',
    '6xl': '3.75rem'
  };

  constructor() {
    this.typographyScale = this.initializeDefaultTypographyScale();
    this.hierarchyRules = this.initializeDefaultHierarchyRules();
  }

  /**
   * Convert hardcoded typography values to design tokens
   */
  async convertHardcodedToTokens(files: FileInfo[]): Promise<TypographyFix[]> {
    const fixes: TypographyFix[] = [];
    
    auditLogger.info('Starting hardcoded typography to token conversion', { 
      fileCount: files.length 
    });

    for (const file of files) {
      try {
        const fileFixes = await this.processFileForTokenConversion(file);
        fixes.push(...fileFixes);
      } catch (error) {
        auditLogger.error('Failed to process file for token conversion', error as Error, {
          filePath: file.path
        });
      }
    }

    auditLogger.info('Completed hardcoded typography conversion', { 
      fixesGenerated: fixes.length 
    });

    return fixes;
  }

  /**
   * Validate typography hierarchy and generate corrections
   */
  async validateTypographyHierarchy(files: FileInfo[]): Promise<TypographyValidationResult> {
    const violations: TypographyHierarchyViolation[] = [];
    const suggestions: string[] = [];

    auditLogger.info('Starting typography hierarchy validation', { 
      fileCount: files.length 
    });

    for (const file of files) {
      try {
        const fileViolations = await this.validateFileHierarchy(file);
        violations.push(...fileViolations);
      } catch (error) {
        auditLogger.error('Failed to validate file hierarchy', error as Error, {
          filePath: file.path
        });
      }
    }

    // Generate suggestions based on violations
    if (violations.length > 0) {
      suggestions.push(
        `Found ${violations.length} typography hierarchy violations`,
        'Consider implementing a consistent typography scale',
        'Use semantic heading elements (h1-h6) with appropriate sizing',
        'Establish clear visual hierarchy with consistent font sizes and weights'
      );
    }

    const result: TypographyValidationResult = {
      isValid: violations.length === 0,
      violations,
      suggestions
    };

    auditLogger.info('Completed typography hierarchy validation', { 
      violations: violations.length,
      isValid: result.isValid
    });

    return result;
  }

  /**
   * Check and correct line-height consistency
   */
  async standardizeLineHeights(files: FileInfo[]): Promise<TypographyFix[]> {
    const fixes: TypographyFix[] = [];

    auditLogger.info('Starting line-height standardization', { 
      fileCount: files.length 
    });

    for (const file of files) {
      try {
        const fileFixes = await this.processFileForLineHeightStandardization(file);
        fixes.push(...fileFixes);
      } catch (error) {
        auditLogger.error('Failed to process file for line-height standardization', error as Error, {
          filePath: file.path
        });
      }
    }

    auditLogger.info('Completed line-height standardization', { 
      fixesGenerated: fixes.length 
    });

    return fixes;
  }

  /**
   * Generate design token system from existing typography usage
   */
  generateDesignTokenSystem(files: FileInfo[]): TypographyScale {
    const extractedValues = this.extractTypographyValues(files);
    const optimizedScale = this.optimizeTypographyScale(extractedValues);
    
    // Merge with default typography scale to ensure we always have base tokens
    const mergedScale: TypographyScale = {
      fontSize: { ...this.typographyScale.fontSize, ...optimizedScale.fontSize },
      fontFamily: { ...this.typographyScale.fontFamily, ...optimizedScale.fontFamily },
      fontWeight: { ...this.typographyScale.fontWeight, ...optimizedScale.fontWeight },
      lineHeight: { ...this.typographyScale.lineHeight, ...optimizedScale.lineHeight },
      letterSpacing: { ...this.typographyScale.letterSpacing, ...optimizedScale.letterSpacing }
    };
    
    auditLogger.info('Generated design token system', {
      fontSizes: Object.keys(mergedScale.fontSize).length,
      fontFamilies: Object.keys(mergedScale.fontFamily).length,
      fontWeights: Object.keys(mergedScale.fontWeight).length,
      lineHeights: Object.keys(mergedScale.lineHeight).length
    });

    return mergedScale;
  }

  /**
   * Apply typography fixes to files
   */
  async applyTypographyFixes(fixes: TypographyFix[]): Promise<{ success: boolean; appliedFixes: number; errors: string[] }> {
    const errors: string[] = [];
    let appliedFixes = 0;

    auditLogger.info('Starting to apply typography fixes', { 
      totalFixes: fixes.length 
    });

    // Group fixes by file for efficient processing
    const fixesByFile = this.groupFixesByFile(fixes);

    for (const [filePath, fileFixes] of fixesByFile.entries()) {
      try {
        const success = await this.applyFixesToFile(filePath, fileFixes);
        if (success) {
          appliedFixes += fileFixes.length;
        } else {
          errors.push(`Failed to apply fixes to ${filePath}`);
        }
      } catch (error) {
        const errorMessage = `Error applying fixes to ${filePath}: ${(error as Error).message}`;
        errors.push(errorMessage);
        auditLogger.error('Failed to apply fixes to file', error as Error, {
          filePath
        });
      }
    }

    const result = {
      success: errors.length === 0,
      appliedFixes,
      errors
    };

    auditLogger.info('Completed applying typography fixes', result);

    return result;
  }

  private async processFileForTokenConversion(file: FileInfo): Promise<TypographyFix[]> {
    const fixes: TypographyFix[] = [];
    const lines = file.content.split('\n');

    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex];
      const lineNumber = lineIndex + 1;

      // Skip comments
      if (this.isCommentLine(line)) {
        continue;
      }

      // Find hardcoded font sizes
      const fontSizeFixes = this.findFontSizeTokenOpportunities(line, lineNumber, file.path);
      fixes.push(...fontSizeFixes);

      // Find hardcoded font families
      const fontFamilyFixes = this.findFontFamilyTokenOpportunities(line, lineNumber, file.path);
      fixes.push(...fontFamilyFixes);

      // Find hardcoded font weights
      const fontWeightFixes = this.findFontWeightTokenOpportunities(line, lineNumber, file.path);
      fixes.push(...fontWeightFixes);
    }

    return fixes;
  }

  private async validateFileHierarchy(file: FileInfo): Promise<TypographyHierarchyViolation[]> {
    const violations: TypographyHierarchyViolation[] = [];
    const lines = file.content.split('\n');

    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex];
      const lineNumber = lineIndex + 1;

      // Find heading elements
      const headingMatches = this.findHeadingElements(line, lineNumber, file.path);
      
      for (const heading of headingMatches) {
        const rule = this.hierarchyRules.find(r => r.element === heading.element);
        if (rule) {
          const hierarchyViolations = this.validateHeadingAgainstRule(heading, rule);
          violations.push(...hierarchyViolations);
        }
      }
    }

    return violations;
  }

  private async processFileForLineHeightStandardization(file: FileInfo): Promise<TypographyFix[]> {
    const fixes: TypographyFix[] = [];
    const lines = file.content.split('\n');

    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex];
      const lineNumber = lineIndex + 1;

      // Find line-height declarations
      const lineHeightMatches = this.findLineHeightDeclarations(line, lineNumber, file.path);
      
      for (const match of lineHeightMatches) {
        const standardizedValue = this.getStandardizedLineHeight(match.value);
        if (standardizedValue && standardizedValue !== match.value) {
          fixes.push({
            filePath: file.path,
            lineNumber,
            columnStart: match.columnStart,
            columnEnd: match.columnEnd,
            originalValue: match.value,
            replacementValue: standardizedValue,
            tokenName: this.getLineHeightTokenName(standardizedValue),
            fixType: 'line-height-standardization'
          });
        }
      }
    }

    return fixes;
  }

  private findFontSizeTokenOpportunities(line: string, lineNumber: number, filePath: string): TypographyFix[] {
    const fixes: TypographyFix[] = [];
    
    // CSS font-size declarations
    const cssPattern = /font-size\s*:\s*(\d+(?:\.\d+)?)(px|rem|em)/g;
    let match: RegExpExecArray | null;
    
    while ((match = cssPattern.exec(line)) !== null) {
      const value = match[1];
      const unit = match[2];
      const fullValue = `${value}${unit}`;
      
      const tokenName = this.findBestFontSizeToken(fullValue);
      if (tokenName) {
        fixes.push({
          filePath,
          lineNumber,
          columnStart: match.index,
          columnEnd: match.index + match[0].length,
          originalValue: fullValue,
          replacementValue: `var(--font-size-${tokenName})`,
          tokenName: `font-size-${tokenName}`,
          fixType: 'hardcoded-to-token'
        });
      }
    }

    // React inline styles
    const reactPattern = /fontSize\s*:\s*['"`](\d+(?:\.\d+)?)(px|rem|em)['"`]/g;
    while ((match = reactPattern.exec(line)) !== null) {
      const value = match[1];
      const unit = match[2];
      const fullValue = `${value}${unit}`;
      
      const tokenName = this.findBestFontSizeToken(fullValue);
      if (tokenName) {
        fixes.push({
          filePath,
          lineNumber,
          columnStart: match.index,
          columnEnd: match.index + match[0].length,
          originalValue: `fontSize: '${fullValue}'`,
          replacementValue: `fontSize: 'var(--font-size-${tokenName})'`,
          tokenName: `font-size-${tokenName}`,
          fixType: 'hardcoded-to-token'
        });
      }
    }

    return fixes;
  }

  private findFontFamilyTokenOpportunities(line: string, lineNumber: number, filePath: string): TypographyFix[] {
    const fixes: TypographyFix[] = [];
    
    // CSS font-family declarations with custom fonts
    const cssPattern = /font-family\s*:\s*['"`]([^'"`]+)['"`]/g;
    let match: RegExpExecArray | null;
    
    while ((match = cssPattern.exec(line)) !== null) {
      const fontFamily = match[1];
      
      // Skip system fonts
      if (!this.isSystemFont(fontFamily)) {
        const tokenName = this.generateFontFamilyTokenName(fontFamily);
        fixes.push({
          filePath,
          lineNumber,
          columnStart: match.index,
          columnEnd: match.index + match[0].length,
          originalValue: `font-family: "${fontFamily}"`,
          replacementValue: `font-family: var(--font-family-${tokenName})`,
          tokenName: `font-family-${tokenName}`,
          fixType: 'hardcoded-to-token'
        });
      }
    }

    return fixes;
  }

  private findFontWeightTokenOpportunities(line: string, lineNumber: number, filePath: string): TypographyFix[] {
    const fixes: TypographyFix[] = [];
    
    // CSS font-weight declarations with numeric values
    const cssPattern = /font-weight\s*:\s*(\d{3})/g;
    let match: RegExpExecArray | null;
    
    while ((match = cssPattern.exec(line)) !== null) {
      const weight = match[1];
      const tokenName = this.getFontWeightTokenName(weight);
      
      if (tokenName) {
        fixes.push({
          filePath,
          lineNumber,
          columnStart: match.index,
          columnEnd: match.index + match[0].length,
          originalValue: `font-weight: ${weight}`,
          replacementValue: `font-weight: var(--font-weight-${tokenName})`,
          tokenName: `font-weight-${tokenName}`,
          fixType: 'hardcoded-to-token'
        });
      }
    }

    return fixes;
  }

  private findHeadingElements(line: string, lineNumber: number, filePath: string): Array<{ element: string; styles: Record<string, string>; location: FileLocation }> {
    const headings: Array<{ element: string; styles: Record<string, string>; location: FileLocation }> = [];
    
    // Find HTML heading tags with potential inline styles
    const headingPattern = /<(h[1-6])([^>]*)>/gi;
    let match: RegExpExecArray | null;
    
    while ((match = headingPattern.exec(line)) !== null) {
      const element = match[1].toLowerCase();
      const attributes = match[2];
      const styles = this.extractInlineStyles(attributes);
      
      headings.push({
        element,
        styles,
        location: {
          filePath,
          lineNumber,
          columnNumber: match.index,
          context: line.trim()
        }
      });
    }

    return headings;
  }

  private findLineHeightDeclarations(line: string, lineNumber: number, filePath: string): Array<{ value: string; columnStart: number; columnEnd: number }> {
    const declarations: Array<{ value: string; columnStart: number; columnEnd: number }> = [];
    
    // CSS line-height declarations
    const cssPattern = /line-height\s*:\s*([^;]+)/g;
    let match: RegExpExecArray | null;
    
    while ((match = cssPattern.exec(line)) !== null) {
      const value = match[1].trim();
      declarations.push({
        value,
        columnStart: match.index,
        columnEnd: match.index + match[0].length
      });
    }

    // React inline styles - handle both quoted and unquoted values
    const reactPattern = /lineHeight\s*:\s*['"`]?([^'"`\s,}]+)['"`]?/g;
    while ((match = reactPattern.exec(line)) !== null) {
      const value = match[1];
      declarations.push({
        value,
        columnStart: match.index,
        columnEnd: match.index + match[0].length
      });
    }

    return declarations;
  }

  private validateHeadingAgainstRule(heading: { element: string; styles: Record<string, string>; location: FileLocation }, rule: TypographyHierarchyRule): TypographyHierarchyViolation[] {
    const violations: TypographyHierarchyViolation[] = [];

    // Check font size
    if (heading.styles.fontSize && heading.styles.fontSize !== rule.fontSize) {
      violations.push({
        element: heading.element,
        property: 'fontSize',
        currentValue: heading.styles.fontSize,
        expectedValue: rule.fontSize,
        location: heading.location,
        severity: Severity.MEDIUM
      });
    }

    // Check line height
    if (heading.styles.lineHeight && heading.styles.lineHeight !== rule.lineHeight) {
      violations.push({
        element: heading.element,
        property: 'lineHeight',
        currentValue: heading.styles.lineHeight,
        expectedValue: rule.lineHeight,
        location: heading.location,
        severity: Severity.LOW
      });
    }

    // Check font weight
    if (heading.styles.fontWeight && heading.styles.fontWeight !== rule.fontWeight) {
      violations.push({
        element: heading.element,
        property: 'fontWeight',
        currentValue: heading.styles.fontWeight,
        expectedValue: rule.fontWeight,
        location: heading.location,
        severity: Severity.LOW
      });
    }

    return violations;
  }

  private getStandardizedLineHeight(value: string): string | null {
    // Parse numeric line-height values
    const numericValue = parseFloat(value);
    
    if (!isNaN(numericValue)) {
      // Find closest standard line-height
      const closest = this.commonLineHeights.reduce((prev, curr) => 
        Math.abs(curr - numericValue) < Math.abs(prev - numericValue) ? curr : prev
      );
      
      // Only suggest change if the difference is significant
      if (Math.abs(closest - numericValue) > 0.05) {
        return closest.toString();
      }
    }

    return null;
  }

  private getLineHeightTokenName(value: string): string {
    const lineHeightMap: Record<string, string> = {
      '1': 'none',
      '1.2': 'tight',
      '1.25': 'snug',
      '1.4': 'normal',
      '1.5': 'relaxed',
      '1.75': 'loose'
    };

    return lineHeightMap[value] || 'custom';
  }

  private findBestFontSizeToken(value: string): string | null {
    // Convert to rem for comparison
    const remValue = this.convertToRem(value);
    if (!remValue) return null;

    // Find closest standard font size
    const standardSizes = Object.entries(this.standardFontSizes);
    const closest = standardSizes.reduce((prev, curr) => {
      const prevRem = parseFloat(prev[1]);
      const currRem = parseFloat(curr[1]);
      const targetRem = parseFloat(remValue);
      
      return Math.abs(currRem - targetRem) < Math.abs(prevRem - targetRem) ? curr : prev;
    });

    // Only suggest if reasonably close
    const targetRem = parseFloat(remValue);
    const closestRem = parseFloat(closest[1]);
    
    if (Math.abs(closestRem - targetRem) < 0.125) { // Within 0.125rem
      return closest[0];
    }

    return null;
  }

  private convertToRem(value: string): string | null {
    const match = value.match(/^(\d+(?:\.\d+)?)(px|rem|em)$/);
    if (!match) return null;

    const num = parseFloat(match[1]);
    const unit = match[2];

    switch (unit) {
      case 'rem':
        return `${num}rem`;
      case 'px':
        return `${num / 16}rem`; // Assuming 16px base
      case 'em':
        return `${num}rem`; // Approximate conversion
      default:
        return null;
    }
  }

  private isSystemFont(fontFamily: string): boolean {
    const systemFonts = [
      'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI',
      'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif', 'serif', 'monospace'
    ];
    
    return systemFonts.some(font => 
      fontFamily.toLowerCase().includes(font.toLowerCase())
    );
  }

  private generateFontFamilyTokenName(fontFamily: string): string {
    return fontFamily
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }

  private getFontWeightTokenName(weight: string): string | null {
    const weightMap: Record<string, string> = {
      '100': 'thin',
      '200': 'extralight',
      '300': 'light',
      '400': 'normal',
      '500': 'medium',
      '600': 'semibold',
      '700': 'bold',
      '800': 'extrabold',
      '900': 'black'
    };

    return weightMap[weight] || null;
  }

  private extractInlineStyles(attributes: string): Record<string, string> {
    const styles: Record<string, string> = {};
    
    // Extract style attribute
    const styleMatch = attributes.match(/style\s*=\s*['"`]([^'"`]+)['"`]/);
    if (styleMatch) {
      const styleContent = styleMatch[1];
      const declarations = styleContent.split(';');
      
      declarations.forEach(declaration => {
        const [property, value] = declaration.split(':').map(s => s.trim());
        if (property && value) {
          styles[this.toCamelCase(property)] = value;
        }
      });
    }

    return styles;
  }

  private extractTypographyValues(files: FileInfo[]): { fontSize: Set<string>; fontFamily: Set<string>; fontWeight: Set<string>; lineHeight: Set<string> } {
    const values = {
      fontSize: new Set<string>(),
      fontFamily: new Set<string>(),
      fontWeight: new Set<string>(),
      lineHeight: new Set<string>()
    };

    files.forEach(file => {
      const lines = file.content.split('\n');
      lines.forEach(line => {
        // Extract font sizes
        const fontSizeMatches = line.match(/font-size\s*:\s*([^;]+)/g);
        fontSizeMatches?.forEach(match => {
          const value = match.split(':')[1].trim();
          values.fontSize.add(value);
        });

        // Extract font families
        const fontFamilyMatches = line.match(/font-family\s*:\s*([^;]+)/g);
        fontFamilyMatches?.forEach(match => {
          const value = match.split(':')[1].trim();
          values.fontFamily.add(value);
        });

        // Extract font weights
        const fontWeightMatches = line.match(/font-weight\s*:\s*([^;]+)/g);
        fontWeightMatches?.forEach(match => {
          const value = match.split(':')[1].trim();
          values.fontWeight.add(value);
        });

        // Extract line heights
        const lineHeightMatches = line.match(/line-height\s*:\s*([^;]+)/g);
        lineHeightMatches?.forEach(match => {
          const value = match.split(':')[1].trim();
          values.lineHeight.add(value);
        });
      });
    });

    return values;
  }

  private optimizeTypographyScale(extractedValues: { fontSize: Set<string>; fontFamily: Set<string>; fontWeight: Set<string>; lineHeight: Set<string> }): TypographyScale {
    return {
      fontSize: this.optimizeFontSizes(Array.from(extractedValues.fontSize)),
      fontFamily: this.optimizeFontFamilies(Array.from(extractedValues.fontFamily)),
      fontWeight: this.optimizeFontWeights(Array.from(extractedValues.fontWeight)),
      lineHeight: this.optimizeLineHeights(Array.from(extractedValues.lineHeight)),
      letterSpacing: {} // Can be extended based on needs
    };
  }

  private optimizeFontSizes(fontSizes: string[]): Record<string, string> {
    const optimized: Record<string, string> = {};
    
    // Convert all to rem and sort
    const remSizes = fontSizes
      .map(size => ({ original: size, rem: this.convertToRem(size) }))
      .filter(item => item.rem)
      .sort((a, b) => parseFloat(a.rem!) - parseFloat(b.rem!));

    // Assign semantic names
    const sizeNames = ['xs', 'sm', 'base', 'lg', 'xl', '2xl', '3xl', '4xl', '5xl', '6xl'];
    
    remSizes.forEach((size, index) => {
      const name = sizeNames[Math.min(index, sizeNames.length - 1)] || `size-${index}`;
      optimized[name] = size.rem!;
    });

    return optimized;
  }

  private optimizeFontFamilies(fontFamilies: string[]): Record<string, string> {
    const optimized: Record<string, string> = {};
    
    fontFamilies.forEach((family, index) => {
      if (!this.isSystemFont(family)) {
        const name = this.generateFontFamilyTokenName(family);
        optimized[name] = family;
      }
    });

    return optimized;
  }

  private optimizeFontWeights(fontWeights: string[]): Record<string, string> {
    const optimized: Record<string, string> = {};
    
    fontWeights.forEach(weight => {
      const tokenName = this.getFontWeightTokenName(weight);
      if (tokenName) {
        optimized[tokenName] = weight;
      }
    });

    return optimized;
  }

  private optimizeLineHeights(lineHeights: string[]): Record<string, string> {
    const optimized: Record<string, string> = {};
    
    lineHeights.forEach(height => {
      const tokenName = this.getLineHeightTokenName(height);
      optimized[tokenName] = height;
    });

    return optimized;
  }

  private groupFixesByFile(fixes: TypographyFix[]): Map<string, TypographyFix[]> {
    const grouped = new Map<string, TypographyFix[]>();
    
    fixes.forEach(fix => {
      if (!grouped.has(fix.filePath)) {
        grouped.set(fix.filePath, []);
      }
      grouped.get(fix.filePath)!.push(fix);
    });

    return grouped;
  }

  private async applyFixesToFile(filePath: string, fixes: TypographyFix[]): Promise<boolean> {
    try {
      // Sort fixes by line number and column (reverse order to avoid offset issues)
      const sortedFixes = fixes.sort((a, b) => {
        if (a.lineNumber !== b.lineNumber) {
          return b.lineNumber - a.lineNumber;
        }
        return b.columnStart - a.columnStart;
      });

      // This would typically read the file, apply fixes, and write back
      // For now, we'll just log the intended changes
      auditLogger.info('Would apply typography fixes to file', {
        filePath,
        fixCount: fixes.length,
        fixes: sortedFixes.map(fix => ({
          line: fix.lineNumber,
          original: fix.originalValue,
          replacement: fix.replacementValue,
          token: fix.tokenName
        }))
      });

      return true;
    } catch (error) {
      auditLogger.error('Failed to apply fixes to file', error as Error, { filePath });
      return false;
    }
  }

  private isCommentLine(line: string): boolean {
    const trimmed = line.trim();
    return trimmed.startsWith('//') || 
           trimmed.startsWith('/*') || 
           trimmed.startsWith('*') ||
           trimmed.startsWith('<!--');
  }

  private toCamelCase(str: string): string {
    return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
  }

  private initializeDefaultTypographyScale(): TypographyScale {
    return {
      fontSize: {
        'xs': '0.75rem',
        'sm': '0.875rem',
        'base': '1rem',
        'lg': '1.125rem',
        'xl': '1.25rem',
        '2xl': '1.5rem',
        '3xl': '1.875rem',
        '4xl': '2.25rem',
        '5xl': '3rem',
        '6xl': '3.75rem'
      },
      fontFamily: {
        'sans': 'ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif',
        'serif': 'ui-serif, Georgia, Cambria, "Times New Roman", Times, serif',
        'mono': 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace'
      },
      fontWeight: {
        'thin': '100',
        'extralight': '200',
        'light': '300',
        'normal': '400',
        'medium': '500',
        'semibold': '600',
        'bold': '700',
        'extrabold': '800',
        'black': '900'
      },
      lineHeight: {
        'none': '1',
        'tight': '1.25',
        'snug': '1.375',
        'normal': '1.5',
        'relaxed': '1.625',
        'loose': '2'
      },
      letterSpacing: {
        'tighter': '-0.05em',
        'tight': '-0.025em',
        'normal': '0em',
        'wide': '0.025em',
        'wider': '0.05em',
        'widest': '0.1em'
      }
    };
  }

  private initializeDefaultHierarchyRules(): TypographyHierarchyRule[] {
    return [
      { element: 'h1', fontSize: '2.25rem', lineHeight: '1.2', fontWeight: '700' },
      { element: 'h2', fontSize: '1.875rem', lineHeight: '1.3', fontWeight: '600' },
      { element: 'h3', fontSize: '1.5rem', lineHeight: '1.4', fontWeight: '600' },
      { element: 'h4', fontSize: '1.25rem', lineHeight: '1.4', fontWeight: '500' },
      { element: 'h5', fontSize: '1.125rem', lineHeight: '1.5', fontWeight: '500' },
      { element: 'h6', fontSize: '1rem', lineHeight: '1.5', fontWeight: '500' }
    ];
  }
}