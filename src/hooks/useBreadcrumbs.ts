import { useMemo } from 'react';
import { useLocation } from 'react-router-dom';
import { BreadcrumbItem } from '@/components/ui/Breadcrumbs';

/**
 * Route configuration for breadcrumb generation
 * Maps route paths to their display labels and parent routes
 */
const routeConfig: Record<string, BreadcrumbItem[]> = {
  '/player-skills-training': [
    { label: 'Training Programs', href: '/#programs' },
    { label: 'Player Skills Training', href: '/player-skills-training' }
  ],
  '/goalie-training': [
    { label: 'Training Programs', href: '/#programs' },
    { label: 'Goalie Training', href: '/goalie-training' }
  ],
  '/coaches': [
    { label: 'Our Team', href: '/#coaches' },
    { label: 'Coaches', href: '/coaches' }
  ],
  '/marketing-package-builder': [
    { label: 'Services', href: '/#services' },
    { label: 'Marketing Package Builder', href: '/marketing-package-builder' }
  ],
  '/hockey-training': [
    { label: 'Training Programs', href: '/#programs' },
    { label: 'Hockey Training', href: '/hockey-training' }
  ],
  '/faq': [
    { label: 'Support', href: '/#faq' },
    { label: 'FAQ', href: '/faq' }
  ],
  '/pop-up-clinics': [
    { label: 'Training Programs', href: '/#programs' },
    { label: 'Pop-Up Clinics', href: '/pop-up-clinics' }
  ],
  '/scheduling': [
    { label: 'Services', href: '/#services' },
    { label: 'Scheduling', href: '/scheduling' }
  ],
  '/client-management': [
    { label: 'Services', href: '/#services' },
    { label: 'Client Management', href: '/client-management' }
  ]
};

/**
 * Section mappings for better breadcrumb organization
 * Groups related pages under common parent sections
 */
const sectionMappings: Record<string, { label: string; href: string }> = {
  'training': { label: 'Training Programs', href: '/#programs' },
  'services': { label: 'Services', href: '/#services' },
  'team': { label: 'Our Team', href: '/#coaches' },
};

/**
 * Custom hook for generating breadcrumb navigation items
 * Automatically creates breadcrumb trails based on current route
 * 
 * @param customItems - Optional custom breadcrumb items to override defaults
 * @returns Array of breadcrumb items for the current route
 */
export const useBreadcrumbs = (customItems?: BreadcrumbItem[]): BreadcrumbItem[] => {
  const location = useLocation();
  
  return useMemo(() => {
    // If custom items are provided, use them
    if (customItems && customItems.length > 0) {
      return customItems;
    }
    
    const currentPath = location.pathname;
    
    // Don't show breadcrumbs on home page
    if (currentPath === '/') {
      return [];
    }
    
    // Generate breadcrumbs based on route configuration
    const currentRoute = routeConfig[currentPath];
    
    if (currentRoute) {
      return currentRoute;
    } else {
      // Fallback: generate breadcrumbs from URL segments
      const segments = currentPath.split('/').filter(Boolean);
      const breadcrumbs: BreadcrumbItem[] = [];
      
      segments.forEach((segment, index) => {
        const path = '/' + segments.slice(0, index + 1).join('/');
        const label = segment
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
        
        breadcrumbs.push({
          label,
          href: path
        });
      });
      
      return breadcrumbs;
    }
  }, [location.pathname, customItems]);
};

/**
 * Hook to determine if breadcrumbs should be shown on current page
 * Some pages like home, 404, etc. might not need breadcrumbs
 * 
 * @returns boolean indicating whether to show breadcrumbs
 */
export const useShouldShowBreadcrumbs = (): boolean => {
  const location = useLocation();
  
  return useMemo(() => {
    const currentPath = location.pathname;
    
    // Don't show breadcrumbs on these pages
    const excludedPaths = ['/', '/404', '/not-found'];
    
    return !excludedPaths.includes(currentPath);
  }, [location.pathname]);
};

/**
 * Utility function to add a new route to the breadcrumb configuration
 * Useful for dynamic routes or when adding new pages
 * 
 * @param path - The route path
 * @param breadcrumbs - Array of breadcrumb items for this route
 */
export const addBreadcrumbRoute = (
  path: string, 
  breadcrumbs: BreadcrumbItem[]
): void => {
  routeConfig[path] = breadcrumbs;
};