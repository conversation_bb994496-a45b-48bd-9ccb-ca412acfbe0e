import { useEffect } from "react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import nfcnImage from "@/assets/SCR-20250608-nfcn.png";
import nfdxImage from "@/assets/SCR-20250608-nfdx.png";
import SEO from "@/components/SEO";
import TrainingProgramSEO from "@/components/TrainingProgramSEO";
import { Breadcrumbs } from "@/components/ui/Breadcrumbs";
import { useBreadcrumbs, useShouldShowBreadcrumbs } from "@/hooks/useBreadcrumbs";

const PlayerSkillsTraining = () => {
  useEffect(() => {
    // Page title now handled by SEO component

    // Implement scroll reveal
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("active");
          }
        });
      },
      { threshold: 0.1 }
    );

    const revealElements = document.querySelectorAll(".reveal");
    revealElements.forEach((el) => observer.observe(el));

    return () => {
      revealElements.forEach((el) => observer.unobserve(el));
    };
  }, []);

  const skillCategories = [
    {
      title: "Mindset",
      description: "Build confidence, focus, and mental toughness for peak performance",
      iconColor: "bg-blue-100",
      iconTextColor: "text-blue-600",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      ),
    },
    {
      title: "Body Control",
      description: "Master body posture, puck protection, and player separation techniques",
      iconColor: "bg-green-100",
      iconTextColor: "text-green-600",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      ),
    },
    {
      title: "Strength & Power",
      description: "Develop core stability, leg power, and overall athletic performance",
      iconColor: "bg-purple-100",
      iconTextColor: "text-purple-600",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
    },
    {
      title: "Game Vision",
      description: "Enhance ice vision, anticipation, and reading the play abilities",
      iconColor: "bg-red-100",
      iconTextColor: "text-red-600",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        </svg>
      ),
    },
    {
      title: "Technical Skills",
      description: "Perfect skating technique, stick handling, and shooting accuracy",
      iconColor: "bg-yellow-100",
      iconTextColor: "text-yellow-600",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
        </svg>
      ),
    },
    {
      title: "Elite Training",
      description: "NHL-level drills and advanced stick and body skills development",
      iconColor: "bg-indigo-100",
      iconTextColor: "text-indigo-600",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
        </svg>
      ),
    }
  ];

  return (
    <div className="flex flex-col min-h-screen font-sans">
      <TrainingProgramSEO
        title="Player Skills Training - ELEV802 | 702HOCKEY - Premier Hockey Training in Las Vegas"
        programName="Elite Player Skills Development Program"
        programDescription="Comprehensive player skills training designed to enhance all aspects of hockey performance at ELEV802's premier training facility in Las Vegas."
        programImage="/images/player-skills.jpg"
        programType="player-skills"
        price={180}
        slug="player-skills-training"
        reviews={[
          {
            reviewRating: 5,
            author: "David R.",
            reviewBody: "The skills development at ELEV802 has completely transformed my game. Their holistic approach addresses every aspect of hockey performance.",
            datePublished: "2025-03-28"
          }
        ]}
      />
      <Header />
      {useShouldShowBreadcrumbs() && (
        <Breadcrumbs
          items={useBreadcrumbs()}
          showHomeIcon={true}
          separator="chevron"
        />
      )}
      <main className="flex-grow">
        {/* Skills Categories Section */}
        <section className="py-20 bg-elev-background-muted reveal relative">
          <div className="container mx-auto px-4">
            <h2 className="text-4xl font-bold text-center mb-16 text-elev-navy">COMPREHENSIVE TRAINING PROGRAM</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {skillCategories.map((category, index) => (
                <div key={index} className="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow">
                  <div className={`w-16 h-16 ${category.iconColor} rounded-full flex items-center justify-center mb-6`}>
                    <div className={category.iconTextColor}>
                      {category.icon}
                    </div>
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-elev-text-primary">{category.title}</h3>
                  <p className="text-elev-text-secondary">{category.description}</p>
                </div>
              ))}
            </div>
          </div>
          {/* Decorative bottom element */}
          <div className="absolute bottom-0 left-0 w-full h-16 bg-white transform -skew-y-2 translate-y-8"></div>
        </section>

        {/* Small Group Skills Section */}
        <section className="py-16 bg-white reveal">
          <div className="container mx-auto px-4">
            <h2 className="text-4xl font-bold text-center mb-12 text-elev-navy">SMALL GROUP SKILLS SESSIONS</h2>
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div>
                <p className="text-lg mb-6 text-elev-text-primary">
                  Our small group skills sessions provide a collaborative environment where players can develop their skills with personalized attention. These sessions are designed to improve fundamental hockey skills through progressive drills and real-game scenarios.
                </p>
                <p className="text-lg mb-6 text-gray-900">
                  With a maximum of 10 skaters per session, our experienced coaches can provide individualized feedback while fostering a competitive and supportive atmosphere.
                </p>
                <ul className="list-disc pl-6 mb-6 text-lg text-elev-text-primary">
                  <li>Specialized skill development</li>
                  <li>Position-specific training</li>
                  <li>Progressive skill advancement</li>
                  <li>Performance tracking</li>
                </ul>
              </div>
              <div className="rounded-lg overflow-hidden shadow-xl">
                <img 
                  src={nfcnImage}
                  alt="Hockey player practicing skills training with equipment" 
                  className="w-full h-[500px] object-cover"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 bg-elev-background-muted reveal">
          <div className="container mx-auto px-4">
            <h2 className="text-4xl font-bold text-center mb-12 text-elev-navy">BENEFITS OF SMALL GROUP TRAINING</h2>
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div className="order-2 md:order-1 rounded-lg overflow-hidden shadow-xl">
                <img 
                  src={nfdxImage}
                  alt="Hockey coaches working with young players in small group training" 
                  className="w-full h-[500px] object-cover object-center"
                />
              </div>
              <div className="order-1 md:order-2">
                <p className="text-lg mb-6 text-gray-900">
                  Small group training offers numerous benefits, including personalized attention, improved skill development, and enhanced teamwork. Our coaches work closely with players to identify areas for improvement and develop customized training plans.
                </p>
                <p className="text-lg mb-6 text-gray-900">
                  By training in a small group setting, players can learn from one another, develop their skills in a supportive environment, and gain valuable experience in game-like situations.
                </p>
                <ul className="list-disc pl-6 mb-6 text-lg text-gray-900">
                  <li>Personalized attention from expert coaches</li>
                  <li>Improved skill development through targeted drills</li>
                  <li>Enhanced teamwork and communication</li>
                  <li>Customized training plans tailored to individual needs</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-blue-900 text-white reveal">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-4xl font-bold mb-6">DEVELOP YOUR SKILLS WITH 702HOCKEY</h2>
            <p className="text-xl max-w-3xl mx-auto mb-8">
              Join today and experience the difference our professional coaching can make in your hockey development.
            </p>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default PlayerSkillsTraining;
