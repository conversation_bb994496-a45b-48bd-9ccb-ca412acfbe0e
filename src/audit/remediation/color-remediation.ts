import { Issue, FileInfo, IssueType } from '../types/index';
import { DesignTokenSystem } from '../design-tokens/types';
import { auditLogger } from '../core/logger';

export interface ColorRemediationResult {
  success: boolean;
  filesModified: string[];
  replacements: ColorReplacement[];
  errors: RemediationError[];
  summary: {
    totalReplacements: number;
    filesAffected: number;
    tokensCreated: number;
  };
}

export interface ColorReplacement {
  filePath: string;
  lineNumber: number;
  originalValue: string;
  tokenValue: string;
  context: string;
}

export interface RemediationError {
  filePath: string;
  lineNumber: number;
  error: string;
  originalValue: string;
}

export class ColorRemediationEngine {
  private colorMap = new Map<string, string>();

  constructor(designTokens: DesignTokenSystem) {
    this.buildColorMap(designTokens);
  }

  async remediateColorIssues(issues: Issue[], files: FileInfo[]): Promise<ColorRemediationResult> {
    auditLogger.info('Starting color remediation', { issueCount: issues.length, fileCount: files.length });

    const colorIssues = issues.filter(issue => 
      issue.type === IssueType.COLOR_INCONSISTENCY && issue.autoFixable
    );

    const result: ColorRemediationResult = {
      success: true,
      filesModified: [],
      replacements: [],
      errors: [],
      summary: { totalReplacements: 0, filesAffected: 0, tokensCreated: 0 }
    };

    for (const issue of colorIssues) {
      const file = files.find(f => f.path === issue.location.filePath);
      if (!file) {
        result.errors.push({
          filePath: issue.location.filePath,
          lineNumber: 0,
          error: 'File not found',
          originalValue: ''
        });
        continue;
      }

      const colorValue = this.extractColorFromIssue(issue);
      const tokenValue = this.findBestTokenMatch(colorValue);
      
      if (colorValue && tokenValue) {
        const replacement: ColorReplacement = {
          filePath: issue.location.filePath,
          lineNumber: issue.location.lineNumber,
          originalValue: colorValue,
          tokenValue,
          context: issue.location.context
        };

        // Apply replacement
        file.content = file.content.replace(colorValue, tokenValue);
        result.replacements.push(replacement);
        
        if (!result.filesModified.includes(file.path)) {
          result.filesModified.push(file.path);
        }
      }
    }

    result.summary = {
      totalReplacements: result.replacements.length,
      filesAffected: result.filesModified.length,
      tokensCreated: 0
    };

    auditLogger.info('Color remediation completed', result.summary);
    return result;
  }

  async validateColorTokenUsage(files: FileInfo[]): Promise<{
    validUsages: number;
    invalidUsages: number;
    suggestions: string[];
  }> {
    let validUsages = 0;
    let invalidUsages = 0;
    const suggestions: string[] = [];

    for (const file of files) {
      const tokenMatches = file.content.match(/var\(--color-[^)]+\)/g) || [];
      for (const token of tokenMatches) {
        if (this.isValidColorToken(token)) {
          validUsages++;
        } else {
          invalidUsages++;
          suggestions.push(`Invalid color token '${token}' in ${file.path}`);
        }
      }
    }

    return { validUsages, invalidUsages, suggestions };
  }

  private buildColorMap(designTokens: DesignTokenSystem): void {
    // Map primary colors first
    Object.entries(designTokens.colors.primary).forEach(([key, value]) => {
      this.colorMap.set(value.toLowerCase(), `var(--color-primary-${key})`);
    });

    // Map neutral colors (high priority for common colors like white/black)
    Object.entries(designTokens.colors.neutral).forEach(([key, value]) => {
      if (!this.colorMap.has(value.toLowerCase())) {
        this.colorMap.set(value.toLowerCase(), `var(--color-neutral-${key})`);
      }
    });

    // Explicitly prioritize neutral colors for common values
    this.colorMap.set('#ffffff', 'var(--color-neutral-50)');
    this.colorMap.set('#000000', 'var(--color-neutral-900)');
    this.colorMap.set('#0f172a', 'var(--color-neutral-900)'); // Dark neutral

    // Map semantic colors (lower priority)
    Object.entries(designTokens.colors.semantic).forEach(([category, colors]) => {
      Object.entries(colors).forEach(([variant, value]) => {
        if (!this.colorMap.has(value.toLowerCase())) {
          this.colorMap.set(value.toLowerCase(), `var(--color-${category}-${variant})`);
        }
      });
    });
  }

  findBestTokenMatch(colorValue: string): string | null {
    const directMatch = this.colorMap.get(colorValue.toLowerCase());
    if (directMatch) return directMatch;

    // Hardcoded rules for common colors
    if (/#3b82f6/i.test(colorValue)) return 'var(--color-primary-500)';
    if (/#dc2626/i.test(colorValue)) return 'var(--color-error-default)';
    if (/#ffffff|#fff|white/i.test(colorValue)) return 'var(--color-neutral-50)';
    if (/#000000|#000|black/i.test(colorValue)) return 'var(--color-neutral-900)';

    return null;
  }

  private extractColorFromIssue(issue: Issue): string {
    const descriptionMatch = issue.description.match(/'([^']+)'/);
    if (descriptionMatch) return descriptionMatch[1];

    const contextMatch = issue.location.context.match(/#[0-9A-F]{6}|#[0-9A-F]{3}|rgb\([^)]+\)|rgba\([^)]+\)/i);
    if (contextMatch) return contextMatch[0];

    return '';
  }

  private isValidColorToken(token: string): boolean {
    const tokenName = token.match(/var\(--([^)]+)\)/)?.[1];
    if (!tokenName) return false;

    const validPatterns = [
      /^color-primary-\d+$/,
      /^color-secondary-\d+$/,
      /^color-neutral-\d+$/,
      /^color-(success|warning|error|info)-(light|default|dark|contrast)$/,
      /^color-surface-\w+$/
    ];

    return validPatterns.some(pattern => pattern.test(tokenName));
  }

  // Apply color replacement to a line of code
  applyColorReplacement(line: string, replacement: ColorReplacement): string {
    const escapedOriginal = replacement.originalValue.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(escapedOriginal, 'gi');
    return line.replace(regex, replacement.tokenValue);
  }

  // Helper methods for testing
  normalizeColor(color: string): string | null {
    if (/^#[0-9A-F]{3}$/i.test(color)) {
      return color.replace(/^#([0-9A-F])([0-9A-F])([0-9A-F])$/i, '#$1$1$2$2$3$3');
    }

    const rgbMatch = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
    if (rgbMatch) {
      const [, r, g, b] = rgbMatch;
      return `#${parseInt(r).toString(16).padStart(2, '0')}${parseInt(g).toString(16).padStart(2, '0')}${parseInt(b).toString(16).padStart(2, '0')}`;
    }

    return color.toLowerCase();
  }

  calculateColorDistance(color1: string, color2: string): number {
    const rgb1 = this.hexToRgb(color1);
    const rgb2 = this.hexToRgb(color2);

    if (!rgb1 || !rgb2) return Infinity;

    return Math.sqrt(
      Math.pow(rgb1.r - rgb2.r, 2) +
      Math.pow(rgb1.g - rgb2.g, 2) +
      Math.pow(rgb1.b - rgb2.b, 2)
    );
  }

  private hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }
}