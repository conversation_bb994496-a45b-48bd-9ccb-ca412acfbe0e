/**
 * Layout Pattern Validator
 * Validates layout patterns and grid system compliance
 */

import { LayoutTokens, ValidationRule, PatternValidation } from './types.js';

export interface LayoutValidationResult {
  isValid: boolean;
  violations: LayoutViolation[];
  patterns: PatternAnalysis[];
  compliance: LayoutCompliance;
}

export interface LayoutViolation {
  type: 'breakpoint' | 'container' | 'grid' | 'pattern' | 'accessibility';
  severity: 'error' | 'warning' | 'info';
  message: string;
  location: {
    file: string;
    line: number;
    column: number;
  };
  suggestion: string;
  autoFixable: boolean;
  rule: string;
}

export interface PatternAnalysis {
  pattern: string;
  usage: number;
  compliance: number;
  issues: string[];
  recommendations: string[];
}

export interface LayoutCompliance {
  breakpointCompliance: number;
  containerCompliance: number;
  gridCompliance: number;
  patternCompliance: number;
  accessibilityCompliance: number;
  overallCompliance: number;
  totalIssues: number;
  criticalIssues: number;
}

export class LayoutPatternValidator {
  private layoutTokens: LayoutTokens;

  constructor(layoutTokens: LayoutTokens) {
    this.layoutTokens = layoutTokens;
  }

  /**
   * Validates CSS against layout and grid system standards
   */
  validateLayout(cssContent: string, filePath: string): LayoutValidationResult {
    const violations: LayoutViolation[] = [];
    const patterns: PatternAnalysis[] = [];

    // Validate breakpoints
    violations.push(...this.validateBreakpoints(cssContent, filePath));
    
    // Validate containers
    violations.push(...this.validateContainers(cssContent, filePath));
    
    // Validate grid usage
    violations.push(...this.validateGridUsage(cssContent, filePath));
    
    // Validate layout patterns
    const patternAnalysis = this.analyzeLayoutPatterns(cssContent, filePath);
    patterns.push(...patternAnalysis.patterns);
    violations.push(...patternAnalysis.violations);
    
    // Validate accessibility
    violations.push(...this.validateAccessibility(cssContent, filePath));

    const compliance = this.generateLayoutCompliance(violations, cssContent);

    return {
      isValid: violations.filter(v => v.severity === 'error').length === 0,
      violations,
      patterns,
      compliance
    };
  }

  /**
   * Validates responsive breakpoint usage
   */
  private validateBreakpoints(cssContent: string, filePath: string): LayoutViolation[] {
    const violations: LayoutViolation[] = [];
    const lines = cssContent.split('\n');

    // Pattern for media queries
    const mediaQueryPattern = /@media[^{]*\((?:min-width|max-width):\s*(\d+(?:\.\d+)?(?:px|rem|em))\)/gi;

    lines.forEach((line, lineIndex) => {
      let match;
      while ((match = mediaQueryPattern.exec(line)) !== null) {
        const breakpointValue = match[1];
        
        if (!this.isStandardBreakpoint(breakpointValue)) {
          violations.push({
            type: 'breakpoint',
            severity: 'warning',
            message: `Non-standard breakpoint "${breakpointValue}" should use design system breakpoints`,
            location: {
              file: filePath,
              line: lineIndex + 1,
              column: match.index + 1
            },
            suggestion: this.suggestBreakpoint(breakpointValue),
            autoFixable: true,
            rule: 'Responsive Breakpoints'
          });
        }
      }
    });

    return violations;
  }

  /**
   * Validates container usage and max-width compliance
   */
  private validateContainers(cssContent: string, filePath: string): LayoutViolation[] {
    const violations: LayoutViolation[] = [];
    const lines = cssContent.split('\n');

    // Pattern for max-width declarations
    const maxWidthPattern = /max-width:\s*(\d+(?:\.\d+)?(?:px|rem|em|%))/gi;

    lines.forEach((line, lineIndex) => {
      let match;
      while ((match = maxWidthPattern.exec(line)) !== null) {
        const maxWidth = match[1];
        
        if (!this.isStandardContainerWidth(maxWidth)) {
          violations.push({
            type: 'container',
            severity: 'info',
            message: `Container max-width "${maxWidth}" could use standard container token`,
            location: {
              file: filePath,
              line: lineIndex + 1,
              column: match.index + 1
            },
            suggestion: this.suggestContainerWidth(maxWidth),
            autoFixable: true,
            rule: 'Container Max Width'
          });
        }
      }
    });

    return violations;
  }

  /**
   * Validates CSS Grid usage and gap consistency
   */
  private validateGridUsage(cssContent: string, filePath: string): LayoutViolation[] {
    const violations: LayoutViolation[] = [];
    const lines = cssContent.split('\n');

    // Pattern for grid gap declarations
    const gapPattern = /(?:gap|grid-gap):\s*(\d+(?:\.\d+)?(?:px|rem|em))/gi;
    const gridTemplatePattern = /grid-template-columns:\s*([^;]+)/gi;

    lines.forEach((line, lineIndex) => {
      // Validate gap values
      let match;
      while ((match = gapPattern.exec(line)) !== null) {
        const gapValue = match[1];
        
        if (!this.isStandardSpacing(gapValue)) {
          violations.push({
            type: 'grid',
            severity: 'warning',
            message: `Grid gap "${gapValue}" should use standardized spacing token`,
            location: {
              file: filePath,
              line: lineIndex + 1,
              column: match.index + 1
            },
            suggestion: this.suggestSpacingToken(gapValue),
            autoFixable: true,
            rule: 'Grid Gap Consistency'
          });
        }
      }

      // Validate grid template patterns
      while ((match = gridTemplatePattern.exec(line)) !== null) {
        const templateValue = match[1];
        
        if (this.hasHardcodedGridValues(templateValue)) {
          violations.push({
            type: 'grid',
            severity: 'info',
            message: `Grid template "${templateValue}" could use responsive grid patterns`,
            location: {
              file: filePath,
              line: lineIndex + 1,
              column: match.index + 1
            },
            suggestion: 'Consider using repeat(auto-fit, minmax()) for responsive grids',
            autoFixable: false,
            rule: 'Responsive Grid Patterns'
          });
        }
      }
    });

    return violations;
  }

  /**
   * Analyzes layout patterns usage and compliance
   */
  private analyzeLayoutPatterns(cssContent: string, filePath: string): { patterns: PatternAnalysis[], violations: LayoutViolation[] } {
    const patterns: PatternAnalysis[] = [];
    const violations: LayoutViolation[] = [];

    // Analyze each layout pattern
    this.layoutTokens.validation.patterns.forEach(patternDef => {
      const analysis = this.analyzePattern(cssContent, filePath, patternDef);
      patterns.push(analysis.analysis);
      violations.push(...analysis.violations);
    });

    return { patterns, violations };
  }

  private analyzePattern(cssContent: string, filePath: string, patternDef: PatternValidation): { analysis: PatternAnalysis, violations: LayoutViolation[] } {
    const violations: LayoutViolation[] = [];
    const lines = cssContent.split('\n');
    let patternUsage = 0;
    let compliantUsage = 0;
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Look for pattern usage
    const patternSelector = new RegExp(`\\.${patternDef.pattern}\\b`, 'gi');
    
    lines.forEach((line, lineIndex) => {
      if (patternSelector.test(line)) {
        patternUsage++;
        
        // Check if required properties are present in the surrounding context
        const contextStart = Math.max(0, lineIndex - 5);
        const contextEnd = Math.min(lines.length, lineIndex + 10);
        const context = lines.slice(contextStart, contextEnd).join('\n');
        
        const hasRequiredProps = patternDef.requiredProperties.every(prop => 
          context.includes(prop)
        );
        
        const hasForbiddenProps = patternDef.forbiddenProperties.some(prop => 
          context.includes(prop)
        );

        if (hasRequiredProps && !hasForbiddenProps) {
          compliantUsage++;
        } else {
          if (!hasRequiredProps) {
            issues.push(`Missing required properties: ${patternDef.requiredProperties.join(', ')}`);
            violations.push({
              type: 'pattern',
              severity: 'error',
              message: `Pattern "${patternDef.pattern}" missing required properties`,
              location: {
                file: filePath,
                line: lineIndex + 1,
                column: 0
              },
              suggestion: `Add required properties: ${patternDef.requiredProperties.join(', ')}`,
              autoFixable: true,
              rule: `${patternDef.pattern} Pattern Compliance`
            });
          }
          
          if (hasForbiddenProps) {
            issues.push(`Contains forbidden properties`);
            violations.push({
              type: 'pattern',
              severity: 'warning',
              message: `Pattern "${patternDef.pattern}" contains forbidden properties`,
              location: {
                file: filePath,
                line: lineIndex + 1,
                column: 0
              },
              suggestion: `Remove forbidden properties: ${patternDef.forbiddenProperties.join(', ')}`,
              autoFixable: true,
              rule: `${patternDef.pattern} Pattern Compliance`
            });
          }
        }
      }
    });

    const compliance = patternUsage > 0 ? (compliantUsage / patternUsage) * 100 : 100;

    if (compliance < 80) {
      recommendations.push(`Improve ${patternDef.pattern} pattern compliance`);
    }

    return {
      analysis: {
        pattern: patternDef.pattern,
        usage: patternUsage,
        compliance: Math.round(compliance),
        issues,
        recommendations
      },
      violations
    };
  }

  /**
   * Validates accessibility compliance in layout
   */
  private validateAccessibility(cssContent: string, filePath: string): LayoutViolation[] {
    const violations: LayoutViolation[] = [];
    const lines = cssContent.split('\n');

    // Check for minimum touch target sizes
    const touchTargetPattern = /(width|height|min-width|min-height):\s*(\d+(?:\.\d+)?(?:px|rem|em))/gi;
    
    lines.forEach((line, lineIndex) => {
      let match;
      while ((match = touchTargetPattern.exec(line)) !== null) {
        const property = match[1];
        const value = match[2];
        const pxValue = this.convertToPx(value);
        
        if (pxValue < 44 && (property === 'width' || property === 'height')) {
          violations.push({
            type: 'accessibility',
            severity: 'warning',
            message: `Touch target size "${value}" is below minimum 44px recommendation`,
            location: {
              file: filePath,
              line: lineIndex + 1,
              column: match.index + 1
            },
            suggestion: 'Increase touch target size to at least 44px',
            autoFixable: false,
            rule: 'Minimum Touch Target Size'
          });
        }
      }
    });

    // Check for excessive line lengths
    const maxWidthPattern = /max-width:\s*(\d+(?:\.\d+)?(?:ch|em|rem))/gi;
    
    lines.forEach((line, lineIndex) => {
      let match;
      while ((match = maxWidthPattern.exec(line)) !== null) {
        const value = match[1];
        const unit = value.match(/(ch|em|rem)$/)?.[1];
        const numValue = parseFloat(value);
        
        if (unit === 'ch' && numValue > 75) {
          violations.push({
            type: 'accessibility',
            severity: 'info',
            message: `Line length "${value}" exceeds recommended 75ch maximum`,
            location: {
              file: filePath,
              line: lineIndex + 1,
              column: match.index + 1
            },
            suggestion: 'Consider reducing max-width to 75ch or less for better readability',
            autoFixable: true,
            rule: 'Maximum Line Length'
          });
        }
      }
    });

    return violations;
  }

  /**
   * Generates comprehensive layout compliance report
   */
  private generateLayoutCompliance(violations: LayoutViolation[], cssContent: string): LayoutCompliance {
    const breakpointViolations = violations.filter(v => v.type === 'breakpoint');
    const containerViolations = violations.filter(v => v.type === 'container');
    const gridViolations = violations.filter(v => v.type === 'grid');
    const patternViolations = violations.filter(v => v.type === 'pattern');
    const accessibilityViolations = violations.filter(v => v.type === 'accessibility');
    const criticalIssues = violations.filter(v => v.severity === 'error').length;

    // Estimate total layout elements for compliance calculation
    const totalElements = this.estimateLayoutElements(cssContent);

    const breakpointCompliance = Math.max(0, 100 - (breakpointViolations.length / Math.max(1, totalElements * 0.1)) * 100);
    const containerCompliance = Math.max(0, 100 - (containerViolations.length / Math.max(1, totalElements * 0.2)) * 100);
    const gridCompliance = Math.max(0, 100 - (gridViolations.length / Math.max(1, totalElements * 0.3)) * 100);
    const patternCompliance = Math.max(0, 100 - (patternViolations.length / Math.max(1, totalElements * 0.2)) * 100);
    const accessibilityCompliance = Math.max(0, 100 - (accessibilityViolations.length / Math.max(1, totalElements * 0.2)) * 100);

    const overallCompliance = (breakpointCompliance + containerCompliance + gridCompliance + patternCompliance + accessibilityCompliance) / 5;

    return {
      breakpointCompliance: Math.round(breakpointCompliance),
      containerCompliance: Math.round(containerCompliance),
      gridCompliance: Math.round(gridCompliance),
      patternCompliance: Math.round(patternCompliance),
      accessibilityCompliance: Math.round(accessibilityCompliance),
      overallCompliance: Math.round(overallCompliance),
      totalIssues: violations.length,
      criticalIssues
    };
  }

  // Helper methods
  private isStandardBreakpoint(value: string): boolean {
    const standardBreakpoints = Object.values(this.layoutTokens.breakpoints).filter(bp => bp !== '0px');
    return standardBreakpoints.includes(value);
  }

  private isStandardContainerWidth(value: string): boolean {
    return Object.values(this.layoutTokens.containers.maxWidths).includes(value);
  }

  private isStandardSpacing(value: string): boolean {
    // This would reference the spacing tokens from the main design system
    const standardSpacing = ['0.5rem', '1rem', '1.5rem', '2rem', '3rem'];
    return standardSpacing.includes(value);
  }

  private hasHardcodedGridValues(template: string): boolean {
    // Check for hardcoded pixel values in grid templates
    return /\d+px/.test(template) && !template.includes('minmax') && !template.includes('auto-fit');
  }

  private convertToPx(value: string): number {
    if (value.endsWith('px')) {
      return parseFloat(value);
    }
    if (value.endsWith('rem')) {
      return parseFloat(value) * 16; // Assuming 16px base
    }
    if (value.endsWith('em')) {
      return parseFloat(value) * 16; // Simplified assumption
    }
    return parseFloat(value);
  }

  private estimateLayoutElements(cssContent: string): number {
    // Simple heuristic to estimate layout-related elements
    const selectors = cssContent.match(/[.#][\w-]+/g) || [];
    const mediaQueries = cssContent.match(/@media/g) || [];
    const gridDeclarations = cssContent.match(/display:\s*grid/g) || [];
    const flexDeclarations = cssContent.match(/display:\s*flex/g) || [];
    
    return selectors.length + mediaQueries.length + gridDeclarations.length + flexDeclarations.length;
  }

  private suggestBreakpoint(value: string): string {
    const breakpoints = this.layoutTokens.breakpoints;
    const pxValue = this.convertToPx(value);
    
    if (pxValue <= 640) return `Use ${breakpoints.sm} (sm breakpoint)`;
    if (pxValue <= 768) return `Use ${breakpoints.md} (md breakpoint)`;
    if (pxValue <= 1024) return `Use ${breakpoints.lg} (lg breakpoint)`;
    if (pxValue <= 1280) return `Use ${breakpoints.xl} (xl breakpoint)`;
    return `Use ${breakpoints['2xl']} (2xl breakpoint)`;
  }

  private suggestContainerWidth(value: string): string {
    const containers = this.layoutTokens.containers.maxWidths;
    const pxValue = this.convertToPx(value);
    
    if (pxValue <= 640) return `Use var(--container-sm) or ${containers.sm}`;
    if (pxValue <= 768) return `Use var(--container-md) or ${containers.md}`;
    if (pxValue <= 1024) return `Use var(--container-lg) or ${containers.lg}`;
    if (pxValue <= 1280) return `Use var(--container-xl) or ${containers.xl}`;
    return `Use var(--container-2xl) or ${containers['2xl']}`;
  }

  private suggestSpacingToken(value: string): string {
    const pxValue = this.convertToPx(value);
    
    if (pxValue <= 8) return 'var(--spacing-2) or 0.5rem';
    if (pxValue <= 16) return 'var(--spacing-4) or 1rem';
    if (pxValue <= 24) return 'var(--spacing-6) or 1.5rem';
    if (pxValue <= 32) return 'var(--spacing-8) or 2rem';
    return 'var(--spacing-12) or 3rem';
  }
}