/**
 * Documentation System Types
 * Defines interfaces for component documentation, design token docs, and style guidelines
 */

import { DesignTokenSystem } from '../design-tokens/types';

// Component Documentation Types
export interface ComponentDocumentation {
  components: ComponentSpec[];
  metadata: DocumentationMetadata;
  navigation: NavigationStructure;
}

export interface ComponentSpec {
  name: string;
  description: string;
  category: ComponentCategory;
  props: PropSpec[];
  variants: ComponentVariant[];
  states: ComponentState[];
  examples: ComponentExample[];
  usage: UsageGuidelines;
  accessibility: AccessibilitySpec;
  designTokens: ComponentTokenUsage;
}

export interface DocumentationMetadata {
  version: string;
  generatedAt: string;
  lastUpdated: string;
  author: string;
  designSystemVersion: string;
}

export interface NavigationStructure {
  sections: NavigationSection[];
  categories: ComponentCategory[];
}

export interface NavigationSection {
  title: string;
  slug: string;
  items: NavigationItem[];
}

export interface NavigationItem {
  title: string;
  slug: string;
  component?: string;
  category?: ComponentCategory;
}

export type ComponentCategory = 
  | 'layout'
  | 'navigation'
  | 'input'
  | 'display'
  | 'feedback'
  | 'overlay'
  | 'media'
  | 'typography'
  | 'utility';

export interface PropSpec {
  name: string;
  type: string;
  required: boolean;
  defaultValue?: string;
  description: string;
  examples: string[];
  validation?: PropValidation;
}

export interface PropValidation {
  pattern?: string;
  min?: number;
  max?: number;
  options?: string[];
}

export interface ComponentVariant {
  name: string;
  description: string;
  props: Record<string, any>;
  preview: string;
  code: string;
  designTokens: string[];
}

export interface ComponentState {
  name: string;
  description: string;
  trigger: string;
  visualChanges: string[];
  accessibility: string[];
}

export interface ComponentExample {
  title: string;
  description: string;
  code: string;
  preview: string;
  interactive: boolean;
  variants?: ExampleVariant[];
}

export interface ExampleVariant {
  name: string;
  props: Record<string, any>;
  description: string;
}

export interface UsageGuidelines {
  when: string[];
  whenNot: string[];
  bestPractices: string[];
  commonMistakes: string[];
  relatedComponents: string[];
}

export interface AccessibilitySpec {
  ariaLabels: AriaLabelSpec[];
  keyboardNavigation: KeyboardSpec[];
  screenReader: ScreenReaderSpec[];
  colorContrast: ContrastSpec[];
  focusManagement: FocusSpec[];
}

export interface AriaLabelSpec {
  attribute: string;
  purpose: string;
  example: string;
  required: boolean;
}

export interface KeyboardSpec {
  key: string;
  action: string;
  context: string;
}

export interface ScreenReaderSpec {
  announcement: string;
  context: string;
  timing: 'immediate' | 'polite' | 'assertive';
}

export interface ContrastSpec {
  element: string;
  ratio: string;
  level: 'AA' | 'AAA';
}

export interface FocusSpec {
  element: string;
  behavior: string;
  visual: string;
}

export interface ComponentTokenUsage {
  colors: string[];
  typography: string[];
  spacing: string[];
  layout: string[];
}

// Design Token Documentation Types
export interface DesignTokenDocumentation {
  tokens: DesignTokenSystem;
  sections: TokenSection[];
  examples: TokenExample[];
  usage: TokenUsageGuidelines;
  migration: MigrationGuide;
}

export interface TokenSection {
  category: TokenCategory;
  title: string;
  description: string;
  tokens: TokenDocEntry[];
  examples: TokenExample[];
}

export type TokenCategory = 'color' | 'typography' | 'spacing' | 'layout' | 'semantic';

export interface TokenDocEntry {
  name: string;
  value: string;
  description: string;
  usage: string[];
  examples: string[];
  deprecated?: boolean;
  replacement?: string;
}

export interface TokenExample {
  title: string;
  description: string;
  tokens: string[];
  code: CodeExample;
  preview: string;
  category: TokenCategory;
}

export interface CodeExample {
  css: string;
  scss?: string;
  javascript?: string;
  typescript?: string;
  tailwind?: string;
}

export interface TokenUsageGuidelines {
  naming: NamingConvention[];
  hierarchy: HierarchyRule[];
  combinations: TokenCombination[];
  antiPatterns: AntiPattern[];
}

export interface NamingConvention {
  pattern: string;
  description: string;
  examples: string[];
  category: TokenCategory;
}

export interface HierarchyRule {
  rule: string;
  description: string;
  examples: string[];
  violations: string[];
}

export interface TokenCombination {
  tokens: string[];
  description: string;
  example: string;
  context: string;
}

export interface AntiPattern {
  description: string;
  example: string;
  solution: string;
  tokens: string[];
}

export interface MigrationGuide {
  version: string;
  changes: MigrationChange[];
  steps: MigrationStep[];
  automation: AutomationScript[];
}

export interface MigrationChange {
  type: 'added' | 'changed' | 'deprecated' | 'removed';
  token: string;
  oldValue?: string;
  newValue?: string;
  reason: string;
  impact: 'low' | 'medium' | 'high';
}

export interface MigrationStep {
  order: number;
  title: string;
  description: string;
  commands: string[];
  validation: string[];
}

export interface AutomationScript {
  name: string;
  description: string;
  script: string;
  usage: string;
}

// Style Guidelines Types
export interface StyleGuidelines {
  sections: GuidelineSection[];
  toneAndVoice: ToneGuidelines;
  terminology: TerminologyStandards;
  messaging: MessagingPatterns;
  examples: GuidelineExample[];
}

export interface GuidelineSection {
  title: string;
  slug: string;
  description: string;
  rules: StyleRule[];
  examples: GuidelineExample[];
}

export interface StyleRule {
  title: string;
  description: string;
  do: string[];
  dont: string[];
  examples: RuleExample[];
}

export interface RuleExample {
  correct: string;
  incorrect: string;
  explanation: string;
}

export interface ToneGuidelines {
  principles: TonePrinciple[];
  contexts: ToneContext[];
  examples: ToneExample[];
  voice: VoiceCharacteristics;
}

export interface TonePrinciple {
  name: string;
  description: string;
  application: string[];
  examples: string[];
}

export interface ToneContext {
  context: string;
  tone: string;
  characteristics: string[];
  examples: string[];
  avoid: string[];
}

export interface ToneExample {
  context: string;
  good: string;
  bad: string;
  explanation: string;
}

export interface VoiceCharacteristics {
  personality: string[];
  values: string[];
  style: string[];
  avoid: string[];
}

export interface TerminologyStandards {
  dictionary: TermEntry[];
  categories: TermCategory[];
  rules: TerminologyRule[];
  consistency: ConsistencyCheck[];
}

export interface TermEntry {
  term: string;
  definition: string;
  category: string;
  usage: string[];
  alternatives: string[];
  avoid: string[];
  context: string[];
}

export interface TermCategory {
  name: string;
  description: string;
  terms: string[];
  rules: string[];
}

export interface TerminologyRule {
  rule: string;
  description: string;
  examples: string[];
  exceptions: string[];
}

export interface ConsistencyCheck {
  pattern: string;
  description: string;
  correct: string;
  incorrect: string[];
}

export interface MessagingPatterns {
  patterns: MessagePattern[];
  contexts: MessageContext[];
  templates: MessageTemplate[];
  validation: MessageValidation[];
}

export interface MessagePattern {
  name: string;
  description: string;
  structure: string;
  examples: string[];
  usage: string[];
}

export interface MessageContext {
  context: string;
  tone: string;
  patterns: string[];
  examples: MessageExample[];
}

export interface MessageExample {
  situation: string;
  message: string;
  explanation: string;
  alternatives?: string[];
}

export interface MessageTemplate {
  name: string;
  template: string;
  variables: TemplateVariable[];
  examples: string[];
  usage: string;
}

export interface TemplateVariable {
  name: string;
  type: string;
  description: string;
  examples: string[];
}

export interface MessageValidation {
  rule: string;
  description: string;
  pattern: string;
  examples: ValidationExample[];
}

export interface ValidationExample {
  input: string;
  valid: boolean;
  explanation: string;
  correction?: string;
}

export interface GuidelineExample {
  title: string;
  description: string;
  good: ExampleContent;
  bad?: ExampleContent;
  explanation: string;
  category: string;
}

export interface ExampleContent {
  text: string;
  code?: string;
  visual?: string;
  context: string;
}

// Interactive Examples Types
export interface InteractiveExample {
  id: string;
  title: string;
  description: string;
  component: string;
  variants: ExampleVariant[];
  controls: ExampleControl[];
  code: LiveCodeExample;
  preview: PreviewConfig;
}

export interface ExampleControl {
  name: string;
  type: ControlType;
  label: string;
  defaultValue: any;
  options?: ControlOption[];
  min?: number;
  max?: number;
  step?: number;
}

export type ControlType = 
  | 'text'
  | 'number'
  | 'boolean'
  | 'select'
  | 'color'
  | 'range'
  | 'textarea';

export interface ControlOption {
  label: string;
  value: any;
}

export interface LiveCodeExample {
  template: string;
  imports: string[];
  dependencies: string[];
  editable: boolean;
}

export interface PreviewConfig {
  background: string;
  padding: string;
  responsive: boolean;
  darkMode: boolean;
  rtl: boolean;
}