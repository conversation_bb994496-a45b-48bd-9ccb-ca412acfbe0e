import React from "react";

import useNavigation from "@/hooks/useNavigation";
import { cn } from "@/lib/utils";

interface NavigationLinkProps {
  to: string;
  children: React.ReactNode;
  className?: string;
  loadingTime?: number;
  onClick?: () => void;
  mobileClassName?: string;
  desktopClassName?: string;
  showLoadingText?: boolean;
}

/**
 * A responsive link component that shows loading indicators and adapts to screen size
 */
const NavigationLink = ({
  to,
  children,
  className = "",
  loadingTime = 500,
  onClick,
  mobileClassName = "",
  desktopClassName = "",
  showLoadingText = true
}: NavigationLinkProps) => {
  const { isNavigating, navigateTo, currentDestination } = useNavigation();

  
  // Check if this specific link's destination is currently loading
  const isThisLinkLoading = isNavigating && currentDestination === to;

  /**
   * Handle click events with proper mobile/desktop behavior
   */
  const handleClick = (e: React.MouseEvent) => {
    // Only hijack plain left-clicks
    if (
      e.button === 0 &&            // left button
      !e.metaKey && !e.ctrlKey && !e.altKey && !e.shiftKey
    ) {
      e.preventDefault();
      if (onClick) onClick();
      navigateTo(to, loadingTime);
    }
  };

  /**
   * Handle keyboard navigation with enhanced accessibility
   */
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      if (onClick) onClick();
      navigateTo(to, loadingTime);
    }
  };

  /**
   * Generate responsive loading indicator
   */
  const renderLoadingIndicator = () => {
    return (
      <div className="flex items-center justify-center">
        <div className={cn(
          "border-2 border-current border-t-transparent rounded-full animate-spin",
          "h-4 w-4 md:h-3 md:w-3", // Responsive spinner size
          "mr-3 md:mr-2" // Responsive spacing
        )}></div>
        {showLoadingText && (
          <span className="text-sm md:text-xs">
            Loading...
          </span>
        )}
      </div>
    );
  };

  // Combine responsive classes
  const responsiveClassName = cn(
    "cursor-pointer transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 rounded-md",
    className,
    mobileClassName, // Apply mobile classes by default
    `md:${desktopClassName}`, // Apply desktop classes on medium screens and up
    isThisLinkLoading && "opacity-75 pointer-events-none",
    // Enhanced touch targets for mobile
    "min-h-[44px] min-w-[44px] flex items-center justify-center md:min-h-0 md:min-w-0 md:inline-flex"
  );

  return (
    <a
      href={to}
      className={responsiveClassName}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      role="button"
      tabIndex={0}
      aria-disabled={isNavigating}
      aria-label={isThisLinkLoading ? `Loading ${children}` : undefined}
    >
      {isThisLinkLoading ? renderLoadingIndicator() : children}
    </a>
  );
};

export default NavigationLink;
