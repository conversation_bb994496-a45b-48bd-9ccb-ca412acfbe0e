import { ComponentStandardizationSystem } from '../component-standardization';
import { FileInfo, IssueType, Severity, Category } from '../../types';
import { expect } from 'vitest';
import { it } from 'vitest';
import { expect } from 'vitest';
import { it } from 'vitest';
import { expect } from 'vitest';
import { it } from 'vitest';
import { expect } from 'vitest';
import { it } from 'vitest';
import { describe } from 'vitest';
import { expect } from 'vitest';
import { expect } from 'vitest';
import { expect } from 'vitest';
import { expect } from 'vitest';
import { expect } from 'vitest';
import { expect } from 'vitest';
import { expect } from 'vitest';
import { it } from 'vitest';
import { expect } from 'vitest';
import { it } from 'vitest';
import { describe } from 'vitest';
import { expect } from 'vitest';
import { expect } from 'vitest';
import { it } from 'vitest';
import { expect } from 'vitest';
import { expect } from 'vitest';
import { it } from 'vitest';
import { expect } from 'vitest';
import { expect } from 'vitest';
import { it } from 'vitest';
import { expect } from 'vitest';
import { it } from 'vitest';
import { expect } from 'vitest';
import { it } from 'vitest';
import { expect } from 'vitest';
import { expect } from 'vitest';
import { it } from 'vitest';
import { expect } from 'vitest';
import { expect } from 'vitest';
import { it } from 'vitest';
import { expect } from 'vitest';
import { expect } from 'vitest';
import { expect } from 'vitest';
import { it } from 'vitest';
import { expect } from 'vitest';
import { expect } from 'vitest';
import { expect } from 'vitest';
import { it } from 'vitest';
import { expect } from 'vitest';
import { expect } from 'vitest';
import { it } from 'vitest';
import { expect } from 'vitest';
import { expect } from 'vitest';
import { expect } from 'vitest';
import { it } from 'vitest';
import { expect } from 'vitest';
import { expect } from 'vitest';
import { it } from 'vitest';
import { describe } from 'vitest';
import { beforeEach } from 'vitest';
import { describe } from 'vitest';

describe('ComponentStandardizationSystem', () => {
  let analyzer: ComponentStandardizationSystem;

  beforeEach(() => {
    analyzer = new ComponentStandardizationSystem();
  });

  describe('analyze', () => {
    it('should validate components against predefined standards', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
export const Button = ({ variant, size }) => {
  return (
    <button className="btn">
      Click me
    </button>
  );
};
          `,
          extension: '.tsx',
          size: 200,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues.length).toBeGreaterThan(0);
      const requiredPropsIssue = result.issues.find(issue => 
        issue.description.includes('Required Props Validation')
      );
      expect(requiredPropsIssue).toBeDefined();
    });

    it('should detect styling approach inconsistencies', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
export const Button = ({ children, onClick }) => {
  return (
    <button style={{ padding: '10px', background: 'blue' }} onClick={onClick}>
      {children}
    </button>
  );
};
          `,
          extension: '.tsx',
          size: 200,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues.length).toBeGreaterThan(0);
      const stylingIssue = result.issues.find(issue => 
        issue.description.includes('Styling Approach Consistency')
      );
      expect(stylingIssue).toBeDefined();
      expect(stylingIssue?.severity).toBe(Severity.HIGH);
    });

    it('should validate interaction patterns', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
import styles from './Button.module.css';

export const Button = ({ children }) => {
  return (
    <button className={styles.button}>
      {children}
    </button>
  );
};
          `,
          extension: '.tsx',
          size: 200,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues.length).toBeGreaterThan(0);
      const interactionIssue = result.issues.find(issue => 
        issue.description.includes('Interaction Pattern Validation')
      );
      expect(interactionIssue).toBeDefined();
    });

    it('should validate accessibility compliance', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
import styles from './Button.module.css';

export const Button = ({ children, onClick }) => {
  return (
    <button className={styles.button} onClick={onClick}>
      {children}
    </button>
  );
};
          `,
          extension: '.tsx',
          size: 200,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues.length).toBeGreaterThan(0);
      const accessibilityIssue = result.issues.find(issue => 
        issue.description.includes('Accessibility Compliance')
      );
      expect(accessibilityIssue).toBeDefined();
      expect(accessibilityIssue?.severity).toBe(Severity.HIGH);
    });

    it('should suggest standardization for components without standards', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/CustomWidget.tsx',
          content: `
export const CustomWidget = ({ title, content }) => {
  return (
    <div className="widget">
      <h3>{title}</h3>
      <p>{content}</p>
    </div>
  );
};
          `,
          extension: '.tsx',
          size: 200,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues.length).toBeGreaterThan(0);
      const standardizationIssue = result.issues.find(issue => 
        issue.description.includes('lacks standardization guidelines')
      );
      expect(standardizationIssue).toBeDefined();
      expect(standardizationIssue?.severity).toBe(Severity.MEDIUM);
    });

    it('should validate Input components against standards', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Input.tsx',
          content: `
export const Input = ({ placeholder, disabled }) => {
  return (
    <input 
      placeholder={placeholder}
      disabled={disabled}
    />
  );
};
          `,
          extension: '.tsx',
          size: 200,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues.length).toBeGreaterThan(0);
      const requiredPropsIssue = result.issues.find(issue => 
        issue.description.includes('Required Props Validation')
      );
      expect(requiredPropsIssue).toBeDefined();
    });

    it('should validate Card components against standards', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Card.tsx',
          content: `
export const Card = ({ title }) => {
  return (
    <div style={{ padding: '16px' }}>
      <h3>{title}</h3>
    </div>
  );
};
          `,
          extension: '.tsx',
          size: 200,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues.length).toBeGreaterThan(0);
      const requiredPropsIssue = result.issues.find(issue => 
        issue.description.includes('Required Props Validation')
      );
      expect(requiredPropsIssue).toBeDefined();
    });

    it('should handle components that meet all standards', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
import styles from './Button.module.css';

export const Button = ({ children, variant = 'primary', size = 'medium', disabled, loading, onClick, onKeyDown }) => {
  return (
    <button 
      className={styles.button}
      onClick={onClick}
      onKeyDown={onKeyDown}
      disabled={disabled}
      role="button"
      tabIndex={0}
      aria-disabled={disabled ? 'true' : 'false'}
    >
      {children}
    </button>
  );
};
          `,
          extension: '.tsx',
          size: 400,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      // Should have fewer issues since this component meets most standards
      const requiredPropsIssues = result.issues.filter(issue => 
        issue.description.includes('Required Props Validation')
      );
      expect(requiredPropsIssues.length).toBe(0);
    });

    it('should exclude test and story files from analysis', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.test.tsx',
          content: `
export const Button = () => <button>Test Button</button>;
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        },
        {
          path: 'src/components/Button.stories.tsx',
          content: `
export const Button = () => <button>Story Button</button>;
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues).toHaveLength(0);
    });

    it('should provide actionable suggestions', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
export const Button = ({ variant, size }) => {
  return <button className="btn">Click me</button>;
};
          `,
          extension: '.tsx',
          size: 150,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      result.issues.forEach(issue => {
        expect(issue.suggestion).toBeTruthy();
        expect(issue.suggestion.length).toBeGreaterThan(10);
      });
    });

    it('should categorize issues correctly', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
export const Button = ({ variant }) => {
  return <button>Click me</button>;
};
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      result.issues.forEach(issue => {
        expect(issue.category).toBe(Category.COMPONENT);
      });

      expect(result.summary.categoryBreakdown[Category.COMPONENT]).toBe(result.issues.length);
    });

    it('should track execution time', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `export const Button = () => <button>Click</button>;`,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.executionTime).toBeGreaterThan(0);
      expect(typeof result.executionTime).toBe('number');
    });
  });

  describe('component standard management', () => {
    it('should allow adding custom component standards', () => {
      const customStandard = {
        name: 'CustomButton',
        requiredProps: ['label', 'action'],
        optionalProps: ['variant'],
        variants: [],
        stylingApproach: 'tailwind' as const,
        stateHandling: 'hooks' as const,
        interactionPatterns: []
      };

      analyzer.addComponentStandard(customStandard);
      const retrieved = analyzer.getComponentStandard('CustomButton');

      expect(retrieved).toEqual(customStandard);
    });

    it('should generate standards from implementations', () => {
      const implementations = [
        {
          name: 'TestComponent',
          filePath: 'test1.tsx',
          props: ['title', 'content', 'variant'],
          variants: ['primary', 'secondary'],
          stylingApproach: 'css-modules',
          stateHandling: 'props-only',
          interactionPatterns: ['click'],
          accessibilityAttributes: {},
          location: { filePath: 'test1.tsx', lineNumber: 1, columnNumber: 0, context: '' }
        },
        {
          name: 'TestComponent',
          filePath: 'test2.tsx',
          props: ['title', 'variant', 'disabled'],
          variants: ['primary'],
          stylingApproach: 'css-modules',
          stateHandling: 'props-only',
          interactionPatterns: ['click', 'hover'],
          accessibilityAttributes: {},
          location: { filePath: 'test2.tsx', lineNumber: 1, columnNumber: 0, context: '' }
        }
      ];

      const standard = analyzer.generateStandardFromImplementations('TestComponent', implementations);

      expect(standard.name).toBe('TestComponent');
      expect(standard.requiredProps).toContain('title');
      expect(standard.requiredProps).toContain('variant');
      expect(standard.optionalProps).toContain('content');
      expect(standard.optionalProps).toContain('disabled');
      expect(standard.stylingApproach).toBe('css-modules');
      expect(standard.stateHandling).toBe('props-only');
    });
  });

  describe('getSeverity', () => {
    it('should return HIGH severity for required props issues', () => {
      const issue = {
        id: 'test',
        type: IssueType.COMPONENT_INCONSISTENCY,
        category: Category.COMPONENT,
        description: 'Button: Required Props Validation',
        location: { filePath: 'test.tsx', lineNumber: 1, columnNumber: 0, context: '' },
        suggestion: 'Fix it',
        autoFixable: false,
        impact: { userExperience: 5, maintenanceEffort: 5, implementationComplexity: 5 }
      };

      const severity = analyzer.getSeverity(issue);
      expect(severity).toBe(Severity.HIGH);
    });

    it('should return HIGH severity for accessibility issues', () => {
      const issue = {
        id: 'test',
        type: IssueType.COMPONENT_INCONSISTENCY,
        category: Category.COMPONENT,
        description: 'Button: Accessibility Compliance',
        location: { filePath: 'test.tsx', lineNumber: 1, columnNumber: 0, context: '' },
        suggestion: 'Fix it',
        autoFixable: false,
        impact: { userExperience: 5, maintenanceEffort: 5, implementationComplexity: 5 }
      };

      const severity = analyzer.getSeverity(issue);
      expect(severity).toBe(Severity.HIGH);
    });

    it('should return MEDIUM severity for styling approach issues', () => {
      const issue = {
        id: 'test',
        type: IssueType.COMPONENT_INCONSISTENCY,
        category: Category.COMPONENT,
        description: 'Button: Styling Approach Consistency',
        location: { filePath: 'test.tsx', lineNumber: 1, columnNumber: 0, context: '' },
        suggestion: 'Fix it',
        autoFixable: false,
        impact: { userExperience: 5, maintenanceEffort: 5, implementationComplexity: 5 }
      };

      const severity = analyzer.getSeverity(issue);
      expect(severity).toBe(Severity.MEDIUM);
    });

    it('should return MEDIUM severity for standardization issues', () => {
      const issue = {
        id: 'test',
        type: IssueType.COMPONENT_INCONSISTENCY,
        category: Category.COMPONENT,
        description: 'Component lacks standardization guidelines',
        location: { filePath: 'test.tsx', lineNumber: 1, columnNumber: 0, context: '' },
        suggestion: 'Fix it',
        autoFixable: false,
        impact: { userExperience: 5, maintenanceEffort: 5, implementationComplexity: 5 }
      };

      const severity = analyzer.getSeverity(issue);
      expect(severity).toBe(Severity.MEDIUM);
    });
  });
});