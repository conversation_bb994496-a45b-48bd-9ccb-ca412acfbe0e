// Tests for prioritization and planning system

import { PrioritizationEngine } from '../prioritization-engine';
import {
  Issue,
  Severity,
  Category,
  IssueType,
  Recommendation,
  ImpactAssessment
} from '../../types';

describe('PrioritizationEngine', () => {
  let prioritizationEngine: PrioritizationEngine;
  let mockIssues: Issue[];
  let mockRecommendations: Recommendation[];

  beforeEach(() => {
    prioritizationEngine = new PrioritizationEngine();

    // Create mock impact assessments
    const lowImpact: ImpactAssessment = {
      userExperience: 3,
      maintenanceEffort: 2,
      implementationComplexity: 2
    };

    const mediumImpact: ImpactAssessment = {
      userExperience: 6,
      maintenanceEffort: 5,
      implementationComplexity: 4
    };

    const highImpact: ImpactAssessment = {
      userExperience: 9,
      maintenanceEffort: 7,
      implementationComplexity: 6
    };

    // Create mock issues with varying priorities
    mockIssues = [
      {
        id: 'critical_color_issue',
        type: IssueType.COLOR_INCONSISTENCY,
        severity: Severity.CRITICAL,
        category: Category.VISUAL,
        description: 'Critical color inconsistency',
        location: {
          filePath: 'src/components/Button.tsx',
          lineNumber: 15,
          columnNumber: 10,
          context: 'background-color: #ff0000'
        },
        suggestion: 'Use design token',
        autoFixable: true,
        impact: highImpact
      },
      {
        id: 'high_spacing_issue',
        type: IssueType.SPACING_INCONSISTENCY,
        severity: Severity.HIGH,
        category: Category.VISUAL,
        description: 'High priority spacing issue',
        location: {
          filePath: 'src/components/Card.tsx',
          lineNumber: 22,
          columnNumber: 5,
          context: 'margin: 12px'
        },
        suggestion: 'Use spacing token',
        autoFixable: true,
        impact: mediumImpact
      },
      {
        id: 'medium_typography_issue',
        type: IssueType.TYPOGRAPHY_INCONSISTENCY,
        severity: Severity.MEDIUM,
        category: Category.VISUAL,
        description: 'Medium priority typography issue',
        location: {
          filePath: 'src/components/Header.tsx',
          lineNumber: 8,
          columnNumber: 12,
          context: 'font-size: 18px'
        },
        suggestion: 'Use typography scale',
        autoFixable: false,
        impact: mediumImpact
      },
      {
        id: 'low_content_issue',
        type: IssueType.CONTENT_INCONSISTENCY,
        severity: Severity.LOW,
        category: Category.CONTENT,
        description: 'Low priority content issue',
        location: {
          filePath: 'src/components/Modal.tsx',
          lineNumber: 30,
          columnNumber: 15,
          context: 'Error message text'
        },
        suggestion: 'Use consistent messaging',
        autoFixable: false,
        impact: lowImpact
      },
      {
        id: 'accessibility_issue',
        type: IssueType.COMPONENT_INCONSISTENCY,
        severity: Severity.HIGH,
        category: Category.ACCESSIBILITY,
        description: 'Accessibility component issue',
        location: {
          filePath: 'src/components/Form.tsx',
          lineNumber: 45,
          columnNumber: 8,
          context: 'missing aria-label'
        },
        suggestion: 'Add accessibility attributes',
        autoFixable: false,
        impact: highImpact
      }
    ];

    // Create mock recommendations
    mockRecommendations = [
      {
        id: 'rec_1',
        title: 'Implement Unified Color System',
        description: 'Standardize color usage across components',
        priority: 9,
        estimatedEffort: 5,
        relatedIssues: ['critical_color_issue']
      },
      {
        id: 'rec_2',
        title: 'Standardize Spacing and Layout',
        description: 'Implement consistent spacing patterns',
        priority: 7,
        estimatedEffort: 4,
        relatedIssues: ['high_spacing_issue']
      },
      {
        id: 'rec_3',
        title: 'Unify Typography System',
        description: 'Create consistent typography hierarchy',
        priority: 6,
        estimatedEffort: 6,
        relatedIssues: ['medium_typography_issue']
      },
      {
        id: 'rec_4',
        title: 'Align Content Standards',
        description: 'Standardize content and messaging',
        priority: 4,
        estimatedEffort: 3,
        relatedIssues: ['low_content_issue']
      },
      {
        id: 'rec_5',
        title: 'Standardize Component Patterns',
        description: 'Ensure consistent component behavior',
        priority: 8,
        estimatedEffort: 8,
        relatedIssues: ['accessibility_issue']
      }
    ];
  });

  describe('createPrioritizedPlan', () => {
    it('should create a comprehensive prioritized plan', () => {
      const result = prioritizationEngine.createPrioritizedPlan(mockIssues, mockRecommendations);

      expect(result.plan).toBeDefined();
      expect(result.metrics).toBeDefined();
      expect(result.plan.tasks.length).toBe(mockRecommendations.length);
      expect(result.plan.estimatedEffort).toBeGreaterThan(0);
    });

    it('should prioritize critical issues higher', () => {
      const result = prioritizationEngine.createPrioritizedPlan(mockIssues, mockRecommendations);

      // Find tasks related to critical issues
      const criticalTask = result.plan.tasks.find(task => 
        task.description.includes('Color')
      );
      const lowPriorityTask = result.plan.tasks.find(task => 
        task.description.includes('Content')
      );

      expect(criticalTask).toBeDefined();
      expect(lowPriorityTask).toBeDefined();
      
      // Critical task should have higher priority in ordering
      expect(criticalTask!.effort).toBeGreaterThan(0);
      expect(lowPriorityTask!.effort).toBeGreaterThan(0);
    });

    it('should identify auto-fixable tasks', () => {
      const result = prioritizationEngine.createPrioritizedPlan(mockIssues, mockRecommendations);

      const autoFixableTasks = result.plan.tasks.filter(task => task.autoFixable);
      expect(autoFixableTasks.length).toBeGreaterThan(0);
    });

    it('should calculate realistic effort estimates', () => {
      const result = prioritizationEngine.createPrioritizedPlan(mockIssues, mockRecommendations);

      result.plan.tasks.forEach(task => {
        expect(task.effort).toBeGreaterThan(0);
        expect(task.effort).toBeLessThan(50); // Reasonable upper bound
      });
    });

    it('should create dependency relationships', () => {
      const result = prioritizationEngine.createPrioritizedPlan(mockIssues, mockRecommendations);

      expect(result.plan.dependencies).toBeDefined();
      expect(Array.isArray(result.plan.dependencies)).toBe(true);
    });

    it('should generate comprehensive metrics', () => {
      const result = prioritizationEngine.createPrioritizedPlan(mockIssues, mockRecommendations);

      expect(result.metrics.totalIssues).toBe(mockIssues.length);
      expect(result.metrics.criticalIssues).toBe(1); // One critical issue in mock data
      expect(result.metrics.autoFixableIssues).toBe(2); // Two auto-fixable issues
      expect(result.metrics.estimatedTotalEffort).toBeGreaterThan(0);
      expect(result.metrics.quickWins).toBeDefined();
      expect(result.metrics.highImpactTasks).toBeDefined();
    });

    it('should identify quick wins', () => {
      const result = prioritizationEngine.createPrioritizedPlan(mockIssues, mockRecommendations);

      expect(Array.isArray(result.metrics.quickWins)).toBe(true);
      expect(result.metrics.quickWins.length).toBeGreaterThanOrEqual(0);
    });

    it('should identify high impact tasks', () => {
      const result = prioritizationEngine.createPrioritizedPlan(mockIssues, mockRecommendations);

      expect(Array.isArray(result.metrics.highImpactTasks)).toBe(true);
      expect(result.metrics.highImpactTasks.length).toBeGreaterThanOrEqual(0);
    });

    it('should handle empty inputs gracefully', () => {
      const result = prioritizationEngine.createPrioritizedPlan([], []);

      expect(result.plan.tasks.length).toBe(0);
      expect(result.metrics.totalIssues).toBe(0);
      expect(result.metrics.estimatedTotalEffort).toBe(0);
    });

    it('should organize tasks into phases', () => {
      const result = prioritizationEngine.createPrioritizedPlan(mockIssues, mockRecommendations);

      expect(result.metrics.recommendedPhases).toBeGreaterThan(0);
      expect(result.metrics.recommendedPhases).toBeLessThanOrEqual(4); // Max 4 phases
    });
  });

  describe('priority scoring', () => {
    it('should score critical issues higher than low priority issues', () => {
      const criticalIssue = mockIssues.find(i => i.severity === Severity.CRITICAL)!;
      const lowIssue = mockIssues.find(i => i.severity === Severity.LOW)!;

      const result = prioritizationEngine.createPrioritizedPlan([criticalIssue, lowIssue], [
        mockRecommendations[0], // Color system (related to critical)
        mockRecommendations[3]  // Content standards (related to low)
      ]);

      const criticalTask = result.plan.tasks.find(t => t.description.includes('Color'));
      const lowTask = result.plan.tasks.find(t => t.description.includes('Content'));

      expect(criticalTask).toBeDefined();
      expect(lowTask).toBeDefined();
    });

    it('should consider accessibility issues as high priority', () => {
      const accessibilityIssue = mockIssues.find(i => i.category === Category.ACCESSIBILITY)!;
      
      const result = prioritizationEngine.createPrioritizedPlan([accessibilityIssue], [
        mockRecommendations[4] // Component patterns (related to accessibility)
      ]);

      expect(result.plan.tasks.length).toBe(1);
      expect(result.plan.tasks[0].effort).toBeGreaterThan(0);
    });

    it('should give bonus to auto-fixable issues', () => {
      const autoFixableIssue = mockIssues.find(i => i.autoFixable)!;
      const nonAutoFixableIssue = mockIssues.find(i => !i.autoFixable)!;

      const result = prioritizationEngine.createPrioritizedPlan(
        [autoFixableIssue, nonAutoFixableIssue], 
        [mockRecommendations[0], mockRecommendations[2]]
      );

      const autoFixableTask = result.plan.tasks.find(t => t.autoFixable);
      expect(autoFixableTask).toBeDefined();
    });
  });

  describe('effort estimation', () => {
    it('should provide realistic effort estimates', () => {
      const result = prioritizationEngine.createPrioritizedPlan(mockIssues, mockRecommendations);

      result.plan.tasks.forEach(task => {
        expect(task.effort).toBeGreaterThan(0);
        expect(task.effort).toBeLessThan(100); // Reasonable upper bound
      });
    });

    it('should consider issue complexity in effort calculation', () => {
      // Create a complex issue
      const complexIssue: Issue = {
        ...mockIssues[0],
        id: 'complex_issue',
        type: IssueType.COMPONENT_INCONSISTENCY, // More complex type
        impact: {
          userExperience: 8,
          maintenanceEffort: 9,
          implementationComplexity: 9
        }
      };

      const complexRecommendation: Recommendation = {
        id: 'complex_rec',
        title: 'Complex Component Standardization',
        description: 'Complex component work',
        priority: 8,
        estimatedEffort: 10,
        relatedIssues: ['complex_issue']
      };

      const result = prioritizationEngine.createPrioritizedPlan([complexIssue], [complexRecommendation]);

      expect(result.plan.tasks[0].effort).toBeGreaterThan(5); // Should be substantial effort
    });

    it('should calculate total effort correctly', () => {
      const result = prioritizationEngine.createPrioritizedPlan(mockIssues, mockRecommendations);

      const calculatedTotal = result.plan.tasks.reduce((sum, task) => sum + task.effort, 0);
      // Allow for small rounding differences due to hour-to-day conversions
      expect(Math.abs(result.plan.estimatedEffort - calculatedTotal)).toBeLessThanOrEqual(1);
    });
  });

  describe('dependency analysis', () => {
    it('should create logical dependencies between tasks', () => {
      const result = prioritizationEngine.createPrioritizedPlan(mockIssues, mockRecommendations);

      expect(result.plan.dependencies.length).toBeGreaterThanOrEqual(0);
      
      // Each dependency should have valid task references
      result.plan.dependencies.forEach(dep => {
        expect(dep.taskId).toBeDefined();
        expect(Array.isArray(dep.dependsOn)).toBe(true);
      });
    });

    it('should not create circular dependencies', () => {
      const result = prioritizationEngine.createPrioritizedPlan(mockIssues, mockRecommendations);

      // Simple check: no task should depend on itself
      result.plan.dependencies.forEach(dep => {
        expect(dep.dependsOn).not.toContain(dep.taskId);
      });
    });
  });
});