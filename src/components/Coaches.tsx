
// SECTION: Imports
// ==================================================================================
import { cn } from "@/lib/utils";
import { useRef, useState, useEffect } from "react";
import { useRevealOnScroll } from "@/hooks/use-reveal-on-scroll";
import { Button } from "@/components/ui/button";
import styles from "./Coaches.module.css";
import coachesData, { Coach } from "@/data/coaches"; // Import centralized data and type
import ResponsiveImage from "@/components/ui/responsive-image";
import CoachesSkeleton from "@/components/skeletons/CoachesSkeleton"; // Import the skeleton component

// SECTION: Interface Definition
// ==================================================================================
// Props for CoachCard - delay is now generated, type comes from Coach interface
interface CoachCardProps extends Omit<Coach, 'id' | 'specialties' | 'featured' | 'order'> {
  delay: number;
}

// SECTION: CoachCard Component
// ==================================================================================
const CoachCard = ({ name, title, bio, image, delay, type }: CoachCardProps) => {
  // --- Hooks ---
  const { ref: coachRef, isVisible } = useRevealOnScroll<HTMLDivElement>({
    threshold: 0.1,
    delay: delay, // Pass the delay from props
  });

  // --- Component JSX ---
  return (
    <div
      ref={coachRef}
      className={cn(
        "bg-white rounded-lg overflow-hidden shadow-lg reveal flex flex-col",
        "hover:shadow-xl transition-all duration-300",
        { active: isVisible } // Apply active class based on visibility
      )}
    >
      <div className="h-64 overflow-hidden">
        <ResponsiveImage
          src={image}
          alt={`Coach ${name}`}
          className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
          loading="lazy"
          sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
          sources={[
            { src: image, width: 400 }, // Mobile/small
            { src: image, width: 600 }, // Tablet/medium
            { src: image, width: 800 }, // Desktop/large
          ]}
        />
      </div>
      <div className="p-4 md:p-6 flex-grow flex flex-col">
        <h3 className="text-lg md:text-xl font-bold mb-1 text-elev-navy">{name}</h3>
        <p className="text-elev-blue font-medium mb-3 text-sm md:text-base">{title}</p>
        <p className="text-elev-text-secondary flex-grow text-sm md:text-base">{bio}</p>
      </div>
    </div>
  );
};

// SECTION: Coaches Component (Main)
// ==================================================================================
const Coaches = () => {
  // --- State ---
  const [loading, setLoading] = useState(true);
  const [coaches, setCoaches] = useState<Coach[]>([]);
  const [filter, setFilter] = useState("all");

  // --- Hooks ---
  const { ref: sectionRef, isVisible: isSectionVisible } = useRevealOnScroll<HTMLDivElement>({ threshold: 0.1 });

  // --- Effects ---
  useEffect(() => {
    // Simulate data loading delay (remove in production and use actual data fetching)
    const timer = setTimeout(() => {
      setCoaches(coachesData);
      setLoading(false);
    }, 800); // Simulate network delay

    return () => clearTimeout(timer);
  }, []);

  // --- Component JSX ---
  if (loading) {
    return <CoachesSkeleton />;
  }

  // Use imported coaches data
  // No need for playerCoaches or goalieCoaches variables, filtering is done inline

  // --- Component JSX ---
  return (
    <section className="section-padding bg-white" id="coaches">
      <div className="container mx-auto px-4 md:px-6 lg:px-8">
        <div
          ref={sectionRef}
          className={cn("text-center mb-8 md:mb-12 reveal", { active: isSectionVisible })}
        >
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-4 text-elev-navy px-4">
            Meet Our Expert Coaches
          </h2>
          <p className="text-base md:text-lg text-elev-text-secondary max-w-3xl mx-auto mb-6 md:mb-8 px-4">
            Our team of experienced professionals brings decades of hockey knowledge and elite performance training to develop champions.
          </p>
          <div className="flex flex-wrap justify-center gap-2 md:gap-4 mb-8 md:mb-12 px-4">
            <Button
              variant={filter === "all" ? "default" : "outline"}
              className="text-sm md:text-base px-3 md:px-4 py-2"
              onClick={() => setFilter("all")}
            >
              All Coaches
            </Button>
            <Button
              variant={filter === "player" ? "default" : "outline"}
              className="text-sm md:text-base px-3 md:px-4 py-2"
              onClick={() => setFilter("player")}
            >
              Player Coaches
            </Button>
            <Button
              variant={filter === "goalie" ? "default" : "outline"}
              className="text-sm md:text-base px-3 md:px-4 py-2"
              onClick={() => setFilter("goalie")}
            >
              Goalie Coaches
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 md:gap-8">
          {coaches
            .filter((coach) => filter === "all" || coach.type === filter)
            .map((coach, index) => (
              <CoachCard
                key={coach.id} // Use coach.id as key
                name={coach.name}
                title={coach.title}
                bio={coach.bio}
                image={coach.image}
                type={coach.type}
                delay={index * 100} // Generate delay dynamically
              />
            ))}
        </div>
      </div>
    </section>
  );
};

export default Coaches;
