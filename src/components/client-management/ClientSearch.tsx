import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Search, X } from 'lucide-react';

/**
 * ClientSearch component provides search functionality for client management
 * Includes debounced search input and clear functionality
 */
interface ClientSearchProps {
  onSearch: (query: string) => void;
  placeholder?: string;
  debounceMs?: number;
  initialValue?: string;
  className?: string;
}

const ClientSearch: React.FC<ClientSearchProps> = ({
  onSearch,
  placeholder = "Search clients by name, email, or phone...",
  debounceMs = 300,
  initialValue = "",
  className = ""
}) => {
  const [searchQuery, setSearchQuery] = useState(initialValue);
  const [debouncedQuery, setDebouncedQuery] = useState(initialValue);

  /**
   * Debounce search query to avoid excessive API calls
   */
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [searchQuery, debounceMs]);

  /**
   * Trigger search when debounced query changes
   */
  useEffect(() => {
    onSearch(debouncedQuery);
  }, [debouncedQuery, onSearch]);

  /**
   * Handle input change
   */
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  /**
   * Clear search input
   */
  const handleClear = () => {
    setSearchQuery("");
  };

  /**
   * Handle key press events
   */
  const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Escape') {
      handleClear();
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* Search Icon */}
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-elev-text-muted" />
      
      {/* Search Input */}
      <Input
        type="text"
        value={searchQuery}
        onChange={handleInputChange}
        onKeyDown={handleKeyPress}
        placeholder={placeholder}
        className="pl-10 pr-10"
      />
      
      {/* Clear Button */}
      {searchQuery && (
        <button
          onClick={handleClear}
          className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-elev-text-muted hover:text-elev-text-secondary transition-colors"
          aria-label="Clear search"
        >
          <X className="h-4 w-4" />
        </button>
      )}
    </div>
  );
};

export default ClientSearch;