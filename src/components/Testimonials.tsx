
// SECTION: Imports
// ==================================================================================
import { cn } from "@/lib/utils";
import { useEffect, useRef } from "react";
import styles from "./TestimonialCard.module.css";
import { getResponsiveTextSize, getResponsivePadding, getTouchTargetClass, getContainerClass, getGridResponsiveClass } from "@/utils/responsive-utilities";

// SECTION: Interface Definition
// ==================================================================================
interface TestimonialProps {
  quote: string;
  name: string;
  title: string;
  image?: string;
}

// SECTION: TestimonialCard Component
// ==================================================================================
const TestimonialCard = ({ quote, name, title, image }: TestimonialProps) => {
  // --- Component JSX ---
  return (
    <div className={cn(
      "bg-white rounded-lg shadow-lg flex flex-col h-full",
      getResponsivePadding("6", "8", "8")
    )}>
      <div className="mb-6 flex-grow">
        <svg className="text-elev-navy h-6 w-6 md:h-8 md:w-8 mb-2" fill="currentColor" viewBox="0 0 32 32" aria-hidden="true">
          <path d="M10 8v8h6c0 3.314-2.686 6-6 6v2c4.418 0 8-3.582 8-8v-8h-8zM22 8v8h6c0 3.314-2.686 6-6 6v2c4.418 0 8-3.582 8-8v-8h-8z" />
        </svg>
        <p className={cn(
          "text-elev-text-secondary italic mb-4 leading-relaxed",
          getResponsiveTextSize("sm", "base", "base")
        )}>{quote}</p>
      </div>

      <div className="mt-auto flex items-center">
        <div 
          className={image ? styles.testimonialImage : styles.testimonialImageInitials}
          style={image ? { '--testimonial-image': `url(${image})` } as React.CSSProperties : undefined}
          aria-hidden="true"
        >
          {!image && name.charAt(0)}
        </div>
        <div>
          <p className={cn(
            "font-bold text-elev-navy",
            getResponsiveTextSize("sm", "base", "base")
          )}>{name}</p>
          <p className={cn(
            "text-elev-text-secondary",
            getResponsiveTextSize("xs", "sm", "sm")
          )}>{title}</p>
        </div>
      </div>
    </div>
  );
};

// SECTION: Testimonials Component (Main)
// ==================================================================================
const Testimonials = () => {
  // --- Hooks ---
  const sectionRef = useRef<HTMLDivElement>(null);
  const testimonialsRef = useRef<HTMLDivElement>(null);

  // Effect for reveal animations
  useEffect(() => {
    const currentSectionRef = sectionRef.current;
    const currentTestimonialsRef = testimonialsRef.current;
    
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("active");
          }
        });
      },
      { threshold: 0.1 }
    );

    if (currentSectionRef) observer.observe(currentSectionRef);
    if (currentTestimonialsRef) observer.observe(currentTestimonialsRef);

    return () => {
      if (currentSectionRef) observer.unobserve(currentSectionRef);
      if (currentTestimonialsRef) observer.unobserve(currentTestimonialsRef);
    };
  }, []);

  // --- Data ---
  const testimonials = [
    {
      quote: "These guys are the real deal! My son has been training here for 6 months and his skating has improved tremendously. The coaches are knowledgeable and patient, and the facility is top-notch with year-round ice. As a hockey parent in Vegas, having access to this level of training locally is a game-changer.",
      name: "Mike R.",
      title: "Las Vegas, NV",
    },
    {
      quote: "I've been bringing my daughter here for the past year and I can't say enough good things about Elev802. The coaches truly care about each player's development and create personalized training plans. The small group sessions ensure everyone gets attention. Worth every penny for serious hockey players in Vegas!",
      name: "Sarah K.",
      title: "Henderson, NV",
    },
    {
      quote: "As a coach myself, I'm extremely impressed with the training methodology. They focus on proper technique and hockey IQ, not just drills. My team has shown remarkable improvement since we started training here. The custom ice surface is perfect for skills work and the staff is professional and knowledgeable.",
      name: "David M.",
      title: "North Las Vegas, NV",
    }
  ];

  // --- Component JSX ---
  return (
    <section className="section-padding bg-gradient-to-b from-gray-100 via-gray-50 to-white" id="testimonials">
      <div className={cn("mx-auto", getContainerClass(), getResponsivePadding("4", "6", "8"))}>
        <div className="text-center mb-8 md:mb-12 reveal" ref={sectionRef}>
          <h2 className={cn("font-bold mb-4 text-elev-navy", getResponsiveTextSize("2xl", "3xl", "4xl"), "px-4")}>
            Yelp Reviews
          </h2>
          <p className={cn("text-elev-text-secondary max-w-3xl mx-auto", getResponsiveTextSize("base", "lg", "lg"), "px-4")}>
            Read what our customers are saying about us on Yelp. We're proud of our 5-star rating!
          </p>
        </div>

        <div
          className={cn("reveal grid", getGridResponsiveClass("1-2-3"), "gap-6 md:gap-8")}
          ref={testimonialsRef}
        >
          {testimonials.map((testimonial, index) => (
            <TestimonialCard
              key={index}
              quote={testimonial.quote}
              name={testimonial.name}
              title={testimonial.title}
            />
          ))}
        </div>
        
        {/* Yelp Review Button */}
        <div className="text-center mt-8 md:mt-12 reveal">
  <a
    href="https://www.yelp.com/biz/elev802-vegas-las-vegas?osq=elev802+vegas"
    target="_blank"
    rel="noopener noreferrer"
    aria-label="Read or leave a Yelp review for ELEV802 Vegas"
    className="inline-flex items-center justify-center px-6 py-3 bg-elev-navy text-white font-semibold rounded-lg hover:bg-gray-100 hover:text-elev-navy border-2 border-elev-navy transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-elev-navy"
  >
    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
      <path d="M20.16 12.594c.034.213.034.426.034.64 0 6.586-5.4 11.76-12 11.76S-4 20.1-4 13.233c0-6.586 5.4-11.76 12-11.76 2.4 0 4.7.746 6.6 2.04l-2.7 2.7c-1.3-.96-2.9-1.493-4.5-1.493-4.2 0-7.5 3.413-7.5 7.413 0 4.16 3.3 7.413 7.5 7.413 4.2 0 7.5-3.253 7.5-7.413 0-.213 0-.426-.04-.64h-8.1v-3.52h11.4z"/>
    </svg>
    Read or Leave a Yelp Review
  </a>
</div>
      </div>
    </section>
  );
};

export default Testimonials;
