/**
 * Helper function to format date for schema
 */
export const formatSchemaDate = (date: Date): string => {
  return date.toISOString();
};

/**
 * Common interface for schema components
 */
export interface SchemaBaseProps {
  url?: string;
  image?: string;
}

/**
 * Interface for LocalBusinessSchema props
 */
export interface LocalBusinessSchemaProps {
  name: string;
  description: string;
  telephone?: string;
  address?: {
    streetAddress: string;
    addressLocality: string;
    addressRegion: string;
    postalCode: string;
    addressCountry: string;
  };
  geo?: {
    latitude: number;
    longitude: number;
  };
  image?: string;
  url?: string;
  openingHours?: string[];
  priceRange?: string;
}

/**
 * Interface for EventSchema props
 */
export interface EventSchemaProps {
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  location?: {
    name: string;
    address: {
      streetAddress: string;
      addressLocality: string;
      addressRegion: string;
      postalCode: string;
      addressCountry: string;
    };
  };
  image?: string;
  url?: string;
  performer?: {
    name: string;
    [key: string]: string | number | boolean | object;
  };
  offers?: {
    price: number;
    priceCurrency: string;
    availability: string;
    url: string;
    [key: string]: string | number | boolean | object;
  };
  eventStatus?: string;
  eventAttendanceMode?: string;
}
