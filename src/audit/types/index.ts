// Core audit system types and interfaces

export enum Severity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum IssueType {
  COLOR_INCONSISTENCY = 'color_inconsistency',
  SPACING_INCONSISTENCY = 'spacing_inconsistency',
  TYPOGRAPHY_INCONSISTENCY = 'typography_inconsistency',
  COMPONENT_INCONSISTENCY = 'component_inconsistency',
  CONTENT_INCONSISTENCY = 'content_inconsistency'
}

export enum Category {
  VISUAL = 'visual',
  CONTENT = 'content',
  COMPONENT = 'component',
  ACCESSIBILITY = 'accessibility'
}

export interface FileLocation {
  filePath: string;
  lineNumber: number;
  columnNumber: number;
  context: string;
}

export interface ImpactAssessment {
  userExperience: number; // 1-10 scale
  maintenanceEffort: number; // 1-10 scale
  implementationComplexity: number; // 1-10 scale
}

export interface Issue {
  id: string;
  type: IssueType;
  severity: Severity;
  category: Category;
  description: string;
  location: FileLocation;
  suggestion: string;
  autoFixable: boolean;
  impact: ImpactAssessment;
}

export interface FileInfo {
  path: string;
  content: string;
  extension: string;
  size: number;
  lastModified: Date;
}

export interface AnalyzerResult {
  analyzerName: string;
  issues: Issue[];
  summary: {
    totalIssues: number;
    severityBreakdown: Record<Severity, number>;
    categoryBreakdown: Record<Category, number>;
  };
  executionTime: number;
}

export interface AuditSummary {
  totalFiles: number;
  totalIssues: number;
  severityBreakdown: Record<Severity, number>;
  categoryBreakdown: Record<Category, number>;
  executionTime: number;
  timestamp: Date;
}

export interface Recommendation {
  id: string;
  title: string;
  description: string;
  priority: number;
  estimatedEffort: number;
  relatedIssues: string[];
}

export interface RemediationTask {
  id: string;
  description: string;
  type: string;
  effort: number;
  dependencies: string[];
  autoFixable: boolean;
}

export interface RemediationPlan {
  tasks: RemediationTask[];
  dependencies: Array<{ taskId: string; dependsOn: string[] }>;
  estimatedEffort: number;
  priority: number;
}

export interface AuditResult {
  summary: AuditSummary;
  issues: Issue[];
  recommendations: Recommendation[];
  remediationPlan: RemediationPlan;
}

export interface Analyzer {
  name: string;
  analyze(files: FileInfo[]): Promise<AnalyzerResult>;
  getSeverity(issue: Omit<Issue, 'severity'>): Severity;
}

export interface AuditEngine {
  scanProject(projectPath: string): Promise<AuditResult>;
  registerAnalyzer(analyzer: Analyzer): void;
  generateReport(results: AnalyzerResult[]): AuditReport;
}

export interface AuditReport {
  summary: AuditSummary;
  results: AnalyzerResult[];
  recommendations: Recommendation[];
  remediationPlan: RemediationPlan;
  generatedAt: Date;
}

export interface AuditConfig {
  projectPath: string;
  includePatterns: string[];
  excludePatterns: string[];
  analyzers: string[];
  outputFormat: 'json' | 'html' | 'markdown';
  logLevel: 'debug' | 'info' | 'warn' | 'error';
}