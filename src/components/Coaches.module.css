.coachImageContainer {
  height: 16rem;
  background-color: #e5e7eb; /* bg-gray-300, fallback if image doesn't load */
  background-image: var(--coach-image, none);
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  overflow: hidden;
  position: relative; /* Needed for the inner div to be positioned correctly if it had other content */
}

/* This inner div is now primarily for the transform effect */
.coachImage {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
  /* Ensure it doesn't accidentally get a background if the variable was misapplied */
  background-image: none;
}

.coachImageContainer:hover .coachImage {
  transform: scale(1.1);
}
