[build]
  publish = "dist"
  command = "npm install --force && npm run build"

[build.environment]
  NODE_VERSION = "20.19.4"
  NODE_OPTIONS = "--max-old-space-size=4096"
  COREPACK_ENABLE_STRICT = "0"
  COREPACK_ENABLE_DOWNLOAD_PROMPT = "0"
  NPM_CONFIG_PACKAGE_MANAGER_STRICT = "false"
  COREPACK_ENABLE_AUTO_PIN = "0"
  COREPACK_ENABLE_PROJECT_SPEC = "0"
  NPM_CONFIG_FUND = "false"
  NPM_CONFIG_AUDIT = "false"

[dev]
  command = "npm run dev"
  port = 8080
  targetPort = 8080
  publish = "dist"
  autoLaunch = false

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
