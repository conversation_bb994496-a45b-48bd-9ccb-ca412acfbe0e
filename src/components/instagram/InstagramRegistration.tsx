import React from 'react';
import { createRoot, Root } from 'react-dom/client';
import { InstagramPost } from './InstagramPost';

/**
 * Register custom element only once, with proper error handling and cleanup
 * This function is extracted to a separate file to avoid ESLint warnings
 */
export const registerInstagramElement = (): void => {
  // Check if we're in a browser environment
  if (typeof window === 'undefined' || typeof customElements === 'undefined') {
    return;
  }
  
  // Only register if not already registered
  if (customElements.get('instagram-post')) {
    console.info('Instagram post element already registered');
    return;
  }
  
  try {
    customElements.define('instagram-post', 
      class extends HTMLElement {
        private root: Root | null = null;
        
        connectedCallback() {
          const postUrl = this.getAttribute('post-url');
          const captions = this.getAttribute('captions') === 'true';
          
          this.root = createRoot(this);
          this.root.render(
            <InstagramPost 
              postUrl={postUrl || undefined} 
              captions={captions} 
            />
          );
          
          // Load Instagram embed script if needed
          this.loadInstagramScript();
        }
        
        disconnectedCallback() {
          // Clean up React root when element is removed
          if (this.root) {
            this.root.unmount();
            this.root = null;
          }
        }
        
        loadInstagramScript() {
          // Only load if not already loaded
          if (!document.querySelector('script[src*="instagram.com/embed.js"]')) {
            const script = document.createElement('script');
            script.async = true;
            script.defer = true;
            script.src = 'https://www.instagram.com/embed.js';
            document.body.appendChild(script);
          } else if (window.instgrm?.Embeds?.process) {
            // If script already loaded and initialized, process this embed
            setTimeout(() => {
              try {
                // Using optional chaining to safely access potentially undefined properties
                window.instgrm?.Embeds?.process(this);
              } catch (e) {
                console.error('Error processing Instagram embed:', e);
              }
            }, 100);
          }
        }
      }
    );
    console.info('Instagram post element registered successfully');
  } catch (error) {
    console.warn('Failed to register Instagram custom element:', error);
  }
};

// Declare global instgrm to prevent TypeScript errors
declare global {
  interface Window {
    instgrm?: {
      Embeds: {
        process: (node?: HTMLElement | null) => void;
      };
    };
  }
}
