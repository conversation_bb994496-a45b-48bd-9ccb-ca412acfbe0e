/**
 * Validation System Entry Point
 * Exports all validation components and provides a unified interface
 */

export * from './types';
export * from './design-token-validator';
export * from './component-consistency-validator';
export * from './visual-regression-validator';
export * from './remediation-impact-tester';

import { DesignTokenValidator } from './design-token-validator';
import { ComponentConsistencyValidator } from './component-consistency-validator';
import { VisualRegressionValidator } from './visual-regression-validator';
import { RemediationImpactTester } from './remediation-impact-tester';
import { 
  ValidationResult, 
  ValidationFile, 
  ComplianceReport, 
  ComplianceRecommendation,
  ValidationCategory,
  Validator
} from './types';
import { DesignTokenSystem } from '../design-tokens/types';
import { Severity } from '../types';

export class ValidationEngine {
  private validators: Map<ValidationCategory, Validator> = new Map();
  private impactTester: RemediationImpactTester;

  constructor() {
    this.validators.set(ValidationCategory.DESIGN_TOKENS, new DesignTokenValidator());
    this.validators.set(ValidationCategory.COMPONENT_CONSISTENCY, new ComponentConsistencyValidator());
    this.validators.set(ValidationCategory.VISUAL_REGRESSION, new VisualRegressionValidator());
    this.impactTester = new RemediationImpactTester();
  }

  /**
   * Run comprehensive validation across all categories
   */
  async validateCompliance(
    files: ValidationFile[], 
    tokens?: DesignTokenSystem
  ): Promise<ComplianceReport> {
    const startTime = Date.now();
    const results: Record<ValidationCategory, ValidationResult> = {} as any;

    // Run all validators in parallel
    const validationPromises = Array.from(this.validators.entries()).map(
      async ([category, validator]) => {
        const result = await validator.validate(files, tokens);
        return { category, result };
      }
    );

    const validationResults = await Promise.all(validationPromises);
    
    // Organize results by category
    validationResults.forEach(({ category, result }) => {
      results[category] = result;
    });

    // Generate overall result
    const overall = this.generateOverallResult(results);
    
    // Generate recommendations
    const recommendations = this.generateRecommendations(results);

    return {
      overall,
      byCategory: results,
      byFile: this.generateFileBreakdown(results),
      recommendations,
      generatedAt: new Date()
    };
  }

  /**
   * Test the impact of remediation changes
   */
  async testRemediationImpact(
    beforeFiles: ValidationFile[],
    afterFiles: ValidationFile[],
    tokens?: DesignTokenSystem
  ) {
    return this.impactTester.testRemediationImpact(beforeFiles, afterFiles, tokens);
  }

  /**
   * Run validation for a specific category
   */
  async validateCategory(
    category: ValidationCategory,
    files: ValidationFile[],
    tokens?: DesignTokenSystem
  ): Promise<ValidationResult> {
    const validator = this.validators.get(category);
    if (!validator) {
      throw new Error(`No validator found for category: ${category}`);
    }

    return validator.validate(files, tokens);
  }

  /**
   * Get available validation categories
   */
  getAvailableCategories(): ValidationCategory[] {
    return Array.from(this.validators.keys());
  }

  /**
   * Register a custom validator
   */
  registerValidator(category: ValidationCategory, validator: Validator): void {
    this.validators.set(category, validator);
  }

  private generateOverallResult(results: Record<ValidationCategory, ValidationResult>): ValidationResult {
    const allViolations = Object.values(results).flatMap(result => result.violations);
    const totalExecutionTime = Object.values(results).reduce((sum, result) => sum + result.executionTime, 0);

    return {
      isValid: allViolations.length === 0,
      violations: allViolations,
      summary: {
        totalViolations: allViolations.length,
        severityBreakdown: allViolations.reduce((acc, v) => {
          acc[v.severity] = (acc[v.severity] || 0) + 1;
          return acc;
        }, {} as Record<Severity, number>),
        ruleBreakdown: allViolations.reduce((acc, v) => {
          acc[v.rule] = (acc[v.rule] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
        fileBreakdown: allViolations.reduce((acc, v) => {
          acc[v.location.filePath] = (acc[v.location.filePath] || 0) + 1;
          return acc;
        }, {} as Record<string, number>)
      },
      executionTime: totalExecutionTime
    };
  }

  private generateFileBreakdown(results: Record<ValidationCategory, ValidationResult>): Record<string, ValidationResult> {
    const fileBreakdown: Record<string, ValidationResult> = {};
    const allViolations = Object.values(results).flatMap(result => result.violations);

    // Group violations by file
    const violationsByFile = allViolations.reduce((acc, violation) => {
      const filePath = violation.location.filePath;
      if (!acc[filePath]) {
        acc[filePath] = [];
      }
      acc[filePath].push(violation);
      return acc;
    }, {} as Record<string, typeof allViolations>);

    // Create validation result for each file
    Object.entries(violationsByFile).forEach(([filePath, violations]) => {
      fileBreakdown[filePath] = {
        isValid: violations.length === 0,
        violations,
        summary: {
          totalViolations: violations.length,
          severityBreakdown: violations.reduce((acc, v) => {
            acc[v.severity] = (acc[v.severity] || 0) + 1;
            return acc;
          }, {} as Record<Severity, number>),
          ruleBreakdown: violations.reduce((acc, v) => {
            acc[v.rule] = (acc[v.rule] || 0) + 1;
            return acc;
          }, {} as Record<string, number>),
          fileBreakdown: { [filePath]: violations.length }
        },
        executionTime: 0
      };
    });

    return fileBreakdown;
  }

  private generateRecommendations(results: Record<ValidationCategory, ValidationResult>): ComplianceRecommendation[] {
    const recommendations: ComplianceRecommendation[] = [];
    let recommendationId = 1;

    Object.entries(results).forEach(([category, result]) => {
      if (result.violations.length === 0) return;

      // Group violations by rule
      const violationsByRule = result.violations.reduce((acc, violation) => {
        if (!acc[violation.rule]) {
          acc[violation.rule] = [];
        }
        acc[violation.rule].push(violation);
        return acc;
      }, {} as Record<string, typeof result.violations>);

      // Create recommendations for each rule
      Object.entries(violationsByRule).forEach(([rule, violations]) => {
        const highestSeverity = violations.reduce((highest, v) => 
          this.getSeverityWeight(v.severity) > this.getSeverityWeight(highest) ? v.severity : highest
        , violations[0].severity);

        const autoFixable = violations.every(v => v.autoFixable);

        recommendations.push({
          id: `rec-${recommendationId++}`,
          title: this.generateRecommendationTitle(rule, violations.length),
          description: this.generateRecommendationDescription(rule, violations),
          category: category as ValidationCategory,
          priority: this.calculatePriority(highestSeverity, violations.length),
          effort: this.estimateEffort(violations.length, autoFixable),
          violations: violations.map(v => v.id),
          autoFixable
        });
      });
    });

    // Sort recommendations by priority (highest first)
    return recommendations.sort((a, b) => b.priority - a.priority);
  }

  private getSeverityWeight(severity: Severity): number {
    const weights = {
      [Severity.LOW]: 1,
      [Severity.MEDIUM]: 2,
      [Severity.HIGH]: 3,
      [Severity.CRITICAL]: 4
    };
    return weights[severity] || 0;
  }

  private generateRecommendationTitle(rule: string, count: number): string {
    const ruleNames: Record<string, string> = {
      'hardcoded-color': 'Replace Hardcoded Colors with Design Tokens',
      'hardcoded-spacing': 'Replace Hardcoded Spacing with Design Tokens',
      'hardcoded-typography': 'Replace Hardcoded Typography with Design Tokens',
      'invalid-token-usage': 'Fix Invalid Design Token Usage',
      'component-naming': 'Fix Component Naming Conventions',
      'prop-consistency': 'Improve Component Prop Consistency',
      'state-management': 'Standardize State Management Patterns',
      'style-consistency': 'Unify Styling Approaches',
      'accessibility-pattern': 'Fix Accessibility Issues',
      'visual-regression': 'Address Visual Regression Issues'
    };

    const baseName = ruleNames[rule] || `Fix ${rule} Issues`;
    return count > 1 ? `${baseName} (${count} instances)` : baseName;
  }

  private generateRecommendationDescription(rule: string, violations: any[]): string {
    const descriptions: Record<string, string> = {
      'hardcoded-color': 'Replace hardcoded color values with design system tokens to ensure consistency and maintainability.',
      'hardcoded-spacing': 'Replace hardcoded spacing values with design system tokens for consistent spacing patterns.',
      'hardcoded-typography': 'Replace hardcoded typography values with design system tokens for consistent text styling.',
      'invalid-token-usage': 'Fix references to non-existent or deprecated design tokens.',
      'component-naming': 'Ensure components follow consistent naming conventions for better maintainability.',
      'prop-consistency': 'Standardize component prop interfaces for better developer experience.',
      'state-management': 'Use consistent state management patterns across components.',
      'style-consistency': 'Use a single styling approach throughout the application.',
      'accessibility-pattern': 'Fix accessibility issues to ensure the application is usable by all users.',
      'visual-regression': 'Address visual inconsistencies that may affect user experience.'
    };

    return descriptions[rule] || `Address ${violations.length} ${rule} violations to improve code quality.`;
  }

  private calculatePriority(severity: Severity, count: number): number {
    const severityWeight = this.getSeverityWeight(severity);
    const countWeight = Math.min(count / 10, 1); // Cap count influence at 10 violations
    return Math.round((severityWeight * 25) + (countWeight * 25));
  }

  private estimateEffort(count: number, autoFixable: boolean): number {
    const baseEffort = autoFixable ? 1 : 3; // Auto-fixable issues take less effort
    const countMultiplier = Math.ceil(count / 5); // Group violations for effort estimation
    return Math.min(baseEffort * countMultiplier, 10); // Cap at 10 effort points
  }
}

// Convenience function to create and use the validation engine
export async function validateDesignSystemCompliance(
  files: ValidationFile[],
  tokens?: DesignTokenSystem
): Promise<ComplianceReport> {
  const engine = new ValidationEngine();
  return engine.validateCompliance(files, tokens);
}

// Convenience function for remediation impact testing
export async function testRemediationImpact(
  beforeFiles: ValidationFile[],
  afterFiles: ValidationFile[],
  tokens?: DesignTokenSystem
) {
  const engine = new ValidationEngine();
  return engine.testRemediationImpact(beforeFiles, afterFiles, tokens);
}