// SECTION: Imports
// ==================================================================================
import React, { useState } from "react";

// SECTION: Type Definitions
// ==================================================================================
/**
 * Props for EmbeddedSchedulingPage component
 * Currently empty as component doesn't require any props
 */
type EmbeddedSchedulingPageProps = Record<string, never>;

// SECTION: EmbeddedSchedulingPage Component
// ==================================================================================
/**
 * EmbeddedSchedulingPage - Component for embedding the 702HOCKEY scheduling page.
 *
 * This component renders an iframe containing the 702HOCKEY booking portal.
 * It manages loading states and provides a responsive container for the external interface.
 */
const EmbeddedSchedulingPage: React.FC<EmbeddedSchedulingPageProps> = () => {
  // SECTION: State Management
  // ==================================================================================
  /**
   * Loading state management for the iframe.
   */
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // SECTION: Event Handlers
  // ==================================================================================
  /**
   * Handles iframe load completion.
   */
  const handleIframeLoad = (): void => {
    setIsLoading(false);
  };

  // SECTION: Component Render
  // ==================================================================================
  return (
    <div className="w-full min-h-screen bg-elev-background-muted">
      {/* HEADER SECTION */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-6">
          <h1 className="text-3xl font-bold text-elev-text-primary mb-2">
            Book Your Session
          </h1>
          <p className="text-elev-text-secondary">
            Schedule your sessions, view availability, and manage your bookings below.
          </p>
        </div>
      </div>

      {/* LOADING INDICATOR SECTION */}
      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-elev-text-secondary">Loading booking portal...</span>
        </div>
      )}

      {/* IFRAME CONTAINER SECTION */}
      <div className="relative" style={{ minHeight: 'calc(100vh - 140px)' }}>
        <iframe
          src="https://wix.702hockey.com/"
          frameBorder="0"
          className={`w-full transition-opacity duration-300 ${
            isLoading ? 'opacity-0' : 'opacity-100'
          }`}
          style={{ height: 'calc(100vh - 140px)', minHeight: '600px' }}
          onLoad={handleIframeLoad}
          title="702HOCKEY Scheduling"
          allow="fullscreen"
          /* 
           * Restrict sandbox permissions to prevent popups but allow essential functionality:
           * - Removed allow-popups to prevent pop-up windows
           * - Keep allow-scripts for JavaScript execution
           * - Keep allow-forms for form submission
           * - Keep allow-same-origin for proper styling and functionality
           */
          sandbox="allow-scripts allow-forms allow-same-origin"
          referrerPolicy="no-referrer-when-downgrade"
        ></iframe>
      </div>
    </div>
  );
};

// SECTION: Component Export
// ==================================================================================
export default EmbeddedSchedulingPage;
