/**
 * Interactive Examples Generator
 * Creates interactive component examples with live code editing and preview capabilities
 */

import { promises as fs } from 'fs';
import * as path from 'path';
import {
  InteractiveExample,
  ExampleVariant,
  ExampleControl,
  ControlType,
  LiveCodeExample,
  PreviewConfig,
  ComponentSpec
} from './types';

export class InteractiveExampleGenerator {
  private outputDir: string;

  constructor(outputDir: string = './docs/examples') {
    this.outputDir = outputDir;
  }

  /**
   * Generate interactive examples for components
   */
  async generateInteractiveExamples(
    components: ComponentSpec[],
    options: InteractiveExampleOptions = {}
  ): Promise<InteractiveExample[]> {
    const examples: InteractiveExample[] = [];

    for (const component of components) {
      const componentExamples = await this.createComponentExamples(component);
      examples.push(...componentExamples);
    }

    // Generate example files
    await this.generateExampleFiles(examples);
    await this.generatePlaygroundIndex(examples);
    await this.generateStorybook(examples);

    return examples;
  }

  /**
   * Create interactive examples for a component
   */
  private async createComponentExamples(component: ComponentSpec): Promise<InteractiveExample[]> {
    const examples: InteractiveExample[] = [];

    // Basic example
    const basicExample = this.createBasicExample(component);
    examples.push(basicExample);

    // Variant examples
    for (const variant of component.variants) {
      const variantExample = this.createVariantExample(component, variant);
      examples.push(variantExample);
    }

    // State examples
    for (const state of component.states) {
      const stateExample = this.createStateExample(component, state);
      examples.push(stateExample);
    }

    // Playground example (all props configurable)
    const playgroundExample = this.createPlaygroundExample(component);
    examples.push(playgroundExample);

    return examples;
  }

  /**
   * Create basic component example
   */
  private createBasicExample(component: ComponentSpec): InteractiveExample {
    const requiredProps = component.props.filter(prop => prop.required);
    const defaultProps = this.getDefaultProps(component.props);

    return {
      id: `${component.name.toLowerCase()}-basic`,
      title: `${component.name} - Basic`,
      description: `Basic usage of ${component.name} component`,
      component: component.name,
      variants: [{
        name: 'default',
        props: defaultProps,
        description: 'Default configuration'
      }],
      controls: this.createBasicControls(requiredProps),
      code: {
        template: this.generateBasicTemplate(component.name, defaultProps),
        imports: [`import { ${component.name} } from '@/components/ui/${component.name.toLowerCase()}';`],
        dependencies: [],
        editable: true
      },
      preview: {
        background: 'white',
        padding: '2rem',
        responsive: true,
        darkMode: true,
        rtl: false
      }
    };
  }

  /**
   * Create variant-specific example
   */
  private createVariantExample(component: ComponentSpec, variant: any): InteractiveExample {
    return {
      id: `${component.name.toLowerCase()}-${variant.name}`,
      title: `${component.name} - ${variant.name}`,
      description: variant.description,
      component: component.name,
      variants: [{
        name: variant.name,
        props: variant.props,
        description: variant.description
      }],
      controls: this.createVariantControls(component.props, variant.props),
      code: {
        template: variant.code || this.generateVariantTemplate(component.name, variant.props),
        imports: [`import { ${component.name} } from '@/components/ui/${component.name.toLowerCase()}';`],
        dependencies: variant.designTokens || [],
        editable: true
      },
      preview: {
        background: 'white',
        padding: '2rem',
        responsive: true,
        darkMode: true,
        rtl: false
      }
    };
  }

  /**
   * Create state-specific example
   */
  private createStateExample(component: ComponentSpec, state: any): InteractiveExample {
    const stateProps = this.getStateProps(state);

    return {
      id: `${component.name.toLowerCase()}-${state.name}`,
      title: `${component.name} - ${state.name} State`,
      description: `${component.name} in ${state.name} state`,
      component: component.name,
      variants: [{
        name: state.name,
        props: stateProps,
        description: state.description
      }],
      controls: this.createStateControls(stateProps),
      code: {
        template: this.generateStateTemplate(component.name, stateProps, state),
        imports: [`import { ${component.name} } from '@/components/ui/${component.name.toLowerCase()}';`],
        dependencies: [],
        editable: false
      },
      preview: {
        background: 'white',
        padding: '2rem',
        responsive: true,
        darkMode: true,
        rtl: false
      }
    };
  }

  /**
   * Create playground example with all configurable props
   */
  private createPlaygroundExample(component: ComponentSpec): InteractiveExample {
    const allControls = this.createAllControls(component.props);
    const defaultProps = this.getDefaultProps(component.props);

    return {
      id: `${component.name.toLowerCase()}-playground`,
      title: `${component.name} - Playground`,
      description: `Interactive playground for ${component.name} with all configurable props`,
      component: component.name,
      variants: [{
        name: 'playground',
        props: defaultProps,
        description: 'All props configurable'
      }],
      controls: allControls,
      code: {
        template: this.generatePlaygroundTemplate(component.name),
        imports: [`import { ${component.name} } from '@/components/ui/${component.name.toLowerCase()}';`],
        dependencies: [],
        editable: true
      },
      preview: {
        background: 'white',
        padding: '2rem',
        responsive: true,
        darkMode: true,
        rtl: true
      }
    };
  }

  /**
   * Create controls for basic example
   */
  private createBasicControls(requiredProps: any[]): ExampleControl[] {
    return requiredProps.map(prop => ({
      name: prop.name,
      type: this.getControlType(prop.type),
      label: prop.name.charAt(0).toUpperCase() + prop.name.slice(1),
      defaultValue: prop.defaultValue || this.getDefaultValueForType(prop.type),
      ...(prop.validation?.options && { options: prop.validation.options.map((opt: string) => ({ label: opt, value: opt })) })
    }));
  }

  /**
   * Create controls for variant example
   */
  private createVariantControls(allProps: any[], variantProps: Record<string, any>): ExampleControl[] {
    const relevantProps = allProps.filter(prop => 
      variantProps.hasOwnProperty(prop.name) || prop.required
    );

    return relevantProps.map(prop => ({
      name: prop.name,
      type: this.getControlType(prop.type),
      label: prop.name.charAt(0).toUpperCase() + prop.name.slice(1),
      defaultValue: variantProps[prop.name] || prop.defaultValue || this.getDefaultValueForType(prop.type),
      ...(prop.validation?.options && { options: prop.validation.options.map((opt: string) => ({ label: opt, value: opt })) })
    }));
  }

  /**
   * Create controls for state example
   */
  private createStateControls(stateProps: Record<string, any>): ExampleControl[] {
    return Object.entries(stateProps).map(([name, value]) => ({
      name,
      type: this.getControlType(typeof value),
      label: name.charAt(0).toUpperCase() + name.slice(1),
      defaultValue: value
    }));
  }

  /**
   * Create controls for all props
   */
  private createAllControls(props: any[]): ExampleControl[] {
    return props.map(prop => ({
      name: prop.name,
      type: this.getControlType(prop.type),
      label: prop.name.charAt(0).toUpperCase() + prop.name.slice(1),
      defaultValue: prop.defaultValue || this.getDefaultValueForType(prop.type),
      ...(prop.validation?.min && { min: prop.validation.min }),
      ...(prop.validation?.max && { max: prop.validation.max }),
      ...(prop.validation?.options && { options: prop.validation.options.map((opt: string) => ({ label: opt, value: opt })) })
    }));
  }

  /**
   * Get control type based on prop type
   */
  private getControlType(propType: string): ControlType {
    const type = propType.toLowerCase();
    
    if (type.includes('string')) return 'text';
    if (type.includes('number')) return 'number';
    if (type.includes('boolean')) return 'boolean';
    if (type.includes('color')) return 'color';
    if (type.includes('enum') || type.includes('union')) return 'select';
    
    return 'text';
  }

  /**
   * Get default value for type
   */
  private getDefaultValueForType(type: string): any {
    const lowerType = type.toLowerCase();
    
    if (lowerType.includes('string')) return '';
    if (lowerType.includes('number')) return 0;
    if (lowerType.includes('boolean')) return false;
    if (lowerType.includes('array')) return [];
    if (lowerType.includes('object')) return {};
    
    return undefined;
  }

  /**
   * Get default props from component spec
   */
  private getDefaultProps(props: any[]): Record<string, any> {
    const defaultProps: Record<string, any> = {};
    
    props.forEach(prop => {
      if (prop.defaultValue !== undefined) {
        defaultProps[prop.name] = prop.defaultValue;
      } else if (prop.required) {
        defaultProps[prop.name] = this.getDefaultValueForType(prop.type);
      }
    });
    
    return defaultProps;
  }

  /**
   * Get props for specific state
   */
  private getStateProps(state: any): Record<string, any> {
    // Extract state-specific props from state definition
    const stateProps: Record<string, any> = {};
    
    if (state.name === 'disabled') {
      stateProps.disabled = true;
    } else if (state.name === 'loading') {
      stateProps.loading = true;
    } else if (state.name === 'error') {
      stateProps.error = true;
    }
    
    return stateProps;
  }

  /**
   * Generate basic template
   */
  private generateBasicTemplate(componentName: string, props: Record<string, any>): string {
    const propsString = Object.entries(props)
      .map(([key, value]) => {
        if (typeof value === 'string') {
          return `${key}="${value}"`;
        } else if (typeof value === 'boolean') {
          return value ? key : '';
        } else {
          return `${key}={${JSON.stringify(value)}}`;
        }
      })
      .filter(Boolean)
      .join(' ');

    return `<${componentName} ${propsString}>
  Example content
</${componentName}>`;
  }

  /**
   * Generate variant template
   */
  private generateVariantTemplate(componentName: string, props: Record<string, any>): string {
    return this.generateBasicTemplate(componentName, props);
  }

  /**
   * Generate state template
   */
  private generateStateTemplate(componentName: string, props: Record<string, any>, state: any): string {
    const template = this.generateBasicTemplate(componentName, props);
    
    return `{/* ${state.description} */}
${template}

{/* 
Trigger: ${state.trigger}
Visual Changes: ${state.visualChanges.join(', ')}
*/}`;
  }

  /**
   * Generate playground template
   */
  private generatePlaygroundTemplate(componentName: string): string {
    return `<${componentName} {...props}>
  {children || 'Example content'}
</${componentName}>`;
  }

  /**
   * Generate example files
   */
  private async generateExampleFiles(examples: InteractiveExample[]): Promise<void> {
    await fs.mkdir(this.outputDir, { recursive: true });

    for (const example of examples) {
      // Generate React component file
      const componentCode = this.generateExampleComponent(example);
      const componentPath = path.join(this.outputDir, 'components', `${example.id}.tsx`);
      await fs.mkdir(path.dirname(componentPath), { recursive: true });
      await fs.writeFile(componentPath, componentCode, 'utf-8');

      // Generate example metadata
      const metadataPath = path.join(this.outputDir, 'metadata', `${example.id}.json`);
      await fs.mkdir(path.dirname(metadataPath), { recursive: true });
      await fs.writeFile(metadataPath, JSON.stringify(example, null, 2), 'utf-8');
    }
  }

  /**
   * Generate playground index
   */
  private async generatePlaygroundIndex(examples: InteractiveExample[]): Promise<void> {
    const indexContent = this.generatePlaygroundIndexContent(examples);
    await fs.writeFile(path.join(this.outputDir, 'index.tsx'), indexContent, 'utf-8');

    // Generate navigation
    const navigation = this.generatePlaygroundNavigation(examples);
    await fs.writeFile(path.join(this.outputDir, 'navigation.json'), JSON.stringify(navigation, null, 2), 'utf-8');
  }

  /**
   * Generate Storybook stories
   */
  private async generateStorybook(examples: InteractiveExample[]): Promise<void> {
    const storiesDir = path.join(this.outputDir, 'stories');
    await fs.mkdir(storiesDir, { recursive: true });

    const componentGroups = this.groupExamplesByComponent(examples);

    for (const [componentName, componentExamples] of componentGroups.entries()) {
      const storyContent = this.generateStorybookStory(componentName, componentExamples);
      const storyPath = path.join(storiesDir, `${componentName}.stories.tsx`);
      await fs.writeFile(storyPath, storyContent, 'utf-8');
    }
  }

  /**
   * Generate example React component
   */
  private generateExampleComponent(example: InteractiveExample): string {
    return `import React, { useState } from 'react';
${example.code.imports.join('\n')}

export interface ${example.component}ExampleProps {
  ${example.controls.map(control => 
    `${control.name}?: ${this.getTypeScriptType(control.type)};`
  ).join('\n  ')}
}

export function ${example.id.split('-').map(part => 
  part.charAt(0).toUpperCase() + part.slice(1)
).join('')}Example(props: ${example.component}ExampleProps) {
  ${example.controls.map(control => 
    `const [${control.name}, set${control.name.charAt(0).toUpperCase() + control.name.slice(1)}] = useState(props.${control.name} ?? ${JSON.stringify(control.defaultValue)});`
  ).join('\n  ')}

  return (
    <div className="example-container">
      <div className="example-preview" style={{
        background: '${example.preview.background}',
        padding: '${example.preview.padding}',
        ${example.preview.responsive ? 'resize: both,' : ''}
        overflow: 'auto'
      }}>
        ${example.code.template}
      </div>
      
      <div className="example-controls">
        <h3>Controls</h3>
        ${example.controls.map(control => this.generateControlJSX(control)).join('\n        ')}
      </div>
      
      <div className="example-code">
        <h3>Code</h3>
        <pre><code>{${JSON.stringify(example.code.template)}}</code></pre>
      </div>
    </div>
  );
}`;
  }

  /**
   * Generate control JSX
   */
  private generateControlJSX(control: ExampleControl): string {
    const controlName = control.name;
    const setterName = `set${controlName.charAt(0).toUpperCase() + controlName.slice(1)}`;

    switch (control.type) {
      case 'boolean':
        return `<label>
          <input 
            type="checkbox" 
            checked={${controlName}} 
            onChange={(e) => ${setterName}(e.target.checked)} 
          />
          ${control.label}
        </label>`;
      
      case 'number':
        return `<label>
          ${control.label}
          <input 
            type="number" 
            value={${controlName}} 
            onChange={(e) => ${setterName}(Number(e.target.value))}
            ${control.min !== undefined ? `min={${control.min}}` : ''}
            ${control.max !== undefined ? `max={${control.max}}` : ''}
            ${control.step !== undefined ? `step={${control.step}}` : ''}
          />
        </label>`;
      
      case 'select':
        return `<label>
          ${control.label}
          <select value={${controlName}} onChange={(e) => ${setterName}(e.target.value)}>
            ${control.options?.map(option => 
              `<option value="${option.value}">${option.label}</option>`
            ).join('\n            ') || ''}
          </select>
        </label>`;
      
      case 'color':
        return `<label>
          ${control.label}
          <input 
            type="color" 
            value={${controlName}} 
            onChange={(e) => ${setterName}(e.target.value)} 
          />
        </label>`;
      
      default:
        return `<label>
          ${control.label}
          <input 
            type="text" 
            value={${controlName}} 
            onChange={(e) => ${setterName}(e.target.value)} 
          />
        </label>`;
    }
  }

  /**
   * Get TypeScript type for control
   */
  private getTypeScriptType(controlType: ControlType): string {
    switch (controlType) {
      case 'boolean': return 'boolean';
      case 'number': return 'number';
      case 'range': return 'number';
      default: return 'string';
    }
  }

  /**
   * Generate playground index content
   */
  private generatePlaygroundIndexContent(examples: InteractiveExample[]): string {
    return `import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';

// Import all example components
${examples.map(example => {
  const componentName = example.id.split('-').map(part => 
    part.charAt(0).toUpperCase() + part.slice(1)
  ).join('') + 'Example';
  return `import { ${componentName} } from './components/${example.id}';`;
}).join('\n')}

export function ComponentPlayground() {
  return (
    <Router>
      <div className="playground">
        <nav className="playground-nav">
          <h1>Component Playground</h1>
          <ul>
            ${examples.map(example => 
              `<li><Link to="/${example.id}">${example.title}</Link></li>`
            ).join('\n            ')}
          </ul>
        </nav>
        
        <main className="playground-main">
          <Routes>
            ${examples.map(example => {
              const componentName = example.id.split('-').map(part => 
                part.charAt(0).toUpperCase() + part.slice(1)
              ).join('') + 'Example';
              return `<Route path="/${example.id}" element={<${componentName} />} />`;
            }).join('\n            ')}
          </Routes>
        </main>
      </div>
    </Router>
  );
}`;
  }

  /**
   * Generate playground navigation
   */
  private generatePlaygroundNavigation(examples: InteractiveExample[]): any {
    const componentGroups = this.groupExamplesByComponent(examples);
    
    return {
      sections: Array.from(componentGroups.entries()).map(([component, componentExamples]) => ({
        title: component,
        items: componentExamples.map(example => ({
          title: example.title,
          id: example.id,
          description: example.description
        }))
      }))
    };
  }

  /**
   * Group examples by component
   */
  private groupExamplesByComponent(examples: InteractiveExample[]): Map<string, InteractiveExample[]> {
    const groups = new Map<string, InteractiveExample[]>();
    
    examples.forEach(example => {
      if (!groups.has(example.component)) {
        groups.set(example.component, []);
      }
      groups.get(example.component)!.push(example);
    });
    
    return groups;
  }

  /**
   * Generate Storybook story
   */
  private generateStorybookStory(componentName: string, examples: InteractiveExample[]): string {
    return `import type { Meta, StoryObj } from '@storybook/react';
import { ${componentName} } from '@/components/ui/${componentName.toLowerCase()}';

const meta: Meta<typeof ${componentName}> = {
  title: 'Components/${componentName}',
  component: ${componentName},
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

${examples.map(example => {
  const storyName = example.title.replace(`${componentName} - `, '').replace(/\s+/g, '');
  return `
export const ${storyName}: Story = {
  name: '${example.title}',
  args: ${JSON.stringify(example.variants[0]?.props || {}, null, 2)},
  parameters: {
    docs: {
      description: {
        story: '${example.description}',
      },
    },
  },
};`;
}).join('')}`;
  }
}

// Supporting interfaces
interface InteractiveExampleOptions {
  includePlayground?: boolean;
  includeStorybook?: boolean;
  customControls?: Record<string, ExampleControl>;
}