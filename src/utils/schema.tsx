import React from 'react';

// Common interface for schema components
interface SchemaBaseProps {
  url?: string;
  image?: string;
}

// Helper function to format date for schema
export const formatSchemaDate = (date: Date): string => {
  return date.toISOString();
};

interface LocalBusinessSchemaProps {
  name: string;
  description: string;
  telephone?: string;
  address?: {
    streetAddress: string;
    addressLocality: string;
    addressRegion: string;
    postalCode: string;
    addressCountry: string;
  };
  geo?: {
    latitude: number;
    longitude: number;
  };
  image?: string;
  url?: string;
  openingHours?: string[];
  priceRange?: string;
}

/**
 * Generates JSON-LD schema markup for a local business
 * Helps search engines understand key information about the business
 */
export function LocalBusinessSchema({
  name,
  description,
  telephone,
  address,
  geo,
  image,
  url,
  openingHours,
  priceRange
}: LocalBusinessSchemaProps) {
  const schema = {
    '@context': 'https://schema.org',
    '@type': 'SportsActivityLocation',
    '@id': url,
    name,
    description,
    ...(telephone && { telephone }),
    ...(address && {
      address: {
        '@type': 'PostalAddress',
        ...address
      }
    }),
    ...(geo && {
      geo: {
        '@type': 'GeoCoordinates',
        latitude: geo.latitude,
        longitude: geo.longitude
      }
    }),
    ...(image && { image }),
    ...(url && { url }),
    ...(openingHours && { openingHours: openingHours }),
    ...(priceRange && { priceRange })
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  );
}

/**
 * Interface for EventSchema props
 */
interface EventSchemaProps extends SchemaBaseProps {
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  location?: {
    name: string;
    address: {
      streetAddress: string;
      addressLocality: string;
      addressRegion: string;
      postalCode: string;
      addressCountry: string;
    };
  };
  performer?: {
    name: string;
    [key: string]: string | number | boolean | object;
  };
  offers?: {
    price: number;
    priceCurrency: string;
    availability: string;
    url: string;
    [key: string]: string | number | boolean | object;
  };
  eventStatus?: string;
  eventAttendanceMode?: string;
}

/**
 * Generates JSON-LD schema markup for an event
 */
export function EventSchema({
  name,
  description,
  startDate,
  endDate,
  location,
  image,
  url,
  performer,
  offers,
  eventStatus = 'https://schema.org/EventScheduled',
  eventAttendanceMode = 'https://schema.org/OfflineEventAttendanceMode'
}: EventSchemaProps) {
  const schema = {
    '@context': 'https://schema.org',
    '@type': 'SportsEvent',
    name,
    description,
    startDate,
    endDate,
    eventStatus,
    eventAttendanceMode,
    ...(location && { 
      location: {
        '@type': 'Place',
        ...location
      } 
    }),
    ...(image && { image }),
    ...(url && { url }),
    ...(performer && { 
      performer: {
        '@type': 'Person',
        ...performer
      } 
    }),
    ...(offers && { 
      offers: {
        '@type': 'Offer',
        ...offers
      } 
    })
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  );
}
