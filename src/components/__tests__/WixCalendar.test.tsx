import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import WixCalendar from '../WixCalendar';

// Mock window properties for mobile detection
const mockWindow = (width: number, touchSupport: boolean = false) => {
  // Mock window.innerWidth
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width,
  });

  // Mock touch support
  if (touchSupport) {
    Object.defineProperty(window, 'ontouchstart', {
      writable: true,
      configurable: true,
      value: {},
    });
  } else {
    Object.defineProperty(window, 'ontouchstart', {
      writable: true,
      configurable: true,
      value: undefined,
    });
  }

  // Mock navigator.maxTouchPoints
  Object.defineProperty(navigator, 'maxTouchPoints', {
    writable: true,
    configurable: true,
    value: touchSupport ? 1 : 0,
  });
};

describe('WixCalendar Mobile Optimization', () => {
  beforeEach(() => {
    // Reset window properties before each test
    vi.clearAllMocks();
  });

  afterEach(() => {
    // Clean up after each test
    vi.restoreAllMocks();
  });

  it('renders with mobile-optimized layout on small screens', async () => {
    // Mock mobile viewport
    mockWindow(375, true);

    render(<WixCalendar />);

    // Check for mobile-optimized header text
    expect(screen.getByText('Schedule Your Training')).toBeInTheDocument();
    
    // Check for loading indicator
    expect(screen.getByText('Loading scheduling calendar...')).toBeInTheDocument();

    // Check for iframe with mobile-optimized title
    const iframe = screen.getByTitle('702 Hockey Scheduling Calendar - Mobile Optimized');
    expect(iframe).toBeInTheDocument();
    expect(iframe).toHaveAttribute('src', 'https://www.702hockey.com/weekly-calendar');
  });

  it('shows mobile tip when on mobile device', async () => {
    // Mock mobile viewport
    mockWindow(375, true);

    render(<WixCalendar />);

    // Wait for mobile detection to complete
    await waitFor(() => {
      expect(screen.getByText(/Tip: Rotate your device to landscape mode/)).toBeInTheDocument();
    });
  });

  it('handles iframe load event correctly', async () => {
    mockWindow(375, true);

    render(<WixCalendar />);

    const iframe = screen.getByTitle('702 Hockey Scheduling Calendar - Mobile Optimized');
    
    // Initially loading should be visible
    expect(screen.getByText('Loading scheduling calendar...')).toBeInTheDocument();

    // Simulate iframe load
    fireEvent.load(iframe);

    // Loading should disappear after iframe loads
    await waitFor(() => {
      expect(screen.queryByText('Loading scheduling calendar...')).not.toBeInTheDocument();
    });
  });

  it('applies correct responsive styles to iframe', () => {
    mockWindow(375, true);

    render(<WixCalendar />);

    const iframe = screen.getByTitle('702 Hockey Scheduling Calendar - Mobile Optimized');
    
    // Check that iframe has basic responsive styling
    expect(iframe).toHaveStyle('height: calc(100vh - 120px)');
    expect(iframe).toHaveStyle('min-height: 500px');
  });

  it('includes proper accessibility attributes', () => {
    mockWindow(375, true);

    render(<WixCalendar />);

    const iframe = screen.getByTitle('702 Hockey Scheduling Calendar - Mobile Optimized');
    
    // Check accessibility attributes
    expect(iframe).toHaveAttribute('title', '702 Hockey Scheduling Calendar - Mobile Optimized');
    expect(iframe).toHaveAttribute('allow', 'fullscreen; pointer-lock');
    expect(iframe).toHaveAttribute('sandbox', 'allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation allow-pointer-lock');
  });

  it('has responsive typography classes', () => {
    mockWindow(375, true);

    render(<WixCalendar />);

    const heading = screen.getByText('Schedule Your Training');
    
    // Check for responsive typography classes
    expect(heading).toHaveClass('text-xl', 'sm:text-2xl', 'lg:text-3xl');
  });
});