
import { useState } from "react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { Breadcrumbs } from "@/components/ui/Breadcrumbs";
import { useBreadcrumbs, useShouldShowBreadcrumbs } from "@/hooks/useBreadcrumbs";


import WixCalendar from "@/components/WixCalendar";

const ClientManagement = () => {
  const { toast } = useToast();
  
  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      {useShouldShowBreadcrumbs() && (
        <Breadcrumbs
          items={useBreadcrumbs()}
          showHomeIcon={true}
          separator="chevron"
        />
      )}
      
      <main className="flex-grow bg-elev-background-muted">
        {/* Mobile-optimized container with responsive padding */}
        <div className="container mx-auto py-4 sm:py-6 lg:py-8 px-2 sm:px-4 lg:px-6">
          {/* Responsive header section */}
          <div className="mb-4 sm:mb-6 lg:mb-8">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
              Client Management
            </h1>
            <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
              Manage your hockey training clients and sessions through our integrated Wix scheduling system.
            </p>
          </div>
          
          {/* Mobile-optimized calendar container */}
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <WixCalendar />
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default ClientManagement;
