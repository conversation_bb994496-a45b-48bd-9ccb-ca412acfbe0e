import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";

interface LoadingLinkProps {
  to: string;
  children: React.ReactNode;
  className?: string;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  onClick?: () => void;
  loadingTime?: number; // Optional minimum loading time in ms
  isButton?: boolean; // Whether to render as a button or a link
}

const LoadingLink = ({ 
  to, 
  children, 
  className = "", 
  variant = "default", 
  onClick, 
  loadingTime = 500, // Default minimum loading time
  isButton = false
}: LoadingLinkProps) => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);

  // Reset loading state when component unmounts or route changes
  useEffect(() => {
    return () => {
      setIsLoading(false);
    };
  }, []);

  const handleClick = () => {
    if (onClick) onClick();
    
    // Set loading state
    setIsLoading(true);
    
    // Navigate after a minimum delay to ensure loading state is visible
    setTimeout(() => {
      navigate(to);
      setIsLoading(false);
    }, loadingTime);
  };

  if (isButton) {
    return (
      <Button
        variant={variant}
        className={className}
        onClick={handleClick}
        disabled={isLoading}
      >
        {isLoading ? (
          <div className="flex items-center">
            <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
            <span>Loading...</span>
          </div>
        ) : (
          children
        )}
      </Button>
    );
  }

  return (
    <a
      href={to}
       className={`cursor-pointer ${className}`}
       onClick={handleClick}
      role="link"
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          handleClick();
        }
      }}
    >
      {isLoading ? (
        <div className="flex items-center">
          <div className="h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"></div>
          <span>Loading...</span>
        </div>
      ) : (
        children
      )}
    </a>
  );
};

export default LoadingLink;
