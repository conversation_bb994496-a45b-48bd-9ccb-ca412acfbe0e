import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import { useRef, useState } from "react";
import { useRevealOnScroll } from "@/hooks/use-reveal-on-scroll";
import { FaHockeyPuck, FaCheckCircle } from "react-icons/fa";
import { getResponsiveTextSize, getResponsivePadding, getTouchTargetClass } from "@/utils/responsive-utilities";

const ContactForm = () => {
  const { toast } = useToast();
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    athleteName: "",
    parentName: "",
    email: "",
    phone: "",
    age: "",
    interest: "",
    message: ""
  });

  const { ref: sectionRef, isVisible: isSectionVisible } = useRevealOnScroll<HTMLDivElement>({ threshold: 0.1 });
  const { ref: formRef, isVisible: isFormVisible } = useRevealOnScroll<HTMLDivElement>({ threshold: 0.1, delay: 200 });
  const { ref: imageRef, isVisible: isImageVisible } = useRevealOnScroll<HTMLDivElement>({ threshold: 0.1, delay: 400 });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isValidating, setIsValidating] = useState(false);
  const [formSubmitted, setFormSubmitted] = useState(false);

  const validateEmail = (email: string) => {
    return email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/);
  };

  const validatePhone = (phone: string) => {
    return phone.match(/^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/);
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.athleteName.trim()) {
      newErrors.athleteName = "Athlete name is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email address is required";
    } else if (!validateEmail(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (!formData.phone.trim()) {
      newErrors.phone = "Phone number is required";
    } else if (!validatePhone(formData.phone)) {
      newErrors.phone = "Please enter a valid phone number";
    }

    if (!formData.age.trim()) {
      newErrors.age = "Age or birth year is required";
    }

    if (!formData.interest) {
      newErrors.interest = "Please select a service";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleSelectChange = (value: string, name: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsValidating(true);
    
    if (!validateForm()) {
      setIsValidating(false);
      const errorMessages = Object.values(errors).join(". ");
      toast({
        title: "Form Validation Error",
        description: errorMessages,
        variant: "destructive",
      });
      
      const firstErrorField = Object.keys(errors)[0];
      document.getElementById(firstErrorField)?.focus();
      return;
    }

    setSubmitting(true);
    
    try {
      const form = e.currentTarget;
      const formData = new FormData(form);
      formData.append('form-name', 'contact');
      
      const searchParams = new URLSearchParams();
      for (const [key, value] of formData.entries()) {
        searchParams.append(key, value.toString());
      }
      
      const response = await fetch('/', {
        method: 'POST',
        body: searchParams.toString(),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      
      toast({
        title: "Inquiry Submitted Successfully",
        description: "Thank you for your interest! Our team will contact you within 24 hours.",
        variant: "default",
      });

      setFormSubmitted(true);
      setFormData({
        athleteName: "",
        parentName: "",
        email: "",
        phone: "",
        age: "",
        interest: "",
        message: ""
      });

      const successMessage = document.createElement('div');
      successMessage.setAttribute('role', 'status');
      successMessage.setAttribute('aria-live', 'polite');
      successMessage.textContent = "Form submitted successfully. We'll contact you within 24 hours.";
      document.body.appendChild(successMessage);
      setTimeout(() => document.body.removeChild(successMessage), 1000);

    } catch (error) {
      console.error('Form submission error:', error);
      toast({
        title: "Submission Error",
        description: "There was a problem submitting your form. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
      setIsValidating(false);
    }
  };

  return (
    <section 
      className="w-full bg-white text-black relative overflow-hidden py-12 md:py-16 lg:py-20" 
      id="contact-form" 
      aria-labelledby="contact-form-heading"
    >
      {/* Hidden form for Netlify's build process */}
      <form name="contact" netlify="true" netlify-honeypot="bot-field" hidden>
        <input type="text" name="athleteName" />
        <input type="text" name="parentName" />
        <input type="email" name="email" />
        <input type="tel" name="phone" />
        <input type="text" name="age" />
        <input type="text" name="interest" />
        <textarea name="message"></textarea>
      </form>
      
      <div className="w-full relative z-10">
        <div
          className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-16 w-full items-stretch"
        >
        <div
            ref={sectionRef}
            className={cn("text-center mb-8 md:mb-0 reveal w-full flex-grow flex-1", { active: isSectionVisible })}
          >
          <h1 id="contact-form-heading" className={cn("font-bold mb-4 md:mb-6 text-black", getResponsiveTextSize("3xl", "4xl", "5xl"))}>
            Contact Us
          </h1>
          <p className={cn("text-black w-full", getResponsiveTextSize("base", "lg", "xl"))}>
            Fill out the form below, and our team will contact you to discuss your goals and how we can help you achieve them.
          </p>
          <div className="flex flex-col space-y-6 w-full">
            <div className="flex flex-wrap justify-center gap-3 md:gap-4 mt-4 text-black" role="list" aria-label="Certifications and achievements">
              <span className={cn("flex items-center gap-1 md:gap-2", getResponsiveTextSize("sm", "base", "base"))} role="listitem">
                <FaCheckCircle className="text-black flex-shrink-0" aria-hidden="true" /> 
                <span>USA Hockey Certified</span>
              </span>
              <span className={cn("flex items-center gap-1 md:gap-2", getResponsiveTextSize("sm", "base", "base"))} role="listitem">
                <FaCheckCircle className="text-black flex-shrink-0" aria-hidden="true" /> 
                <span>Pro-Level Training</span>
              </span>
              <span className={cn("flex items-center gap-1 md:gap-2", getResponsiveTextSize("sm", "base", "base"))} role="listitem">
                <FaCheckCircle className="text-black flex-shrink-0" aria-hidden="true" /> 
                <span>500+ Trained Athletes</span>
              </span>
            </div>
              <FaCheckCircle className="text-black flex-shrink-0" aria-hidden="true" /> 
              <span>Pro-Level Training</span>
            </span>
            <span className={cn("flex items-center gap-1 md:gap-2", getResponsiveTextSize("sm", "base", "base"))} role="listitem">
              <FaCheckCircle className="text-black flex-shrink-0" aria-hidden="true" /> 
              <span>500+ Trained Athletes</span>
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8 lg:gap-16 items-start px-4">
          <div
            ref={formRef}
            className={cn(
              "reveal bg-white rounded-2xl shadow-2xl transform transition-all duration-500 hover:scale-[1.01] border border-gray-100 w-full",
              getResponsivePadding("4", "6", "8"),
              { active: isFormVisible }
            )}
          >
            <div className="flex items-center justify-between mb-4 md:mb-6 pb-3 md:pb-4 border-b border-gray-100">
              <h3 className={cn("font-bold text-gray-900", getResponsiveTextSize("lg", "xl", "xl"))}>
                Get In Touch
              </h3>
            </div>
            
            <form 
              name="contact" 
              method="POST" 
              data-netlify="true"
              netlify-honeypot="bot-field"
              onSubmit={handleSubmit}
              noValidate
              aria-label="Contact form"
            >
              <input type="hidden" name="form-name" value="contact" />
              
              {/* Honeypot field for spam protection */}
              <div className="hidden">
                <label>
                  Don't fill this out if you're human: <input name="bot-field" />
                </label>
              </div>

              <div className="space-y-4 w-full flex-grow flex flex-col">
                <div className="space-y-2">
                  <Label htmlFor="athleteName" className={cn("flex items-center font-medium", getResponsiveTextSize("sm", "base", "base"))}>
                    Athlete Name
                    <span className="text-red-500 ml-1" aria-hidden="true">*</span>
                    <span className="sr-only">required</span>
                  </Label>
                  <Input
                    id="athleteName"
                    name="athleteName"
                    placeholder="Athlete's full name"
                    required
                    aria-required="true"
                    aria-invalid={!!errors.athleteName}
                    aria-describedby={errors.athleteName ? "athleteName-error" : undefined}
                    value={formData.athleteName}
                    onChange={handleChange}
                    className={cn(
                      "focus:ring-2 focus:ring-elev-blue focus:border-elev-blue touch-manipulation",
                      getTouchTargetClass(),
                      errors.athleteName && "border-red-500"
                    )}
                  />
                  {errors.athleteName && (
                    <p id="athleteName-error" className={cn("text-red-500 mt-1", getResponsiveTextSize("xs", "sm", "sm"))} role="alert">
                      {errors.athleteName}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="parentName" className={cn("font-medium", getResponsiveTextSize("sm", "base", "base"))}>
                    Parent Name (if applicable)
                  </Label>
                  <Input
                    id="parentName"
                    name="parentName"
                    placeholder="Parent's full name"
                    aria-describedby="parentName-hint"
                    value={formData.parentName}
                    onChange={handleChange}
                    className={cn("focus:ring-2 focus:ring-elev-blue focus:border-elev-blue touch-manipulation", getTouchTargetClass())}
                  />
                  <p id="parentName-hint" className={cn("text-gray-500", getResponsiveTextSize("xs", "sm", "sm"))}>
                    Optional for athletes under 18
                  </p>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email" className={cn("flex items-center font-medium", getResponsiveTextSize("sm", "base", "base"))}>
                      Email
                      <span className="text-red-500 ml-1" aria-hidden="true">*</span>
                      <span className="sr-only">required</span>
                    </Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="<EMAIL>"
                      required
                      aria-required="true"
                      aria-invalid={!!errors.email}
                      aria-describedby={errors.email ? "email-error" : undefined}
                      value={formData.email}
                      onChange={handleChange}
                      className={cn(
                        "focus:ring-2 focus:ring-elev-blue focus:border-elev-blue touch-manipulation",
                        getTouchTargetClass(),
                        errors.email && "border-red-500"
                      )}
                    />
                    {errors.email && (
                      <p id="email-error" className={cn("text-red-500 mt-1", getResponsiveTextSize("xs", "sm", "sm"))} role="alert">
                        {errors.email}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone" className={cn("flex items-center font-medium", getResponsiveTextSize("sm", "base", "base"))}>
                      Phone
                      <span className="text-red-500 ml-1" aria-hidden="true">*</span>
                      <span className="sr-only">required</span>
                    </Label>
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      placeholder="(*************"
                      required
                      aria-required="true"
                      aria-invalid={!!errors.phone}
                      aria-describedby={errors.phone ? "phone-error" : undefined}
                      value={formData.phone}
                      onChange={handleChange}
                      className={cn(
                        "focus:ring-2 focus:ring-elev-blue focus:border-elev-blue touch-manipulation",
                        getTouchTargetClass(),
                        errors.phone && "border-red-500"
                      )}
                    />
                    {errors.phone && (
                      <p id="phone-error" className={cn("text-red-500 mt-1", getResponsiveTextSize("xs", "sm", "sm"))} role="alert">
                        {errors.phone}
                      </p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="age" className={cn("flex items-center font-medium", getResponsiveTextSize("sm", "base", "base"))}>
                      Age or Birth Year
                      <span className="text-red-500 ml-1" aria-hidden="true">*</span>
                      <span className="sr-only">required</span>
                    </Label>
                    <Input
                      id="age"
                      name="age"
                      placeholder="e.g., 15 or 2008"
                      required
                      aria-required="true"
                      aria-invalid={!!errors.age}
                      aria-describedby={errors.age ? "age-error" : undefined}
                      value={formData.age}
                      onChange={handleChange}
                      className={cn(
                        "focus:ring-2 focus:ring-elev-blue focus:border-elev-blue touch-manipulation",
                        getTouchTargetClass(),
                        errors.age && "border-red-500"
                      )}
                    />
                    {errors.age && (
                      <p id="age-error" className={cn("text-red-500 mt-1", getResponsiveTextSize("xs", "sm", "sm"))} role="alert">
                        {errors.age}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="interest" className={cn("flex items-center font-medium", getResponsiveTextSize("sm", "base", "base"))}>
                      I'm interested in
                      <span className="text-red-500 ml-1" aria-hidden="true">*</span>
                      <span className="sr-only">required</span>
                    </Label>
                    <Select
                      name="interest"
                      value={formData.interest}
                      onValueChange={(value) => handleSelectChange(value, 'interest')}
                    >
                      <SelectTrigger 
                        id="interest"
                        className={cn(
                          "focus:ring-2 focus:ring-elev-blue focus:border-elev-blue touch-manipulation text-left",
                          getTouchTargetClass(),
                          errors.interest && "border-red-500"
                        )}
                        aria-invalid={!!errors.interest}
                        aria-describedby={errors.interest ? "interest-error" : undefined}
                      >
                        <SelectValue placeholder="Select a program" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="individual">Individual Training</SelectItem>
                        <SelectItem value="group">Small Group Training</SelectItem>
                        <SelectItem value="both">Both Individual & Group</SelectItem>
                        <SelectItem value="other">Other (please specify in message)</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.interest && (
                      <p id="interest-error" className={cn("text-red-500 mt-1", getResponsiveTextSize("xs", "sm", "sm"))} role="alert">
                        {errors.interest}
                      </p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="message" className={cn("font-medium", getResponsiveTextSize("sm", "base", "base"))}>
                    Message (optional)
                  </Label>
                  <Textarea
                    id="message"
                    name="message"
                    placeholder="Tell us about your goals, experience level, and any questions you have..."
                    rows={4}
                    value={formData.message}
                    onChange={handleChange}
                    className={cn(
                      "focus:ring-2 focus:ring-elev-blue focus:border-elev-blue touch-manipulation min-h-[120px]",
                      getTouchTargetClass()
                    )}
                  />
                </div>

                <div className="pt-2">
                  <Button
                    type="submit"
                    disabled={submitting}
                    className={cn(
                      "w-full bg-elev-navy hover:bg-elev-navy/90 text-white font-medium",
                      "transition-colors duration-200",
                      "focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-elev-navy",
                      "disabled:opacity-70 disabled:cursor-not-allowed",
                      getTouchTargetClass()
                    )}
                  >
                    {submitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Sending...
                      </>
                    ) : (
                      "Send Message"
                    )}
                  </Button>
                </div>

                <p className={cn("text-center text-gray-500", getResponsiveTextSize("xs", "sm", "sm"))}>
                  We'll get back to you within 24 hours.
                </p>
              </div>
            </form>
          </div>

          <div
            ref={imageRef}
            className={cn(
              "reveal relative rounded-2xl overflow-hidden aspect-[4/5] w-full",
              "bg-gradient-to-br from-elev-navy/10 to-elev-navy/5",
              "flex items-center justify-center p-8",
              { active: isImageVisible }
            )}
          >
            <div className="text-center max-w-md">
              <div className="mx-auto w-16 h-16 bg-elev-navy/10 rounded-full flex items-center justify-center mb-6">
                <FaHockeyPuck className="text-elev-navy text-2xl" />
              </div>
              <h3 className={cn("font-bold text-gray-900 mb-3", getResponsiveTextSize("xl", "2xl", "3xl"))}>
                Ready to take your game to the next level?
              </h3>
              <p className={cn("text-gray-600 mb-6", getResponsiveTextSize("sm", "base", "lg"))}>
                Our expert coaches are here to help you develop your skills, build confidence, and achieve your hockey goals.
              </p>
              <div className="space-y-4 text-left max-w-xs mx-auto">
                <div className="flex items-start gap-3">
                  <FaCheckCircle className="text-elev-navy mt-1 flex-shrink-0" />
                  <div>
                    <h4 className={cn("font-semibold text-gray-900", getResponsiveTextSize("sm", "base", "lg"))}>Personalized Training</h4>
                    <p className={cn("text-gray-500", getResponsiveTextSize("xs", "sm", "sm"))}>Customized programs for all skill levels</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <FaCheckCircle className="text-elev-navy mt-1 flex-shrink-0" />
                  <div>
                    <h4 className={cn("font-semibold text-gray-900", getResponsiveTextSize("sm", "base", "lg"))}>Experienced Coaches</h4>
                    <p className={cn("text-gray-500", getResponsiveTextSize("xs", "sm", "sm"))}>Professional guidance from hockey experts</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <FaCheckCircle className="text-elev-navy mt-1 flex-shrink-0" />
                  <div>
                    <h4 className={cn("font-semibold text-gray-900", getResponsiveTextSize("sm", "base", "lg"))}>Proven Results</h4>
                    <p className={cn("text-gray-500", getResponsiveTextSize("xs", "sm", "sm"))}>See measurable improvement in your game</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  );
};

export default ContactForm;
