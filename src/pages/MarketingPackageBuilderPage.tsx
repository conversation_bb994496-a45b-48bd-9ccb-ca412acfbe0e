import React, { useState, useEffect } from 'react';
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import SEO from "@/components/SEO";
import { Breadcrumbs } from "@/components/ui/Breadcrumbs";
import { useBreadcrumbs, useShouldShowBreadcrumbs } from "@/hooks/useBreadcrumbs";
import { Button } from '@/components/ui/button';
import MarketingOptionCard from '@/components/MarketingOptionCard'; // Import the separate component

// Define a type for marketing options
interface MarketingOption {
  id: string;
  name: string;
  price: string;
  description: string;
  details: string[];
  type: 'checkbox' | 'quantity';
  cost: number;
  selected?: boolean; // Optional for checkbox type
  quantity?: number; // Optional for quantity type
}

const MarketingPackageBuilderPage = () => {
  const breadcrumbs = useBreadcrumbs();
  const shouldShowBreadcrumbs = useShouldShowBreadcrumbs();
  
  // Define marketing options based on the screenshot
  const [marketingOptions, setMarketingOptions] = useState<MarketingOption[]>([
    {
      id: 'center-ice-logo',
      name: 'Center Ice Logo',
      price: '$5,000 / year',
      description: 'Your logo placed at the very center of the ice for unmatched exposure',
      details: [
        'Premium Opportunity: Center ice advertising is considered the most impactful due to its high visibility and strategic placement. The center circle is constantly in view during games and events.',
        'Limited Availability: This exclusive placement is only made available once per year.'
      ],
      type: 'checkbox',
      selected: false,
      cost: 5000 // Assuming a numerical cost for calculation
    },
    {
      id: 'prime-dasherboard',
      name: 'Prime Dasherboard',
      price: '$2,400 / year each',
      description: 'High visibility placement behind benches or at center (choose # of locations)',
      details: [
        'Maximum Visibility: Prime dasherboard advertising offers exceptional exposure, positioned at eye-level in the most viewed areas of the rink.',
        'Brand Impact: These premium locations generate significant brand awareness and are frequently captured during photography and video recording.'
      ],
      type: 'quantity',
      quantity: 0,
      cost: 2400 // Assuming a numerical cost for calculation
    },
    {
      id: 'standard-dasherboard',
      name: 'Standard Dasherboard',
      price: '$1,200 / year each',
      description: 'Standard view placement along the rink perimeter (choose # of locations)',
      details: [
        'Consistent Exposure: Standard dasherboard ads provide reliable visibility throughout all rink activities and events.',
        'Cost-Effective: These placements offer excellent value while maintaining strong brand presence around the facility.'
      ],
      type: 'quantity',
      quantity: 0,
      cost: 1200 // Assuming a numerical cost for calculation
    },
    {
      id: 'digital-presence',
      name: 'Digital Presence',
      price: '$1,200 / year',
      description: 'Website, email, and social media shoutouts to amplify your reach in Vegas',
      details: [
        'Extended Reach: Digital presence extends your brand beyond the physical rink to our online community and followers.',
        'Audience Engagement: Connect with our engaged digital audience through targeted promotions and featured content.'
      ],
      type: 'checkbox',
      selected: false,
      cost: 1200 // Assuming a numerical cost for calculation
    }
  ]);

  // Calculate package total
  const packageTotal = marketingOptions.reduce((total, option) => {
    if (option.type === 'checkbox' && option.selected) {
      return total + option.cost;
    }
    if (option.type === 'quantity') {
      return total + (option.cost * option.quantity);
    }
    return total;
  }, 0);

  // Handlers for selecting/changing quantity
  const handleOptionSelect = (id: string) => {
    setMarketingOptions(marketingOptions.map(option =>
      option.id === id ? { ...option, selected: !option.selected } : option
    ));
  };

  const handleQuantityChange = (id: string, delta: number) => {
    setMarketingOptions(marketingOptions.map(option => {
      if (option.id === id) {
        const newQuantity = Math.max(0, (option.quantity || 0) + delta);
        return { ...option, quantity: newQuantity };
      }
      return option;
    }));
  };

  // Placeholder for form state and submission
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: ''
  });

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Frontend validation
    if (!formData.name || !formData.email) {
      alert("Please fill out all required fields (Name and Email).");
      return;
    }

    // Basic email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      alert("Please enter a valid email address.");
      return;
    }

    const selectedOptionsDetails = marketingOptions
      .filter(option => option.selected || (option.type === 'quantity' && (option.quantity || 0) > 0))
      .map(option => {
        if (option.type === 'checkbox') {
          return `Option: ${option.name}`;
        }
        if (option.type === 'quantity') {
          return `Option: ${option.name}, Quantity: ${option.quantity}`;
        }
        return '';
      })
      .join('\\n'); // Use \\n for newline in the email body

    const formDataForEmail = {
      access_key: "08fffaeb-713e-4759-a962-68c7eb59e6ab", // Web3Forms Access Key
      subject: "New Marketing Package Request",
      "Package Details": selectedOptionsDetails,
      "Package Total": `$${packageTotal.toLocaleString()}`,
      "Name": formData.name,
      "Email": formData.email,
      "Phone Number": formData.phone,
    };

    try {
      const response = await fetch("https://api.web3forms.com/submit", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json"
        },
        body: JSON.stringify(formDataForEmail)
      });

      const result = await response.json();

      if (result.success) {
        alert("Package request submitted successfully!");
        // Optionally clear the form
        setFormData({ name: '', email: '', phone: '' });
        setMarketingOptions(marketingOptions.map(option => {
          if (option.type === 'checkbox') {
            return { ...option, selected: false };
          }
          if (option.type === 'quantity') {
            return { ...option, quantity: 0 };
          }
          return option;
        }));
      } else {
        alert("There was an error submitting your request: " + result.message);
      }
    } catch (error) {
      console.error("Submission error:", error);
      alert("There was an error submitting your request.");
    }

    console.log('Package Request Submitted via Web3Forms:', {
      selectedOptions: marketingOptions.filter(option => option.selected || (option.type === 'quantity' && (option.quantity || 0) > 0)),
      packageTotal,
      formData
    });
  };


  return (
    <div className="flex flex-col min-h-screen font-sans">
      <Header />
      {shouldShowBreadcrumbs && (
        <Breadcrumbs
          items={breadcrumbs}
          showHomeIcon={true}
          separator="chevron"
        />
      )}
      <main className="flex-grow max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <h1 className="text-3xl sm:text-4xl font-bold mb-8 text-center text-elev-navy">Build Your Marketing Package</h1>
        <p className="text-lg text-elev-text-primary mb-6 text-center">Customize your advertising package. Select your placements and build your unique marketing presence!</p>

        {/* Marketing Presentation Section */}
        <section className="bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-lg shadow p-4 sm:p-6 mb-12 w-full max-w-7xl mx-auto" aria-labelledby="presentation-heading">
          <h2 id="presentation-heading" className="text-2xl font-bold text-elev-text-primary mb-6 text-center">Marketing Presentation</h2>
          <div className="relative w-full" style={{ paddingTop: '56.25%' }}>
            <iframe
              src="https://docs.google.com/presentation/d/14Ohnp44WxKxUY41OudFAxEOoH93Ej01HcisVpzJnYcQ/embed?start=false&loop=false&delayms=3000"
              className="absolute top-0 left-0 w-full h-full rounded-lg"
              title="Marketing Package Presentation"
              allowFullScreen
              aria-label="Marketing package presentation slides"
            />
          </div>
          <div className="text-center mt-4">
            <a
              href="https://docs.google.com/presentation/d/14Ohnp44WxKxUY41OudFAxEOoH93Ej01HcisVpzJnYcQ/edit?slide=id.p12#slide=id.p12"
              target="_blank"
              rel="noopener noreferrer"
              className="text-elev-blue hover:underline focus:outline-none focus:ring-2 focus:ring-elev-blue focus:ring-offset-2 rounded"
              aria-label="Open marketing presentation in new tab"
            >
              Open presentation in new tab
            </a>
          </div>
        </section>
        {/* Facility Overview */}
        <section className="bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-lg shadow p-6 mb-12" aria-labelledby="facility-overview-heading">
          <h2 id="facility-overview-heading" className="text-2xl font-bold text-elev-navy mb-6 text-center">Facility Overview</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6" role="list">
            <div 
              className="text-center p-6 bg-elev-background-muted rounded-lg hover:bg-elev-background-accent transition-colors" 
              role="listitem"
            >
              <h3 className="text-xl font-semibold mb-2 text-elev-text-primary">NHL-Style Rink</h3>
              <p className="text-elev-text-secondary">
                Professional ice surface dedicated to on-ice training
                <span className="sr-only">Our rink is built to NHL specifications, providing a professional training environment.</span>
              </p>
            </div>
            <div 
              className="text-center p-6 bg-elev-background-muted rounded-lg hover:bg-elev-background-accent transition-colors" 
              role="listitem"
            >
              <h3 className="text-xl font-semibold mb-2 text-elev-text-primary">130,000+ Annual Visitors</h3>
              <p className="text-elev-text-secondary">
                Consistent foot traffic throughout the year
                <span className="sr-only">We welcome over 130,000 visitors annually, providing excellent exposure for your brand.</span>
              </p>
            </div>
            <div 
              className="text-center p-6 bg-elev-background-muted rounded-lg hover:bg-elev-background-accent transition-colors" 
              role="listitem"
            >
              <h3 className="text-xl font-semibold mb-2 text-elev-text-primary">Premier Training Facility</h3>
              <p className="text-elev-text-secondary">
                Destination for serious athletes and families
                <span className="sr-only">Our state-of-the-art facility attracts dedicated athletes and families committed to hockey excellence.</span>
              </p>
            </div>
          </div>
        </section>

        {/* Audience Profile */}
        <section className="bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-lg shadow p-6 mb-12" aria-labelledby="audience-profile-heading">
          <h2 id="audience-profile-heading" className="text-2xl font-bold text-elev-text-primary mb-6 text-center">Audience Profile</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6" role="list">
            <div 
              className="text-center p-6 bg-elev-background-muted rounded-lg hover:bg-elev-background-accent transition-colors" 
              role="listitem"
            >
              <h3 className="text-xl font-semibold mb-2 text-elev-text-primary">High Income</h3>
              <p className="text-elev-text-secondary">
                $125K+ average household income
                <span className="sr-only">Our audience consists of high-income households with significant purchasing power.</span>
              </p>
            </div>
            <div 
              className="text-center p-6 bg-elev-background-muted rounded-lg hover:bg-elev-background-accent transition-colors" 
              role="listitem"
            >
              <h3 className="text-xl font-semibold mb-2 text-elev-text-primary">Local Decision Makers</h3>
              <p className="text-elev-text-secondary">
                85% homeowners from premium Vegas neighborhoods
                <span className="sr-only">The majority of our audience are homeowners from affluent Las Vegas neighborhoods, representing key decision makers in their communities.</span>
              </p>
            </div>
            <div 
              className="text-center p-6 bg-elev-background-muted rounded-lg hover:bg-elev-background-accent transition-colors" 
              role="listitem"
            >
              <h3 className="text-xl font-semibold mb-2 text-elev-text-primary">Dedicated Sports Families</h3>
              <p className="text-elev-text-secondary">
                Parents and youth athletes with consistent weekly visits
                <span className="sr-only">Our core audience includes committed sports families who regularly visit our facility, providing consistent exposure for your brand.</span>
              </p>
            </div>
          </div>
        </section>

        {/* Engagement Statistics */}
        <section className="bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-lg shadow p-6 mb-12" aria-labelledby="engagement-stats-heading">
          <h2 id="engagement-stats-heading" className="text-2xl font-bold text-elev-text-primary mb-6 text-center">Weekly Engagement</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6" role="list">
            <div 
              className="text-center p-6 bg-elev-background-muted rounded-lg hover:bg-elev-background-accent transition-colors" 
              role="listitem"
            >
              <h3 className="text-xl font-semibold mb-2 text-elev-text-primary">450+</h3>
              <p className="text-elev-text-secondary">
                Social Media Mentions
                <span className="sr-only">Over 450 social media mentions weekly, providing significant online visibility.</span>
              </p>
            </div>
            <div 
              className="text-center p-6 bg-elev-background-muted rounded-lg hover:bg-elev-background-accent transition-colors" 
              role="listitem"
            >
              <h3 className="text-xl font-semibold mb-2 text-elev-text-primary">325+</h3>
              <p className="text-elev-text-secondary">
                Photo Shares
                <span className="sr-only">More than 325 photos shared weekly, featuring our facility and activities.</span>
              </p>
            </div>
            <div 
              className="text-center p-6 bg-elev-background-muted rounded-lg hover:bg-elev-background-accent transition-colors" 
              role="listitem"
            >
              <h3 className="text-xl font-semibold mb-2 text-elev-text-primary">250+</h3>
              <p className="text-elev-text-secondary">
                Event Attendees
                <span className="sr-only">Over 250 attendees at our weekly events, creating consistent in-person engagement.</span>
              </p>
            </div>
            <div 
              className="text-center p-6 bg-elev-background-muted rounded-lg hover:bg-elev-background-accent transition-colors" 
              role="listitem"
            >
              <h3 className="text-xl font-semibold mb-2 text-elev-text-primary">800+</h3>
              <p className="text-elev-text-secondary">
                Website Visits
                <span className="sr-only">More than 800 unique website visits per week, demonstrating strong online presence.</span>
              </p>
            </div>
          </div>
        </section>

        {/* Marketing Options Grid */}
        <section 
          className="mb-12" 
          aria-labelledby="marketing-options-heading"
          role="region"
        >
          <h2 
            id="marketing-options-heading" 
            className="text-2xl font-bold text-elev-text-primary mb-6 text-center"
          >
            Marketing Options
            <span className="sr-only">Use arrow keys to navigate between options, Space or Enter to select</span>
          </h2>
          <div 
            className="grid grid-cols-1 md:grid-cols-2 gap-6" 
            role="list"
            aria-label="Available marketing options"
            onKeyDown={(e) => {
              const currentFocus = document.activeElement;
              const cards = Array.from(document.querySelectorAll('[data-marketing-card]'));
              const currentIndex = cards.indexOf(currentFocus);

              switch (e.key) {
                case 'ArrowRight':
                case 'ArrowDown':
                  e.preventDefault();
                  if (currentIndex < cards.length - 1) {
                    (cards[currentIndex + 1] as HTMLElement).focus();
                  }
                  break;
                case 'ArrowLeft':
                case 'ArrowUp':
                  e.preventDefault();
                  if (currentIndex > 0) {
                    (cards[currentIndex - 1] as HTMLElement).focus();
                  }
                  break;
                case 'Home':
                  e.preventDefault();
                  (cards[0] as HTMLElement).focus();
                  break;
                case 'End':
                  e.preventDefault();
                  (cards[cards.length - 1] as HTMLElement).focus();
                  break;
              }
            }}
          >
            {marketingOptions.map((option, index) => (
              <MarketingOptionCard
                key={option.id}
                option={option}
                onSelect={handleOptionSelect}
                onQuantityChange={handleQuantityChange}
              />
            ))}
          </div>
        </section>

        {/* Package Total */}
        <section 
          className="bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-lg shadow p-6 mb-12 text-center" 
          aria-labelledby="package-total-heading"
        >
          <h2 
            id="package-total-heading" 
            className="text-2xl font-bold text-elev-text-primary mb-4"
          >
            <span className="sr-only">Selected marketing options total cost: </span>
            Package Total: ${packageTotal.toLocaleString()}
          </h2>
          <div 
            className="text-elev-text-secondary" 
            role="status" 
            aria-live="polite"
          >
            {packageTotal > 0 ? (
              <p>Your selected marketing package includes {marketingOptions.filter(opt => opt.selected || (opt.type === 'quantity' && (opt.quantity || 0) > 0)).length} items</p>
            ) : (
              <p>Select marketing options above to build your package</p>
            )}
          </div>
        </section>

        {/* Contact Form */}
        <section 
          className="bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-lg shadow p-8 mb-12 max-w-lg mx-auto"
          aria-labelledby="contact-form-heading"
        >
          <h2 
            id="contact-form-heading" 
            className="text-2xl font-bold text-elev-text-primary mb-6"
          >
            Contact Us
          </h2>
          <form 
            onSubmit={handleSubmit}
            aria-label="Package request form"
            noValidate
          >
            <div className="mb-4" role="group">
              <label 
                htmlFor="name" 
                className="block text-elev-text-primary font-semibold mb-2"
              >
                Your Name
                <span className="text-red-500 ml-1" aria-hidden="true">*</span>
                <span className="sr-only">required</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-elev-blue focus:ring-offset-2 transition-colors bg-gradient-to-b from-gray-100 via-gray-50 to-white text-elev-text-primary placeholder:text-elev-text-secondary"
                onChange={handleFormChange}
                value={formData.name}
                required
                aria-required="true"
                aria-invalid={!formData.name}
                aria-describedby="name-error"
                placeholder="Enter your full name"
              />
              {!formData.name && (
                <div 
                  id="name-error" 
                  className="text-red-500 text-sm mt-1" 
                  role="alert"
                >
                  Please enter your name
                </div>
              )}
            </div>
            <div className="mb-4" role="group">
              <label 
                htmlFor="email" 
                className="block text-elev-text-primary font-semibold mb-2"
              >
                Your Email
                <span className="text-red-500 ml-1" aria-hidden="true">*</span>
                <span className="sr-only">required</span>
              </label>
              <input
                type="email"
                id="email"
                name="email"
                className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-elev-blue focus:ring-offset-2 transition-colors bg-gradient-to-b from-gray-100 via-gray-50 to-white text-elev-text-primary placeholder:text-elev-text-secondary"
                onChange={handleFormChange}
                value={formData.email}
                required
                aria-required="true"
                aria-invalid={!formData.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)}
                aria-describedby="email-error"
                placeholder="<EMAIL>"
              />
              {(!formData.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) && (
                <div 
                  id="email-error" 
                  className="text-red-500 text-sm mt-1" 
                  role="alert"
                >
                  {!formData.email ? 'Please enter your email' : 'Please enter a valid email address'}
                </div>
              )}
            </div>
            <div className="mb-6" role="group">
              <label 
                htmlFor="phone" 
                className="block text-elev-text-primary font-semibold mb-2"
              >
                Phone Number
                <span className="text-elev-text-secondary ml-2">(Optional)</span>
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-elev-blue focus:ring-offset-2 transition-colors bg-gradient-to-b from-gray-100 via-gray-50 to-white text-elev-text-primary placeholder:text-elev-text-secondary"
                onChange={handleFormChange}
                value={formData.phone}
                aria-describedby="phone-hint"
                placeholder="(*************"
              />
              <div 
                id="phone-hint" 
                className="text-elev-text-secondary text-sm mt-1"
              >
                We'll only use this for urgent updates about your package request
              </div>
            </div>
            <Button 
              type="submit" 
              variant="navy"
              size="lg"
              className="w-full"
              aria-label="Submit package request form"
            >
              Submit Package Request
            </Button>
          </form>
        </section>

        {/* Contact Our Advertising Team */}
        <section 
          className="bg-elev-background-muted rounded-lg shadow p-6 max-w-lg mx-auto text-center"
          aria-labelledby="advertising-team-heading"
        >
          <h2 
            id="advertising-team-heading" 
            className="text-2xl font-bold text-elev-text-primary mb-4"
          >
            Contact Our Advertising Team
          </h2>
          <p className="text-elev-text-primary mb-4">Ready to book your placements or have questions?</p>
          <ul 
            className="list-none p-0 m-0 text-elev-text-secondary space-y-3"
            aria-label="Contact options"
          >
            <li>
              <span className="sr-only">Email address: </span>
              <a 
                href="mailto:<EMAIL>" 
                className="text-elev-blue hover:text-elev-blue/90 hover:underline focus:outline-none focus:ring-2 focus:ring-elev-blue focus:ring-offset-2 rounded-sm inline-flex items-center"
                aria-label="Email sales at 702hockey.com"
              >
                <svg 
                  className="h-5 w-5 mr-2" 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" 
                  />
                </svg>
                <EMAIL>
              </a>
            </li>
            <li>
              Schedule a tour or request a formal proposal via our website.
              <span className="sr-only">Use the contact form above to schedule.</span>
            </li>
            <li>
              <strong className="text-elev-text-primary">Customize your marketing</strong> for Las Vegas' premier sports community!
            </li>
          </ul>
        </section>

      </main>
      <Footer />
    </div>
  );
};

export default MarketingPackageBuilderPage;
