import { useEffect } from "react";
import { Helmet } from "react-helmet-async";
import { Link } from 'react-router-dom';
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import NavigationLink from "@/components/ui/NavigationLink";
import NavigationButton from "@/components/ui/NavigationButton";
import TrainingProgramSEO from "@/components/TrainingProgramSEO";
import ResponsiveImage from "@/components/ui/responsive-image";
import { Breadcrumbs } from "@/components/ui/Breadcrumbs";
import { useBreadcrumbs, useShouldShowBreadcrumbs } from "@/hooks/useBreadcrumbs";

// Import images
import hockeyTrainingImg1 from "../assets/A92462C0-.jpg";
import hockeyTrainingImg2 from "../assets/B6CB678B-.jpg";
import hockeyTrainingImg3 from "../assets/D780B74F-.jpg";

const PopUpClinics = () => {
  const breadcrumbs = useBreadcrumbs();
  const shouldShowBreadcrumbs = useShouldShowBreadcrumbs();
  
  useEffect(() => {
    // Page title now handled by SEO component

    // Implement scroll reveal
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("active");
          }
        });
      },
      { threshold: 0.1 }
    );

    const revealElements = document.querySelectorAll(".reveal");
    revealElements.forEach((el) => observer.observe(el));

    return () => {
      revealElements.forEach((el) => observer.unobserve(el));
    };
  }, []);

  const clinicSessions = [
    {
      date: "August 4-7, 2025",
      sessions: [
        { time: "7:30 AM - 3:30 PM", ageGroup: "All Ages", color: "bg-blue-100 text-blue-800" }
      ]
    }
  ];

  const clinicFeatures = [
    {
      title: "Expert Coaching",
      description: "Learn from experienced coaches with proven track records in player development",
      iconColor: "bg-blue-100",
      iconTextColor: "text-blue-600",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
      ),
    },
    {
      title: "All Ages Training",
      description: "Comprehensive training suitable for players of all ages and skill levels",
      iconColor: "bg-green-100",
      iconTextColor: "text-green-600",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      ),
    },
    {
      title: "Skill Development",
      description: "Focus on fundamental skills including skating, stickhandling, and shooting",
      iconColor: "bg-purple-100",
      iconTextColor: "text-purple-600",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
    },
    {
      title: "Small Group Setting",
      description: "Limited participants ensure personalized attention and maximum development",
      iconColor: "bg-red-100",
      iconTextColor: "text-red-600",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      ),
    }
  ];

  return (
    <div className="flex flex-col min-h-screen">
      <TrainingProgramSEO
        title="Camps + Clinics - ELEV802 | 702HOCKEY - Premier Hockey Training in Las Vegas"
        programName="ELEV802 Hockey Camps and Clinics"
        programDescription="Join our specialized hockey camps and pop-up clinics designed for players of all ages and skill levels at ELEV802's premier facility in Las Vegas."
        programImage="/images/pop-up-clinics.jpg"
        programType="pop-up"
        slug="camps-clinics"
      />
      <Header />
      {shouldShowBreadcrumbs && (
        <Breadcrumbs
          items={breadcrumbs}
          showHomeIcon={true}
          separator="chevron"
        />
      )}
      <main className="flex-grow">

        {/* Upcoming Clinic Section */}
        <section className="py-20 bg-gradient-to-b from-gray-100 via-gray-50 to-white reveal">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold mb-6 text-elev-navy">LAS VEGAS 4-DAY HOCKEY CAMP</h2>
              <p className="text-xl text-elev-text-secondary max-w-3xl mx-auto">
                Intensive 4-day hockey camp featuring comprehensive on-ice and off-ice training with expert coaching
              </p>
            </div>
            
            {/* Camp Dates & Age Groups */}
            <div className="mb-12">
              <h3 className="text-3xl font-bold text-center mb-8 text-elev-navy">CAMP SESSIONS</h3>
              <div className="bg-[#1e3a8a] p-6 rounded-lg text-center max-w-4xl mx-auto">
  <div className="mb-4">
    <h4 className="text-xl font-semibold mb-1 text-white">📅 August 4-7</h4>
    <p className="text-white font-medium">All Ages Welcome</p>
  </div>
</div>
            </div>

            {/* Daily Schedule */}
            <div className="mb-12">
              <h3 className="text-3xl font-bold text-center mb-8 text-elev-navy">📅 DAILY SCHEDULE</h3>
              <div className="bg-gradient-to-b from-gray-100 via-gray-50 to-white p-8 rounded-lg max-w-4xl mx-auto border border-[#0A2240]">
                <div className="text-center mb-6">
                  <p className="text-2xl font-semibold text-elev-text-primary">🕘 7:30 AM – 3:30 PM</p>
                </div>
                <div className="grid md:grid-cols-2 gap-8">
                  <div>
                    <h4 className="text-xl font-semibold mb-4 text-elev-text-primary">Training Sessions</h4>
                    <ul className="space-y-3 text-elev-text-primary">
                      <li>• 2 on-ice sessions daily</li>
                      <li>• 2 off-ice sessions daily</li>
                      <li>• 🎥 Hockey Talk & Video Sessions</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-xl font-semibold mb-4 text-elev-text-primary">Included</h4>
                    <ul className="space-y-3 text-elev-text-primary">
                      <li>• 🥤 Snacks & Drinks Provided by Elev8</li>
                      <li>• 🎽 Jersey & Socks Included</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Location & Dates */}
            <div className="bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-xl p-8 mb-12 max-w-4xl mx-auto border border-[#0A2240]">
              <div className="grid md:grid-cols-2 gap-8">
                <a 
                  href="https://maps.google.com/?q=5031+Wagon+Trail+Ave+STE+100,+Las+Vegas,+NV+89118" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="block hover:bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-lg p-4 -m-4 transition-colors cursor-pointer"
                >
                  <h3 className="text-2xl font-bold mb-4 text-elev-navy flex items-center">
                    <svg className="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    Location
                    <svg className="w-4 h-4 ml-2 text-elev-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </h3>
                  <p className="text-lg text-elev-text-primary mb-2">ELEV802 VEGAS</p>
                  <p className="text-elev-text-secondary">5031 Wagon Trail Ave STE 100</p>
                  <p className="text-elev-text-secondary">Las Vegas, NV 89118</p>
                  <p className="text-sm text-blue-600 mt-2 font-medium">Click to view in Google Maps</p>
                </a>
                <div>
                  <h3 className="text-2xl font-bold mb-4 text-elev-navy flex items-center">
                    <svg className="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    Camp Details
                  </h3>
                  <p className="text-lg text-elev-text-primary mb-2">4-day Intensive Camp</p>
                  <p className="text-elev-text-secondary">2 on-ice + 2 off-ice sessions daily</p>
                  <p className="text-elev-text-secondary">Comprehensive training program</p>
                </div>
              </div>
            </div>

            {/* Camp Inclusions */}
            <div className="max-w-6xl mx-auto">
              <h3 className="text-3xl font-bold text-center mb-12 text-elev-navy">CAMP INCLUSIONS</h3>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-xl shadow-lg p-6 border border-[#0A2240]">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h4 className="text-lg font-bold mb-2">Daily Sessions</h4>
                  <p className="text-elev-text-secondary">2 on-ice + 2 off-ice sessions per day</p>
                </div>
                <div className="bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-xl shadow-lg p-6 border border-[#0A2240]">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                  </div>
                  <h4 className="text-lg font-bold mb-2">Gear Included</h4>
                  <p className="text-elev-text-secondary">Jerseys + socks provided</p>
                </div>
                <div className="bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-xl shadow-lg p-6 border border-[#0A2240]">
                  <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h4 className="text-lg font-bold mb-2">🎥 Hockey Talk & Video Sessions</h4>
                  <p className="text-elev-text-secondary">Educational sessions included</p>
                </div>
                <div className="bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-xl shadow-lg p-6 border border-[#0A2240]">
                  <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 3H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
                    </svg>
                  </div>
                  <h4 className="text-lg font-bold mb-2">🥤 Snacks & Drinks</h4>
                  <p className="text-elev-text-secondary">Provided by ELEV802 VEGAS</p>
                </div>
              </div>
            </div>

            {/* Extras Section */}
            <div className="max-w-6xl mx-auto mt-16">
              <h3 className="text-3xl font-bold text-center mb-12 text-elev-navy">🎁 EXTRAS</h3>
              <div className="grid md:grid-cols-3 gap-6">
                <div className="bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-xl shadow-lg p-6 border border-[#0A2240]">
                  <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                  <h4 className="text-lg font-bold mb-2">🔥 Swag Bags</h4>
                  <p className="text-elev-text-secondary">Exclusive camp merchandise</p>
                </div>
                <div className="bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-xl shadow-lg p-6 border border-[#0A2240]">
                  <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                    </svg>
                  </div>
                  <h4 className="text-lg font-bold mb-2">🏆 Camp MVP Prize</h4>
                  <p className="text-elev-text-secondary">A Pure Hockey Gift Card</p>
                </div>
                <div className="bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-xl shadow-lg p-6 border border-[#0A2240]">
                  <div className="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h4 className="text-lg font-bold mb-2">🎉 Daily Giveaways</h4>
                  <p className="text-elev-text-secondary">Prizes throughout the camp</p>
                </div>
              </div>
              
              {/* What to Bring */}
              <div className="mt-12 bg-yellow-50 rounded-xl p-8">
                <h3 className="text-2xl font-bold mb-6 text-elev-navy text-center">WHAT TO BRING</h3>
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="flex items-start">
                    <svg className="w-6 h-6 text-yellow-600 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                    </svg>
                    <div>
                      <h4 className="font-bold text-elev-text-primary mb-2">Your Own Lunch</h4>
                      <p className="text-elev-text-primary">Bring lunch for each day of camp</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <svg className="w-6 h-6 text-yellow-600 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                    </svg>
                    <div>
                      <h4 className="font-bold text-elev-text-primary mb-2">Off-Ice Training Gear</h4>
                      <p className="text-elev-text-primary">Running shoes, training clothes, and spare change of clothes</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 bg-gradient-to-b from-gray-100 via-gray-50 to-white reveal">
          <div className="container mx-auto px-4">
            <h2 className="text-4xl font-bold text-center mb-12 text-elev-navy">CAMP PRICING</h2>
            <div className="max-w-2xl mx-auto">
              <div className="bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-xl shadow-lg p-8 text-center border-2 border-elev-navy relative">
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-elev-navy text-white px-4 py-2 rounded-full text-sm font-medium">4-day CAMP</span>
                </div>
                <div className="mb-6">
                  <h3 className="text-3xl font-bold text-elev-text-primary mb-2">Complete Camp Package</h3>
                  <p className="text-elev-text-secondary">Everything included for 4 days of intensive training</p>
                </div>
                <div className="mb-8">
                  <span className="text-6xl font-bold text-elev-navy">$799</span>
                  <span className="text-2xl text-elev-text-secondary">.99</span>
                </div>
                {/* Booking buttons section - Updated June 12, 2025 */}
                <div className="mb-8">
                  <div className="flex flex-col sm:flex-row justify-center gap-4">
                    <div className="flex justify-center">
                      <a
                        href="https://wix.702hockey.com/pricing-plans/list"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-[#0ab43b] text-white px-8 py-3 rounded-lg font-semibold inline-block hover:bg-[#099a33] transition-all transform hover:scale-105"
                      >
                        BOOK NOW
                      </a>
                    </div>
                  </div>
                </div>
                <div className="grid md:grid-cols-2 gap-4 text-left mb-6">
                  <ul className="space-y-3">
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>4 full days of training</span>
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>4 sessions per day</span>
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>Jerseys & socks included</span>
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>Goodie bag</span>
                    </li>
                  </ul>
                  <ul className="space-y-3">
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>Video session & hockey talk</span>
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>MVP prizes</span>
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>Snacks & drinks</span>
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>Expert coaching</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20 bg-gradient-to-b from-gray-100 via-gray-50 to-white reveal relative">
          <div className="container mx-auto px-4">
            <h2 className="text-4xl font-bold text-center mb-16 text-elev-navy">WHAT TO EXPECT</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
              {clinicFeatures.map((feature, index) => (
                <div key={index} className="bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow">
                  <div className={`w-16 h-16 ${feature.iconColor} rounded-full flex items-center justify-center mb-6`}>
                    <div className={feature.iconTextColor}>
                      {feature.icon}
                    </div>
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-elev-text-primary">{feature.title}</h3>
                  <p className="text-elev-text-secondary">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
          {/* Decorative bottom element */}
          <div className="absolute bottom-0 left-0 w-full h-16 bg-gradient-to-b from-gray-100 via-gray-50 to-white transform -skew-y-2 translate-y-8"></div>
        </section>

        {/* Training Images Section */}
        <section className="py-16 bg-gradient-to-b from-gray-100 via-gray-50 to-white reveal">
          <div className="container mx-auto px-4">
            <h2 className="text-4xl font-bold text-center mb-12 text-elev-navy">TRAINING IN ACTION</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="relative overflow-hidden rounded-lg hover:opacity-90 transition-opacity">
                <ResponsiveImage
                  src={hockeyTrainingImg1}
                  alt="Hockey players practicing on ice during training session"
                  aspectRatio="landscape"
                  objectFit="cover"
                  className="w-full h-full transition-transform duration-300 hover:scale-105"
                  loading="lazy"
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                  sources={[
                    { src: hockeyTrainingImg1, width: 400 },  // Mobile
                    { src: hockeyTrainingImg1, width: 600 },  // Tablet
                    { src: hockeyTrainingImg1, width: 800 },  // Desktop
                  ]}
                />
              </div>
              <div className="relative overflow-hidden rounded-lg hover:opacity-90 transition-opacity">
                <ResponsiveImage
                  src={hockeyTrainingImg2}
                  alt="Intensive hockey training session with professional coaching"
                  aspectRatio="landscape"
                  objectFit="cover"
                  className="w-full h-full transition-transform duration-300 hover:scale-105"
                  loading="lazy"
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                  sources={[
                    { src: hockeyTrainingImg2, width: 400 },  // Mobile
                    { src: hockeyTrainingImg2, width: 600 },  // Tablet
                    { src: hockeyTrainingImg2, width: 800 },  // Desktop
                  ]}
                />
              </div>
              <div className="relative overflow-hidden rounded-lg hover:opacity-90 transition-opacity">
                <ResponsiveImage
                  src={hockeyTrainingImg3}
                  alt="Hockey skills development and technique training"
                  aspectRatio="landscape"
                  objectFit="cover"
                  className="w-full h-full transition-transform duration-300 hover:scale-105"
                  loading="lazy"
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                  sources={[
                    { src: hockeyTrainingImg3, width: 400 },  // Mobile
                    { src: hockeyTrainingImg3, width: 600 },  // Tablet
                    { src: hockeyTrainingImg3, width: 800 },  // Desktop
                  ]}
                />
              </div>
            </div>
          </div>
        </section>

        {/* Registration Section */}
        <section className="py-16 bg-gradient-to-b from-gray-100 via-gray-50 to-white reveal">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-4xl font-bold mb-8 text-elev-navy">REGISTRATION INFORMATION</h2>
              <div className="bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-xl shadow-lg p-8 mb-8">
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="text-left">
                    <h3 className="text-2xl font-bold mb-4 text-elev-text-primary">How to Register</h3>
                    <p className="text-lg text-elev-text-primary mb-4">
                      Registration is simple and secure. Contact our team to reserve your spot in the clinic sessions.
                    </p>
                    <ul className="space-y-2 text-elev-text-primary">
                      <li className="flex items-center">
                        <svg className="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        Limited spots available
                      </li>
                      <li className="flex items-center">
                        <svg className="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        First come, first served
                      </li>
                      <li className="flex items-center">
                        <svg className="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        Secure online payment
                      </li>
                    </ul>
                  </div>
                  <div className="bg-elev-navy text-white rounded-lg p-6 text-center">
                    <h4 className="text-xl font-bold mb-4">Contact Information</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-center">
                        <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        <span><EMAIL></span>
                      </div>
                      <p className="text-sm opacity-90">For registration questions and support</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-blue-900 text-white reveal">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-4xl font-bold mb-6">READY TO ELEVATE YOUR GAME?</h2>
            <p className="text-xl max-w-3xl mx-auto mb-8">
              Don't miss this opportunity to join our intensive 4-day hockey camp with expert coaches and comprehensive training. 
              Register today for the Las Vegas 4-day Hockey Camp!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              
              
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default PopUpClinics;