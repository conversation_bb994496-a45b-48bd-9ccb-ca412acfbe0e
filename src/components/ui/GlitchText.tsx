import React from 'react';
import '@/components/ui/GlitchText.css';

/**
 * GlitchText now displays a looping lightning effect overlay on the text using pure CSS.
 * The effect uses a yellow/white animated SVG lightning bolt, flashes briefly in a loop, and is accessible/readable.
 * Props:
 *   - text: The string to display with the lightning effect overlay.
 *   - className: Optional additional classes for the wrapper.
 */
interface GlitchTextProps {
  text: string;
  className?: string;
}

const GlitchText: React.FC<GlitchTextProps> = ({ text, className = '' }) => {
  return (
    <span className={`glitch-wrapper ${className}`}>
      <span className="glitch" data-text={text}>
        {text}
      </span>
    </span>
  );
};

export default GlitchText;
