import { createRoot } from 'react-dom/client';
import './index.css';
import { RootComponent } from './components/root';
import { registerInstagramElement } from './components/instagram';

console.log('Application starting in', import.meta.env.MODE, 'mode');

// Register the Instagram custom element
registerInstagramElement();

// Render the main app
createRoot(document.getElementById('root')!).render(<RootComponent />);

// Initialize stagewise toolbar in development mode only
if (import.meta.env.MODE === 'development') {
  Promise.all([
    import('@stagewise/toolbar-react'),
    import('@stagewise-plugins/react')
  ]).then(([{ StagewiseToolbar }, { ReactPlugin }]) => {
    // Create a separate React root for the toolbar to avoid interfering with the main app
    const toolbarContainer = document.createElement('div');
    toolbarContainer.id = 'stagewise-toolbar-container';
    document.body.appendChild(toolbarContainer);
    
    const stagewiseConfig = {
      plugins: [ReactPlugin]
    };
    
    createRoot(toolbarContainer).render(
      <StagewiseToolbar config={stagewiseConfig} />
    );
    
    console.log('Stagewise toolbar initialized in development mode');
  }).catch(error => {
    console.error('Failed to initialize Stagewise toolbar:', error);
  });
}

// Initialize the application
console.log('Application initialized');
