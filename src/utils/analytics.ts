// Google Analytics utility functions
// This file provides functions for initializing and tracking with Google Analytics 4

// Type definitions for Google Analytics gtag
declare global {
  interface Window {
    dataLayer: Array<Record<string, unknown>>;
    gtag: Gtag;
  }
}

type Gtag = (
  command: 'config' | 'event',
  targetId: string,
  configObject?: {
    page_title?: string;
    page_path?: string;
    page_location?: string;
    event_category?: string;
    event_label?: string;
    value?: number;
    [key: string]: unknown;
  }
) => void;

// Initialize Google Analytics
export const initGA = (measurementId: string): void => {
  // Check if gtag is available (loaded via script tag)
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', measurementId, {
      page_title: document.title,
      page_location: window.location.href,
    });
  }
};

// Track page views
export const trackPageView = (path: string): void => {
  // Check if gtag is available
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', 'G-5HDKYBGC86', {
      page_path: path,
      page_title: document.title,
      page_location: window.location.href,
    });
  }
};

// Track custom events
export const trackEvent = (action: string, category: string, label?: string, value?: number): void => {
  // Check if gtag is available
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    });
  }
};