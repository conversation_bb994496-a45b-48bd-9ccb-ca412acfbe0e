// Tests for typography standardization utilities

import { TypographyStandardizationUtilities, TypographyFix, TypographyValidationResult } from '../typography-standardization';
import { FileInfo, Severity } from '../../types';

describe('TypographyStandardizationUtilities', () => {
  let utilities: TypographyStandardizationUtilities;

  beforeEach(() => {
    utilities = new TypographyStandardizationUtilities();
  });

  describe('convertHardcodedToTokens', () => {
    it('should identify hardcoded font sizes and suggest token replacements', async () => {
      const files: FileInfo[] = [
        {
          path: 'test.css',
          content: `
            .title { font-size: 24px; }
            .subtitle { font-size: 1.5rem; }
            .body { font-size: 16px; }
          `,
          extension: '.css',
          size: 100,
          lastModified: new Date()
        }
      ];

      const fixes = await utilities.convertHardcodedToTokens(files);

      expect(fixes).toHaveLength(3);
      expect(fixes[0]).toMatchObject({
        filePath: 'test.css',
        originalValue: '24px',
        fixType: 'hardcoded-to-token'
      });
      expect(fixes[0].replacementValue).toContain('var(--font-size-');
    });

    it('should identify hardcoded font families and suggest token replacements', async () => {
      const files: FileInfo[] = [
        {
          path: 'test.css',
          content: `
            .custom { font-family: "Inter", sans-serif; }
            .brand { font-family: "Roboto Slab", serif; }
          `,
          extension: '.css',
          size: 100,
          lastModified: new Date()
        }
      ];

      const fixes = await utilities.convertHardcodedToTokens(files);

      expect(fixes.length).toBeGreaterThan(0);
      const fontFamilyFixes = fixes.filter(fix => fix.tokenName.includes('font-family'));
      expect(fontFamilyFixes.length).toBeGreaterThan(0);
    });

    it('should identify hardcoded font weights and suggest token replacements', async () => {
      const files: FileInfo[] = [
        {
          path: 'test.css',
          content: `
            .bold { font-weight: 700; }
            .medium { font-weight: 500; }
            .light { font-weight: 300; }
          `,
          extension: '.css',
          size: 100,
          lastModified: new Date()
        }
      ];

      const fixes = await utilities.convertHardcodedToTokens(files);

      expect(fixes).toHaveLength(3);
      expect(fixes.find(fix => fix.originalValue.includes('700'))).toMatchObject({
        replacementValue: 'font-weight: var(--font-weight-bold)',
        tokenName: 'font-weight-bold'
      });
    });

    it('should handle React inline styles', async () => {
      const files: FileInfo[] = [
        {
          path: 'Component.tsx',
          content: `
            const styles = {
              title: { fontSize: '24px', fontWeight: '700' },
              subtitle: { fontSize: '18px' }
            };
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const fixes = await utilities.convertHardcodedToTokens(files);

      expect(fixes.length).toBeGreaterThan(0);
      const reactFixes = fixes.filter(fix => fix.originalValue.includes('fontSize:'));
      expect(reactFixes.length).toBeGreaterThan(0);
    });

    it('should skip system fonts', async () => {
      const files: FileInfo[] = [
        {
          path: 'test.css',
          content: `
            .system { font-family: system-ui, sans-serif; }
            .arial { font-family: Arial, sans-serif; }
          `,
          extension: '.css',
          size: 100,
          lastModified: new Date()
        }
      ];

      const fixes = await utilities.convertHardcodedToTokens(files);

      // Should not suggest fixes for system fonts
      expect(fixes).toHaveLength(0);
    });
  });

  describe('validateTypographyHierarchy', () => {
    it('should detect typography hierarchy violations', async () => {
      const files: FileInfo[] = [
        {
          path: 'page.html',
          content: `
            <h1 style="font-size: 14px;">Small Heading</h1>
            <h2 style="font-size: 32px; font-weight: 300;">Wrong Weight</h2>
            <h3 style="font-size: 2rem; line-height: 1.1;">Tight Line Height</h3>
          `,
          extension: '.html',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await utilities.validateTypographyHierarchy(files);

      expect(result.isValid).toBe(false);
      expect(result.violations.length).toBeGreaterThan(0);
      
      const h1Violations = result.violations.filter(v => v.element === 'h1');
      expect(h1Violations.length).toBeGreaterThan(0);
      expect(h1Violations[0].property).toBe('fontSize');
    });

    it('should pass validation for correct hierarchy', async () => {
      const files: FileInfo[] = [
        {
          path: 'page.html',
          content: `
            <h1 style="font-size: 2.25rem; font-weight: 700; line-height: 1.2;">Correct H1</h1>
            <h2 style="font-size: 1.875rem; font-weight: 600; line-height: 1.3;">Correct H2</h2>
          `,
          extension: '.html',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await utilities.validateTypographyHierarchy(files);

      expect(result.isValid).toBe(true);
      expect(result.violations).toHaveLength(0);
    });

    it('should provide helpful suggestions for violations', async () => {
      const files: FileInfo[] = [
        {
          path: 'page.html',
          content: `<h1 style="font-size: 12px;">Too Small</h1>`,
          extension: '.html',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await utilities.validateTypographyHierarchy(files);

      expect(result.suggestions.length).toBeGreaterThan(0);
      expect(result.suggestions.some(s => s.includes('typography scale'))).toBe(true);
    });
  });

  describe('standardizeLineHeights', () => {
    it('should identify non-standard line heights and suggest corrections', async () => {
      const files: FileInfo[] = [
        {
          path: 'test.css',
          content: `
            .text1 { line-height: 1.33; }
            .text2 { line-height: 1.67; }
            .text3 { line-height: 2.1; }
          `,
          extension: '.css',
          size: 100,
          lastModified: new Date()
        }
      ];

      const fixes = await utilities.standardizeLineHeights(files);

      expect(fixes.length).toBeGreaterThan(0);
      expect(fixes[0]).toMatchObject({
        fixType: 'line-height-standardization'
      });
    });

    it('should not suggest changes for already standard line heights', async () => {
      const files: FileInfo[] = [
        {
          path: 'test.css',
          content: `
            .text1 { line-height: 1.5; }
            .text2 { line-height: 1.25; }
            .text3 { line-height: 2; }
          `,
          extension: '.css',
          size: 100,
          lastModified: new Date()
        }
      ];

      const fixes = await utilities.standardizeLineHeights(files);

      expect(fixes).toHaveLength(0);
    });

    it('should handle React inline styles for line height', async () => {
      const files: FileInfo[] = [
        {
          path: 'Component.tsx',
          content: `
            const styles = {
              text: { lineHeight: '1.8' }
            };
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const fixes = await utilities.standardizeLineHeights(files);

      expect(fixes.length).toBeGreaterThan(0);
      expect(fixes[0].originalValue).toBe('1.8');
    });
  });

  describe('generateDesignTokenSystem', () => {
    it('should extract and optimize typography values from files', () => {
      const files: FileInfo[] = [
        {
          path: 'styles.css',
          content: `
            .title { font-size: 32px; font-weight: 700; }
            .subtitle { font-size: 24px; font-weight: 600; }
            .body { font-size: 16px; font-weight: 400; }
            .small { font-size: 14px; font-weight: 300; }
          `,
          extension: '.css',
          size: 100,
          lastModified: new Date()
        }
      ];

      const tokenSystem = utilities.generateDesignTokenSystem(files);

      expect(tokenSystem.fontSize).toBeDefined();
      expect(tokenSystem.fontWeight).toBeDefined();
      expect(Object.keys(tokenSystem.fontSize).length).toBeGreaterThan(0);
      expect(Object.keys(tokenSystem.fontWeight).length).toBeGreaterThan(0);
    });

    it('should include default typography scale', () => {
      const files: FileInfo[] = [];

      const tokenSystem = utilities.generateDesignTokenSystem(files);

      expect(tokenSystem.fontSize).toHaveProperty('base');
      expect(tokenSystem.fontSize).toHaveProperty('lg');
      expect(tokenSystem.fontWeight).toHaveProperty('normal');
      expect(tokenSystem.fontWeight).toHaveProperty('bold');
    });
  });

  describe('applyTypographyFixes', () => {
    it('should group fixes by file and apply them', async () => {
      const fixes: TypographyFix[] = [
        {
          filePath: 'file1.css',
          lineNumber: 1,
          columnStart: 10,
          columnEnd: 20,
          originalValue: '24px',
          replacementValue: 'var(--font-size-xl)',
          tokenName: 'font-size-xl',
          fixType: 'hardcoded-to-token'
        },
        {
          filePath: 'file1.css',
          lineNumber: 2,
          columnStart: 15,
          columnEnd: 25,
          originalValue: '700',
          replacementValue: 'var(--font-weight-bold)',
          tokenName: 'font-weight-bold',
          fixType: 'hardcoded-to-token'
        },
        {
          filePath: 'file2.css',
          lineNumber: 1,
          columnStart: 5,
          columnEnd: 15,
          originalValue: '1.33',
          replacementValue: '1.25',
          tokenName: 'line-height-snug',
          fixType: 'line-height-standardization'
        }
      ];

      const result = await utilities.applyTypographyFixes(fixes);

      expect(result.success).toBe(true);
      expect(result.appliedFixes).toBe(3);
      expect(result.errors).toHaveLength(0);
    });

    it('should handle errors gracefully', async () => {
      const fixes: TypographyFix[] = [
        {
          filePath: 'nonexistent.css',
          lineNumber: 1,
          columnStart: 0,
          columnEnd: 10,
          originalValue: '24px',
          replacementValue: 'var(--font-size-xl)',
          tokenName: 'font-size-xl',
          fixType: 'hardcoded-to-token'
        }
      ];

      const result = await utilities.applyTypographyFixes(fixes);

      // Should handle gracefully without throwing
      expect(result).toBeDefined();
      expect(typeof result.success).toBe('boolean');
      expect(typeof result.appliedFixes).toBe('number');
      expect(Array.isArray(result.errors)).toBe(true);
    });
  });

  describe('edge cases and error handling', () => {
    it('should handle empty files gracefully', async () => {
      const files: FileInfo[] = [
        {
          path: 'empty.css',
          content: '',
          extension: '.css',
          size: 0,
          lastModified: new Date()
        }
      ];

      const fixes = await utilities.convertHardcodedToTokens(files);
      const validation = await utilities.validateTypographyHierarchy(files);
      const lineHeightFixes = await utilities.standardizeLineHeights(files);

      expect(fixes).toHaveLength(0);
      expect(validation.isValid).toBe(true);
      expect(lineHeightFixes).toHaveLength(0);
    });

    it('should skip comment lines', async () => {
      const files: FileInfo[] = [
        {
          path: 'commented.css',
          content: `
            /* .title { font-size: 24px; } */
            // .subtitle { font-size: 18px; }
            .body { font-size: 16px; }
          `,
          extension: '.css',
          size: 100,
          lastModified: new Date()
        }
      ];

      const fixes = await utilities.convertHardcodedToTokens(files);

      // Should only find the non-commented font-size
      expect(fixes).toHaveLength(1);
      expect(fixes[0].originalValue).toBe('16px');
    });

    it('should handle malformed CSS gracefully', async () => {
      const files: FileInfo[] = [
        {
          path: 'malformed.css',
          content: `
            .broken { font-size: ; }
            .incomplete { font-size
            .valid { font-size: 16px; }
          `,
          extension: '.css',
          size: 100,
          lastModified: new Date()
        }
      ];

      const fixes = await utilities.convertHardcodedToTokens(files);

      // Should only process valid declarations
      expect(fixes).toHaveLength(1);
      expect(fixes[0].originalValue).toBe('16px');
    });

    it('should handle files with mixed content types', async () => {
      const files: FileInfo[] = [
        {
          path: 'mixed.tsx',
          content: `
            import React from 'react';
            
            const styles = {
              title: { fontSize: '24px' }
            };
            
            .css-class { font-size: 18px; }
            
            export default function Component() {
              return <h1 style={{fontSize: '32px'}}>Title</h1>;
            }
          `,
          extension: '.tsx',
          size: 200,
          lastModified: new Date()
        }
      ];

      const fixes = await utilities.convertHardcodedToTokens(files);

      // Should find font sizes in different contexts
      expect(fixes.length).toBeGreaterThan(0);
    });
  });
});