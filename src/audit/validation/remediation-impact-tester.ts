/**
 * Remediation Impact Tester
 * Tests the impact of design system remediation changes
 */

import { ValidationResult, ValidationViolation, Validator, ValidationCategory, ValidationFile } from './types';
import { DesignTokenSystem } from '../design-tokens/types';
import { Severity } from '../types';

export interface RemediationImpactResult {
  functional: FunctionalTestResult;
  accessibility: AccessibilityTestResult;
  performance: PerformanceTestResult;
  overall: ValidationResult;
}

export interface FunctionalTestResult {
  passed: boolean;
  tests: FunctionalTest[];
  summary: TestSummary;
}

export interface AccessibilityTestResult {
  passed: boolean;
  violations: AccessibilityViolation[];
  score: number; // 0-100
  summary: AccessibilitySummary;
}

export interface PerformanceTestResult {
  passed: boolean;
  metrics: PerformanceMetrics;
  comparison: PerformanceComparison;
  recommendations: PerformanceRecommendation[];
}

export interface FunctionalTest {
  id: string;
  name: string;
  component: string;
  scenario: string;
  passed: boolean;
  error?: string;
  duration: number;
}

export interface TestSummary {
  total: number;
  passed: number;
  failed: number;
  skipped: number;
  duration: number;
}

export interface AccessibilityViolation {
  id: string;
  rule: string;
  impact: 'minor' | 'moderate' | 'serious' | 'critical';
  description: string;
  element: string;
  help: string;
  helpUrl: string;
}

export interface AccessibilitySummary {
  totalViolations: number;
  impactBreakdown: Record<string, number>;
  ruleBreakdown: Record<string, number>;
  score: number;
}

export interface PerformanceMetrics {
  bundleSize: {
    before: number;
    after: number;
    difference: number;
    percentChange: number;
  };
  renderTime: {
    before: number;
    after: number;
    difference: number;
    percentChange: number;
  };
  memoryUsage: {
    before: number;
    after: number;
    difference: number;
    percentChange: number;
  };
  cssSize: {
    before: number;
    after: number;
    difference: number;
    percentChange: number;
  };
}

export interface PerformanceComparison {
  improved: string[];
  degraded: string[];
  unchanged: string[];
}

export interface PerformanceRecommendation {
  id: string;
  type: 'bundle' | 'render' | 'memory' | 'css';
  severity: Severity;
  description: string;
  suggestion: string;
  impact: number;
}

export class RemediationImpactTester {
  private functionalTester: FunctionalTester;
  private accessibilityTester: AccessibilityTester;
  private performanceTester: PerformanceTester;

  constructor() {
    this.functionalTester = new FunctionalTester();
    this.accessibilityTester = new AccessibilityTester();
    this.performanceTester = new PerformanceTester();
  }

  async testRemediationImpact(
    beforeFiles: ValidationFile[],
    afterFiles: ValidationFile[],
    tokens?: DesignTokenSystem
  ): Promise<RemediationImpactResult> {
    const [functional, accessibility, performance] = await Promise.all([
      this.functionalTester.test(beforeFiles, afterFiles, tokens),
      this.accessibilityTester.test(beforeFiles, afterFiles, tokens),
      this.performanceTester.test(beforeFiles, afterFiles, tokens)
    ]);

    const overall = this.generateOverallResult(functional, accessibility, performance);

    return {
      functional,
      accessibility,
      performance,
      overall
    };
  }

  private generateOverallResult(
    functional: FunctionalTestResult,
    accessibility: AccessibilityTestResult,
    performance: PerformanceTestResult
  ): ValidationResult {
    const violations: ValidationViolation[] = [];

    // Convert functional test failures to violations
    functional.tests.filter(t => !t.passed).forEach(test => {
      violations.push({
        id: `functional-${test.id}`,
        rule: 'functional-test',
        severity: Severity.HIGH,
        message: `Functional test failed: ${test.name}`,
        location: {
          filePath: test.component,
          lineNumber: 1,
          columnNumber: 1,
          context: test.scenario
        },
        actualValue: test.error || 'Test failed',
        suggestion: 'Review component functionality after remediation',
        autoFixable: false
      });
    });

    // Convert accessibility violations
    accessibility.violations.forEach(violation => {
      violations.push({
        id: `accessibility-${violation.id}`,
        rule: violation.rule,
        severity: this.mapAccessibilitySeverity(violation.impact),
        message: violation.description,
        location: {
          filePath: violation.element,
          lineNumber: 1,
          columnNumber: 1,
          context: violation.element
        },
        actualValue: violation.element,
        suggestion: violation.help,
        autoFixable: false
      });
    });

    // Convert performance issues to violations
    if (performance.metrics.bundleSize.percentChange > 10) {
      violations.push({
        id: 'performance-bundle-size',
        rule: 'performance-impact',
        severity: Severity.MEDIUM,
        message: `Bundle size increased by ${performance.metrics.bundleSize.percentChange.toFixed(1)}%`,
        location: {
          filePath: 'bundle',
          lineNumber: 1,
          columnNumber: 1,
          context: 'Bundle analysis'
        },
        actualValue: `${performance.metrics.bundleSize.after} bytes`,
        suggestion: 'Consider optimizing bundle size after remediation',
        autoFixable: false
      });
    }

    return {
      isValid: violations.length === 0,
      violations,
      summary: {
        totalViolations: violations.length,
        severityBreakdown: violations.reduce((acc, v) => {
          acc[v.severity] = (acc[v.severity] || 0) + 1;
          return acc;
        }, {} as Record<Severity, number>),
        ruleBreakdown: violations.reduce((acc, v) => {
          acc[v.rule] = (acc[v.rule] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
        fileBreakdown: violations.reduce((acc, v) => {
          acc[v.location.filePath] = (acc[v.location.filePath] || 0) + 1;
          return acc;
        }, {} as Record<string, number>)
      },
      executionTime: 0
    };
  }

  private mapAccessibilitySeverity(impact: string): Severity {
    switch (impact) {
      case 'critical':
        return Severity.CRITICAL;
      case 'serious':
        return Severity.HIGH;
      case 'moderate':
        return Severity.MEDIUM;
      case 'minor':
        return Severity.LOW;
      default:
        return Severity.LOW;
    }
  }
}

class FunctionalTester {
  async test(
    beforeFiles: ValidationFile[],
    afterFiles: ValidationFile[],
    tokens?: DesignTokenSystem
  ): Promise<FunctionalTestResult> {
    const startTime = Date.now();
    const tests: FunctionalTest[] = [];

    // Generate functional tests for each component
    const componentFiles = afterFiles.filter(file => file.path.includes('.tsx') || file.path.includes('.jsx'));

    for (const file of componentFiles) {
      const componentTests = await this.generateComponentTests(file);
      tests.push(...componentTests);
    }

    // Run tests
    const results = await Promise.all(tests.map(test => this.runTest(test)));
    const duration = Date.now() - startTime;

    const summary: TestSummary = {
      total: results.length,
      passed: results.filter(r => r.passed).length,
      failed: results.filter(r => !r.passed).length,
      skipped: 0,
      duration
    };

    return {
      passed: summary.failed === 0,
      tests: results,
      summary
    };
  }

  private async generateComponentTests(file: ValidationFile): Promise<FunctionalTest[]> {
    const componentName = this.extractComponentName(file.path);
    const tests: FunctionalTest[] = [];

    // Basic rendering test
    tests.push({
      id: `${componentName}-render`,
      name: `${componentName} renders without crashing`,
      component: file.path,
      scenario: 'Basic rendering',
      passed: false,
      duration: 0
    });

    // Props test
    if (file.content.includes('interface') && file.content.includes('Props')) {
      tests.push({
        id: `${componentName}-props`,
        name: `${componentName} handles props correctly`,
        component: file.path,
        scenario: 'Props handling',
        passed: false,
        duration: 0
      });
    }

    // Event handling test
    if (file.content.includes('onClick') || file.content.includes('onChange')) {
      tests.push({
        id: `${componentName}-events`,
        name: `${componentName} handles events correctly`,
        component: file.path,
        scenario: 'Event handling',
        passed: false,
        duration: 0
      });
    }

    // State test
    if (file.content.includes('useState') || file.content.includes('useReducer')) {
      tests.push({
        id: `${componentName}-state`,
        name: `${componentName} manages state correctly`,
        component: file.path,
        scenario: 'State management',
        passed: false,
        duration: 0
      });
    }

    return tests;
  }

  private async runTest(test: FunctionalTest): Promise<FunctionalTest> {
    const startTime = Date.now();
    
    try {
      // Mock test execution - in a real implementation, you'd use a testing framework
      // like Jest, Vitest, or React Testing Library
      const passed = await this.mockTestExecution(test);
      
      return {
        ...test,
        passed,
        duration: Date.now() - startTime
      };
    } catch (error) {
      return {
        ...test,
        passed: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime
      };
    }
  }

  private async mockTestExecution(test: FunctionalTest): Promise<boolean> {
    // Mock implementation - replace with actual test execution
    await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
    return Math.random() > 0.1; // 90% pass rate for demo
  }

  private extractComponentName(filePath: string): string {
    return filePath.split('/').pop()?.replace(/\.(tsx|jsx)$/, '') || 'Unknown';
  }
}

class AccessibilityTester {
  async test(
    beforeFiles: ValidationFile[],
    afterFiles: ValidationFile[],
    tokens?: DesignTokenSystem
  ): Promise<AccessibilityTestResult> {
    const violations: AccessibilityViolation[] = [];

    // Test each component file for accessibility issues
    for (const file of afterFiles) {
      if (file.path.includes('.tsx') || file.path.includes('.jsx')) {
        const fileViolations = await this.testFileAccessibility(file);
        violations.push(...fileViolations);
      }
    }

    const score = this.calculateAccessibilityScore(violations);
    const summary = this.generateAccessibilitySummary(violations, score);

    return {
      passed: violations.length === 0,
      violations,
      score,
      summary
    };
  }

  private async testFileAccessibility(file: ValidationFile): Promise<AccessibilityViolation[]> {
    const violations: AccessibilityViolation[] = [];
    const lines = file.content.split('\n');

    // Check for missing alt text on images
    lines.forEach((line, index) => {
      if (line.includes('<img') && !line.includes('alt=')) {
        violations.push({
          id: `img-alt-${index}`,
          rule: 'image-alt',
          impact: 'serious',
          description: 'Images must have alternate text',
          element: `${file.path}:${index + 1}`,
          help: 'Add alt attribute to img elements',
          helpUrl: 'https://dequeuniversity.com/rules/axe/4.4/image-alt'
        });
      }
    });

    // Check for missing labels on form inputs
    lines.forEach((line, index) => {
      if (line.includes('<input') && !line.includes('aria-label') && !line.includes('aria-labelledby')) {
        violations.push({
          id: `input-label-${index}`,
          rule: 'label',
          impact: 'critical',
          description: 'Form elements must have labels',
          element: `${file.path}:${index + 1}`,
          help: 'Add aria-label or aria-labelledby to input elements',
          helpUrl: 'https://dequeuniversity.com/rules/axe/4.4/label'
        });
      }
    });

    // Check for missing button text
    lines.forEach((line, index) => {
      if (line.includes('<button') && !line.includes('>') && !line.includes('aria-label')) {
        violations.push({
          id: `button-name-${index}`,
          rule: 'button-name',
          impact: 'critical',
          description: 'Buttons must have discernible text',
          element: `${file.path}:${index + 1}`,
          help: 'Add text content or aria-label to button elements',
          helpUrl: 'https://dequeuniversity.com/rules/axe/4.4/button-name'
        });
      }
    });

    // Check for color contrast issues
    const colorPattern = /#[0-9a-fA-F]{6}/g;
    lines.forEach((line, index) => {
      const colors = line.match(colorPattern);
      if (colors && colors.length >= 2) {
        // Simple heuristic - in reality, you'd calculate actual contrast ratios
        violations.push({
          id: `color-contrast-${index}`,
          rule: 'color-contrast',
          impact: 'serious',
          description: 'Elements must have sufficient color contrast',
          element: `${file.path}:${index + 1}`,
          help: 'Ensure color contrast ratio is at least 4.5:1',
          helpUrl: 'https://dequeuniversity.com/rules/axe/4.4/color-contrast'
        });
      }
    });

    return violations;
  }

  private calculateAccessibilityScore(violations: AccessibilityViolation[]): number {
    if (violations.length === 0) return 100;

    const impactWeights = {
      minor: 1,
      moderate: 2,
      serious: 4,
      critical: 8
    };

    const totalWeight = violations.reduce((sum, v) => sum + impactWeights[v.impact], 0);
    const maxPossibleWeight = violations.length * impactWeights.critical;

    return Math.max(0, 100 - (totalWeight / maxPossibleWeight) * 100);
  }

  private generateAccessibilitySummary(violations: AccessibilityViolation[], score: number): AccessibilitySummary {
    const impactBreakdown = violations.reduce((acc, v) => {
      acc[v.impact] = (acc[v.impact] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const ruleBreakdown = violations.reduce((acc, v) => {
      acc[v.rule] = (acc[v.rule] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalViolations: violations.length,
      impactBreakdown,
      ruleBreakdown,
      score
    };
  }
}

class PerformanceTester {
  async test(
    beforeFiles: ValidationFile[],
    afterFiles: ValidationFile[],
    tokens?: DesignTokenSystem
  ): Promise<PerformanceTestResult> {
    const metrics = await this.measurePerformanceMetrics(beforeFiles, afterFiles);
    const comparison = this.comparePerformance(metrics);
    const recommendations = this.generateRecommendations(metrics);

    return {
      passed: this.isPerformanceAcceptable(metrics),
      metrics,
      comparison,
      recommendations
    };
  }

  private async measurePerformanceMetrics(
    beforeFiles: ValidationFile[],
    afterFiles: ValidationFile[]
  ): Promise<PerformanceMetrics> {
    // Mock performance measurements - in reality, you'd use tools like:
    // - Webpack Bundle Analyzer for bundle size
    // - Lighthouse for performance metrics
    // - Chrome DevTools for memory usage
    // - PostCSS for CSS analysis

    const beforeBundleSize = this.calculateBundleSize(beforeFiles);
    const afterBundleSize = this.calculateBundleSize(afterFiles);

    const beforeRenderTime = this.mockRenderTime(beforeFiles);
    const afterRenderTime = this.mockRenderTime(afterFiles);

    const beforeMemoryUsage = this.mockMemoryUsage(beforeFiles);
    const afterMemoryUsage = this.mockMemoryUsage(afterFiles);

    const beforeCssSize = this.calculateCssSize(beforeFiles);
    const afterCssSize = this.calculateCssSize(afterFiles);

    return {
      bundleSize: {
        before: beforeBundleSize,
        after: afterBundleSize,
        difference: afterBundleSize - beforeBundleSize,
        percentChange: ((afterBundleSize - beforeBundleSize) / beforeBundleSize) * 100
      },
      renderTime: {
        before: beforeRenderTime,
        after: afterRenderTime,
        difference: afterRenderTime - beforeRenderTime,
        percentChange: ((afterRenderTime - beforeRenderTime) / beforeRenderTime) * 100
      },
      memoryUsage: {
        before: beforeMemoryUsage,
        after: afterMemoryUsage,
        difference: afterMemoryUsage - beforeMemoryUsage,
        percentChange: ((afterMemoryUsage - beforeMemoryUsage) / beforeMemoryUsage) * 100
      },
      cssSize: {
        before: beforeCssSize,
        after: afterCssSize,
        difference: afterCssSize - beforeCssSize,
        percentChange: ((afterCssSize - beforeCssSize) / beforeCssSize) * 100
      }
    };
  }

  private calculateBundleSize(files: ValidationFile[]): number {
    return files.reduce((total, file) => total + file.content.length, 0);
  }

  private mockRenderTime(files: ValidationFile[]): number {
    // Mock render time based on component complexity
    const componentCount = files.filter(f => f.path.includes('.tsx') || f.path.includes('.jsx')).length;
    return 50 + componentCount * 10 + Math.random() * 20;
  }

  private mockMemoryUsage(files: ValidationFile[]): number {
    // Mock memory usage
    const totalSize = this.calculateBundleSize(files);
    return totalSize * 0.5 + Math.random() * 1000;
  }

  private calculateCssSize(files: ValidationFile[]): number {
    return files
      .filter(f => f.path.includes('.css') || f.path.includes('.scss'))
      .reduce((total, file) => total + file.content.length, 0);
  }

  private comparePerformance(metrics: PerformanceMetrics): PerformanceComparison {
    const improved: string[] = [];
    const degraded: string[] = [];
    const unchanged: string[] = [];

    const threshold = 5; // 5% threshold for considering a change significant

    if (Math.abs(metrics.bundleSize.percentChange) < threshold) {
      unchanged.push('Bundle Size');
    } else if (metrics.bundleSize.percentChange < 0) {
      improved.push('Bundle Size');
    } else {
      degraded.push('Bundle Size');
    }

    if (Math.abs(metrics.renderTime.percentChange) < threshold) {
      unchanged.push('Render Time');
    } else if (metrics.renderTime.percentChange < 0) {
      improved.push('Render Time');
    } else {
      degraded.push('Render Time');
    }

    if (Math.abs(metrics.memoryUsage.percentChange) < threshold) {
      unchanged.push('Memory Usage');
    } else if (metrics.memoryUsage.percentChange < 0) {
      improved.push('Memory Usage');
    } else {
      degraded.push('Memory Usage');
    }

    if (Math.abs(metrics.cssSize.percentChange) < threshold) {
      unchanged.push('CSS Size');
    } else if (metrics.cssSize.percentChange < 0) {
      improved.push('CSS Size');
    } else {
      degraded.push('CSS Size');
    }

    return { improved, degraded, unchanged };
  }

  private generateRecommendations(metrics: PerformanceMetrics): PerformanceRecommendation[] {
    const recommendations: PerformanceRecommendation[] = [];

    if (metrics.bundleSize.percentChange > 10) {
      recommendations.push({
        id: 'bundle-size-increase',
        type: 'bundle',
        severity: Severity.MEDIUM,
        description: `Bundle size increased by ${metrics.bundleSize.percentChange.toFixed(1)}%`,
        suggestion: 'Consider code splitting or removing unused dependencies',
        impact: metrics.bundleSize.percentChange
      });
    }

    if (metrics.renderTime.percentChange > 15) {
      recommendations.push({
        id: 'render-time-increase',
        type: 'render',
        severity: Severity.HIGH,
        description: `Render time increased by ${metrics.renderTime.percentChange.toFixed(1)}%`,
        suggestion: 'Optimize component rendering with React.memo or useMemo',
        impact: metrics.renderTime.percentChange
      });
    }

    if (metrics.memoryUsage.percentChange > 20) {
      recommendations.push({
        id: 'memory-usage-increase',
        type: 'memory',
        severity: Severity.MEDIUM,
        description: `Memory usage increased by ${metrics.memoryUsage.percentChange.toFixed(1)}%`,
        suggestion: 'Check for memory leaks and optimize data structures',
        impact: metrics.memoryUsage.percentChange
      });
    }

    if (metrics.cssSize.percentChange > 25) {
      recommendations.push({
        id: 'css-size-increase',
        type: 'css',
        severity: Severity.LOW,
        description: `CSS size increased by ${metrics.cssSize.percentChange.toFixed(1)}%`,
        suggestion: 'Consider CSS purging or optimization techniques',
        impact: metrics.cssSize.percentChange
      });
    }

    return recommendations;
  }

  private isPerformanceAcceptable(metrics: PerformanceMetrics): boolean {
    // Define acceptable thresholds
    const thresholds = {
      bundleSize: 15, // 15% increase is acceptable
      renderTime: 20, // 20% increase is acceptable
      memoryUsage: 25, // 25% increase is acceptable
      cssSize: 30 // 30% increase is acceptable
    };

    return (
      metrics.bundleSize.percentChange <= thresholds.bundleSize &&
      metrics.renderTime.percentChange <= thresholds.renderTime &&
      metrics.memoryUsage.percentChange <= thresholds.memoryUsage &&
      metrics.cssSize.percentChange <= thresholds.cssSize
    );
  }
}