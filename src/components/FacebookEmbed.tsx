import React from 'react';
import type { FC } from 'react';

/**
 * LazyFacebookEmbed component displaying 702Hockey Back-to-School Hockey Camp post
 * Uses direct iframe embed for better reliability and specific post display
 */
const LazyFacebookEmbed: FC = () => {
  // Facebook iframe embed for Back-to-School Hockey Camp post
  const facebookIframeSrc = "https://www.facebook.com/plugins/post.php?href=https%3A%2F%2Fwww.facebook.com%2Fpermalink.php%3Fstory_fbid%3Dpfbid0TJPdjQN9pt5rm1WVBNFcZAL5DS8W7VWTVvJwE1RDxTCkc2kR8LRsKBKVTXxWGYnkl%26id%3D100092532255106&show_text=true&width=500";
  
  return (
    <div className="w-full max-w-md mx-auto">
      <div className="overflow-hidden rounded-lg shadow-sm">
        <iframe 
          src={facebookIframeSrc}
          width="500" 
          height="673" 
          style={{
            border: 'none',
            overflow: 'hidden',
            width: '100%',
            maxWidth: '500px'
          }}
          scrolling="no" 
          frameBorder="0" 
          allowFullScreen={true}
          allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share"
        />
      </div>
    </div>
  );
};

export default LazyFacebookEmbed;
