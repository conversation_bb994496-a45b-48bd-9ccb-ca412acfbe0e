import { useContext } from 'react';
import NavigationContext from '@/context/NavigationContext';

/**
 * Custom hook for accessing the navigation context
 * @returns NavigationContext values and methods
 */
const useNavigation = () => {
  const context = useContext(NavigationContext);
  
  if (context === undefined) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  
  return context;
};

export default useNavigation;
