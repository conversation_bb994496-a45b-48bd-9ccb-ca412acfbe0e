// Tests for SpacingAnalyzer

import { SpacingAnalyzer } from '../spacing-analyzer';
import { FileInfo, IssueType, Severity, Category } from '../../types';

describe('SpacingAnalyzer', () => {
  let analyzer: SpacingAnalyzer;

  beforeEach(() => {
    analyzer = new SpacingAnalyzer();
  });

  describe('analyze', () => {
    it('should detect hardcoded pixel spacing values', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
            const Button = () => (
              <button style={{ 
                margin: '16px',
                padding: '8px 12px',
                borderRadius: '4px'
              }}>
                Click me
              </button>
            );
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues.length).toBeGreaterThan(0);
      const spacingIssues = result.issues.filter(issue => 
        issue.type === IssueType.SPACING_INCONSISTENCY &&
        issue.description.includes('Hardcoded spacing value')
      );
      expect(spacingIssues.length).toBeGreaterThan(0);
    });

    it('should detect hardcoded spacing in CSS files', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/styles/button.css',
          content: `
            .button {
              margin: 20px;
              padding: 10px 15px;
              border-radius: 5px;
              width: 200px;
              height: 40px;
            }
          `,
          extension: '.css',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues.length).toBeGreaterThan(0);
      expect(result.issues.some(issue => issue.description.includes('20px'))).toBe(true);
      expect(result.issues.some(issue => issue.description.includes('10px 15px'))).toBe(true);
    });

    it('should detect rem and em spacing values', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Text.tsx',
          content: `
            const Text = () => (
              <p style={{ 
                margin: '1.5rem',
                padding: '0.75em',
                lineHeight: '1.6em'
              }}>
                Hello World
              </p>
            );
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      const remIssues = result.issues.filter(issue => 
        issue.description.includes('1.5rem')
      );
      expect(remIssues.length).toBeGreaterThan(0);
    });

    it('should not flag design tokens', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
            const Button = () => (
              <button style={{ 
                margin: 'var(--spacing-md)',
                padding: 'theme("spacing.4")',
                borderRadius: '$border-radius-sm'
              }}>
                Click me
              </button>
            );
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      const hardcodedIssues = result.issues.filter(issue => 
        issue.description.includes('Hardcoded spacing value')
      );
      expect(hardcodedIssues).toHaveLength(0);
    });

    it('should not flag zero values', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/styles/reset.css',
          content: `
            .reset {
              margin: 0;
              padding: 0px;
              border: 0;
            }
          `,
          extension: '.css',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      const zeroValueIssues = result.issues.filter(issue => 
        issue.description.includes('0') || issue.description.includes('0px')
      );
      expect(zeroValueIssues).toHaveLength(0);
    });

    it('should not flag common utility values', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/styles/utilities.css',
          content: `
            .utilities {
              margin: auto;
              width: 100%;
              height: inherit;
              padding: initial;
            }
          `,
          extension: '.css',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      const utilityIssues = result.issues.filter(issue => 
        issue.description.includes('auto') || 
        issue.description.includes('100%') ||
        issue.description.includes('inherit') ||
        issue.description.includes('initial')
      );
      expect(utilityIssues).toHaveLength(0);
    });

    it('should not flag comments', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
            // TODO: Change margin to 16px
            /* Padding should be 8px */
            const Button = () => <button>Click me</button>;
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues).toHaveLength(0);
    });

    it('should detect Tailwind spacing utilities', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Card.tsx',
          content: `
            const Card = () => (
              <div className="m-4 p-6 gap-2 w-64 h-32">
                Card content
              </div>
            );
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      // Tailwind utilities should be detected but not flagged as issues
      // (they're considered utility classes, not hardcoded values)
      const tailwindIssues = result.issues.filter(issue => 
        issue.description.includes('m-4') || 
        issue.description.includes('p-6')
      );
      expect(tailwindIssues).toHaveLength(0);
    });

    it('should detect frequently used spacing values', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Layout.tsx',
          content: `
            const Layout = () => (
              <div>
                <div style={{ margin: '16px' }}>Section 1</div>
                <div style={{ margin: '16px' }}>Section 2</div>
                <div style={{ margin: '16px' }}>Section 3</div>
                <div style={{ margin: '16px' }}>Section 4</div>
              </div>
            );
          `,
          extension: '.tsx',
          size: 200,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      const frequentUsageIssues = result.issues.filter(issue => 
        issue.description.includes('is used') && 
        issue.description.includes('times') &&
        issue.description.includes('16px')
      );
      expect(frequentUsageIssues.length).toBeGreaterThan(0);
    });

    it('should detect mixed spacing approaches', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Mixed.tsx',
          content: `
            const Mixed = () => (
              <div style={{ 
                margin: 'var(--spacing-md)',
                padding: '16px'
              }}>
                Mixed spacing
              </div>
            );
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      const mixedApproachIssues = result.issues.filter(issue => 
        issue.description.includes('Mixed spacing approaches')
      );
      expect(mixedApproachIssues.length).toBeGreaterThan(0);
    });

    it('should detect mixed spacing units', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/MultiUnit.tsx',
          content: `
            const MultiUnit = () => (
              <div style={{ 
                margin: '16px',
                padding: '1rem',
                gap: '0.5em',
                borderRadius: '4px'
              }}>
                Multiple units
              </div>
            );
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      const mixedUnitsIssues = result.issues.filter(issue => 
        issue.description.includes('Multiple spacing units detected')
      );
      expect(mixedUnitsIssues.length).toBeGreaterThan(0);
    });

    it('should detect layout pattern inconsistencies', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Layout1.tsx',
          content: `
            .layout1 {
              display: flex;
              justify-content: center;
            }
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        },
        {
          path: 'src/components/Layout2.tsx',
          content: `
            .layout2 {
              display: grid;
              grid-template-columns: 1fr 1fr;
            }
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        },
        {
          path: 'src/components/Layout3.tsx',
          content: `
            .layout3 {
              float: left;
              position: absolute;
            }
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      const layoutInconsistencyIssues = result.issues.filter(issue => 
        issue.description.includes('Multiple layout approaches detected')
      );
      expect(layoutInconsistencyIssues.length).toBeGreaterThan(0);
    });

    it('should provide correct severity levels', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
            const Button = () => (
              <button style={{ margin: '16px' }}>
                Click me
              </button>
            );
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      const spacingIssue = result.issues.find(issue => 
        issue.description.includes('16px')
      );
      expect(spacingIssue?.severity).toBe(Severity.MEDIUM);
      expect(spacingIssue?.category).toBe(Category.VISUAL);
    });

    it('should provide actionable suggestions', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
            const Button = () => (
              <button style={{ margin: '16px' }}>
                Click me
              </button>
            );
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      const spacingIssue = result.issues.find(issue => 
        issue.description.includes('16px')
      );
      expect(spacingIssue?.suggestion).toContain('spacing token');
      expect(spacingIssue?.suggestion).toContain('var(--spacing-md)');
    });

    it('should mark simple spacing as auto-fixable', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/styles/button.css',
          content: `
            .button {
              margin: 16px;
            }
          `,
          extension: '.css',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      const spacingIssue = result.issues.find(issue => 
        issue.description.includes('16px')
      );
      expect(spacingIssue?.autoFixable).toBe(true);
    });

    it('should calculate impact assessment', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
            const Button = () => (
              <button style={{ padding: '16px' }}>
                Click me
              </button>
            );
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      const spacingIssue = result.issues.find(issue => 
        issue.description.includes('16px')
      );
      expect(spacingIssue?.impact.userExperience).toBeGreaterThan(0);
      expect(spacingIssue?.impact.maintenanceEffort).toBeGreaterThan(0);
      expect(spacingIssue?.impact.implementationComplexity).toBeGreaterThan(0);
    });

    it('should provide correct file locations', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `const Button = () => (
  <button style={{ margin: '16px' }}>
    Click me
  </button>
);`,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      const spacingIssue = result.issues.find(issue => 
        issue.description.includes('16px')
      );
      expect(spacingIssue?.location.filePath).toBe('src/components/Button.tsx');
      expect(spacingIssue?.location.lineNumber).toBe(2);
      expect(spacingIssue?.location.context).toContain('16px');
    });

    it('should handle complex inline styles', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Complex.tsx',
          content: `
            const Complex = () => (
              <div style={{
                marginTop: '20px',
                marginBottom: '10px',
                paddingLeft: '15px',
                paddingRight: '15px'
              }}>
                Complex spacing
              </div>
            );
          `,
          extension: '.tsx',
          size: 200,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      const spacingIssues = result.issues.filter(issue => 
        issue.type === IssueType.SPACING_INCONSISTENCY &&
        issue.description.includes('Hardcoded spacing value')
      );
      expect(spacingIssues.length).toBeGreaterThan(2);
    });

    it('should handle shorthand properties', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/styles/shorthand.css',
          content: `
            .shorthand {
              margin: 10px 20px;
              padding: 5px 10px 15px 20px;
              border-radius: 4px 8px;
            }
          `,
          extension: '.css',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      const spacingIssues = result.issues.filter(issue => 
        issue.type === IssueType.SPACING_INCONSISTENCY
      );
      expect(spacingIssues.length).toBeGreaterThan(0);
    });
  });

  describe('getSeverity', () => {
    it('should return appropriate severity for different issue types', () => {
      const highSeverityIssue = {
        id: 'test',
        type: IssueType.SPACING_INCONSISTENCY,
        category: Category.VISUAL,
        description: 'frequently used spacing value',
        location: { filePath: '', lineNumber: 1, columnNumber: 1, context: '' },
        suggestion: '',
        autoFixable: false,
        impact: { userExperience: 5, maintenanceEffort: 5, implementationComplexity: 5 }
      };

      expect(analyzer.getSeverity(highSeverityIssue)).toBe(Severity.HIGH);

      const mediumSeverityIssue = {
        ...highSeverityIssue,
        description: 'hardcoded spacing value'
      };

      expect(analyzer.getSeverity(mediumSeverityIssue)).toBe(Severity.MEDIUM);

      const mixedApproachIssue = {
        ...highSeverityIssue,
        description: 'Mixed spacing approaches detected'
      };

      expect(analyzer.getSeverity(mixedApproachIssue)).toBe(Severity.MEDIUM);
    });
  });
});