// Centralized coaches data for use across the website
// This file serves as the single source of truth for coach information

import john<PERSON>iemerImg from "@/assets/john-siemer.jpg";
import coleDiamondImg from "@/assets/cole-diamond.jpg";
import ivanMacherasImg from "@/assets/ivan-macheras.jpg";
import chrisCobbImg from "@/assets/chris-cobb.jpg";
import zachWicksonImg from "@/assets/zach-wickson.jpeg";
import teddyStanowskiImg from "@/assets/teddy-stanowski.png";

export interface Coach {
  id: string;
  name: string;
  title: string;
  bio: string;
  image: string;
  type: 'player' | 'goalie';  // Used to categorize coaches
  specialties?: string[];
  featured?: boolean;         // Whether to feature on homepage
  order?: number;             // For controlling display order
}

// Main coaches data array
// When adding, editing, or removing coaches, only modify this array
const coaches: Coach[] = [
  // Player Skills Coaches
  {
    id: "john-siemer",
    name: "<PERSON>",
    title: "Player Skills Coach",
    bio: "Experienced hockey skills coach specializing in player development with a focus on skill refinement, hockey IQ, and fostering a competitive mindset. Specializes in shooting, stickhandling, and offensive techniques.",
    image: john<PERSON>iemerImg,
    type: "player",
    specialties: ["Shooting", "Stickhandling", "Offensive Techniques"],
    featured: true,
    order: 1
  },
  {
    id: "cole-diamond",
    name: "Cole Diamond",
    title: "Player Skills Coach",
    bio: "Specialized in advanced hockey techniques with a comprehensive focus on enhancing game awareness, strategic positioning, and competitive skill development. Expertise includes the refinement of defensive skills and effective gap control.",
    image: coleDiamondImg,
    type: "player",
    specialties: ["Defensive Skills", "Gap Control"],
    featured: true,
    order: 2
  },
  {
    id: "ivan-macheras",
    name: "Ivan Macheras",
    title: "Player Skills Coach",
    bio: "Expert skills training and tactical awareness focused on holistic player development by enhancing technical abilities and game strategy and situational awareness.",
    image: ivanMacherasImg,
    type: "player",
    specialties: ["Technical Abilities", "Tactical Awareness"],
    featured: true,
    order: 3
  },
  
  // Goalie Coaches
  {
    id: "chris-cobb",
    name: "Chris Cobb",
    title: "Goalie Coach",
    bio: "Specialized goaltending coach with expertise in modern goaltending techniques, positioning, and mental preparation for goalies of all levels.",
    image: chrisCobbImg,
    type: "goalie",
    specialties: ["Goaltending", "Mental Preparation"],
    featured: true,
    order: 1
  },
  {
    id: "zach-wickson",
    name: "Zach Wickson",
    title: "Goalie Coach",
    bio: "Dedicated to developing elite goaltenders through personalized training programs focusing on technical skills and mental performance.",
    image: zachWicksonImg,
    type: "goalie",
    specialties: ["Goaltending", "Mental Performance"],
    featured: false,
    order: 2
  },
  {
    id: "teddy-stanowski",
    name: "Teddy Stanowski",
    title: "Goalie Coach",
    bio: "Specialized in modern goaltending techniques with a focus on positioning, save selection, and developing a strong mental approach to the position.",
    image: teddyStanowskiImg,
    type: "goalie",
    specialties: ["Goaltending", "Save Selection"],
    featured: false,
    order: 3
  }
];

// Helper functions for accessing coach data
export const getAllCoaches = (): Coach[] => coaches;

export const getPlayerCoaches = (): Coach[] => 
  coaches.filter(coach => coach.type === 'player');

export const getGoalieCoaches = (): Coach[] => 
  coaches.filter(coach => coach.type === 'goalie');

export const getFeaturedCoaches = (): Coach[] => 
  coaches.filter(coach => coach.featured).sort((a, b) => (a.order || 99) - (b.order || 99));

export const getCoachById = (id: string): Coach | undefined => 
  coaches.find(coach => coach.id === id);

export default coaches;
