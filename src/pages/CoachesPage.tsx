import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { CalendarDays } from "lucide-react";
import { useNavigate } from "react-router-dom";
import coaches, { Coach, getPlayerCoaches, getGoalieCoaches } from "@/data/coaches";
import SEO from "@/components/SEO";
import { Breadcrumbs } from "@/components/ui/Breadcrumbs";
import { useBreadcrumbs, useShouldShowBreadcrumbs } from "@/hooks/useBreadcrumbs";

// Using the Coach interface from our centralized data file

interface Session {
  id: string;
  title: string;
  time: string;
  duration: string;
  spots: number;
}

const CoachesPage = () => {
  const navigate = useNavigate();
  const [selectedCoach, setSelectedCoach] = useState<Coach | null>(null);
  const breadcrumbs = useBreadcrumbs();
  const shouldShowBreadcrumbs = useShouldShowBreadcrumbs();

  
  // Using coaches from our centralized data file
  // The coaches are already imported at the top of the file

  // Mock training sessions for each coach
  const getCoachSessions = (coachId: string): Session[] => {
    // In a real app, this would come from an API call
    return [
      {
        id: `${coachId}-1`,
        title: "Individual Skills Training",
        time: "Mon, Wed, Fri - 4:00 PM",
        duration: "60 minutes",
        spots: 3
      },
      {
        id: `${coachId}-2`,
        title: "Small Group Session",
        time: "Tue, Thu - 5:30 PM",
        duration: "90 minutes",
        spots: 2
      },
      {
        id: `${coachId}-3`,
        title: "Position-Specific Training",
        time: "Sat - 10:00 AM",
        duration: "120 minutes",
        spots: 5
      }
    ];
  };

  const handleCoachSelect = (coach: Coach) => {
    setSelectedCoach(coach);
    // trackEvent("Coach Interaction", "Coach Selected", coach.name); // Removed analytics
  };

  // Close panel and deselect coach
  const handleClosePanel = () => {
    setSelectedCoach(null);
  };

  const handleBookSession = (session: Session) => {
    if (selectedCoach) {
      // trackEvent("Session Booking", "Session Selected", `${selectedCoach.name} - ${session.title}`); // Removed analytics
      navigate("/scheduling"); // Changed from window.open
    }
  };

  return (
    <div className="flex flex-col min-h-screen">
      <SEO
        title="Expert Hockey Coaches - ELEV802 | 702HOCKEY - Las Vegas"
        description="Meet our team of professional hockey coaches at ELEV802's premier training facility in Las Vegas. Book one-on-one sessions with NHL-experienced coaches."
        keywords="ELEV802, hockey coaches Las Vegas, professional hockey training, 702Hockey coaches, hockey skills development, NHL coaches"
        canonical="/coaches"
        image="/images/coaches-team.jpg"
      />
      <Header />
      {shouldShowBreadcrumbs && (
        <Breadcrumbs
          items={breadcrumbs}
          showHomeIcon={true}
          separator="chevron"
        />
      )}
      <main className="flex-grow bg-elev-background-muted">
        <section className="container mx-auto py-8 px-4 max-w-7xl">
          <h1 className="text-3xl font-bold text-elev-navy mb-2">Our Expert Coaches</h1>
          
          <div className="relative">
            {/* Coaches List */}
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-elev-navy mb-8 text-center">Player Skills Coaches</h2>
              <div className={`flex flex-wrap justify-center gap-6 mb-16 transition-all duration-300 ${selectedCoach ? 'opacity-0 pointer-events-none' : ''}`}>
                {getPlayerCoaches().map((coach) => (
                <div 
                  key={coach.id}
                  className={`w-full max-w-[240px] cursor-pointer transition-all duration-300 ${
                    selectedCoach?.id === coach.id 
                      ? 'ring-2 ring-elev-blue scale-105' 
                      : 'hover:scale-105'
                  }`}
                  onClick={() => handleCoachSelect(coach)}
                >
                  <Card className="overflow-hidden h-full w-full shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                    <div className="relative pt-[100%]">
                      <img 
                        src={coach.image} 
                        alt={coach.name}
                        className="absolute top-0 left-0 w-full h-full object-cover object-top" 
                      />
                    </div>
                    <CardContent className="p-3 text-center">
                      <h3 className="font-semibold text-elev-navy mb-1 text-base">{coach.name}</h3>
                      <p className="text-sm text-elev-text-secondary">{coach.title}</p>
                    </CardContent>
                  </Card>
                </div>
                ))}
              </div>
              
              <hr className="my-8 border-elev-background-accent" />

              <h2 className="text-2xl font-bold text-elev-navy mb-8 text-center">Goalie Coaches</h2>
              <div className={`flex flex-wrap justify-center gap-6 mb-16 transition-all duration-300 ${selectedCoach ? 'opacity-0 pointer-events-none' : ''}`}>
                {getGoalieCoaches().map((coach) => (
                  <div 
                    key={coach.id}
                    className={`w-full max-w-[240px] cursor-pointer transition-all duration-300 ${
                      selectedCoach?.id === coach.id 
                        ? 'ring-2 ring-elev-blue scale-105' 
                        : 'hover:scale-105'
                    }`}
                    onClick={() => handleCoachSelect(coach)}
                  >
                    <Card className="overflow-hidden h-full w-full shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                      <div className="relative pt-[100%]">
                        <img 
                          src={coach.image} 
                          alt={coach.name}
                          className="absolute top-0 left-0 w-full h-full object-cover object-top" 
                        />
                      </div>
                      <CardContent className="p-3 text-center">
                        <h3 className="font-medium text-elev-navy">{coach.name}</h3>
                        <p className="text-sm text-elev-text-muted">{coach.title}</p>
                      </CardContent>
                    </Card>
                  </div>
                ))}
              </div>
            </div>

            {/* Schedule Panel */}
            <div className={`fixed bottom-0 left-0 w-full bg-white rounded-t-2xl shadow-lg p-6 transition-transform duration-300 z-40
              ${selectedCoach ? 'translate-y-0' : 'translate-y-full'}
            `}>
              <div className="flex flex-col xs:flex-row xs:justify-between xs:items-center gap-2 mb-4">
                <h2 className="text-2xl font-bold text-elev-navy">{selectedCoach?.name}'s Schedule</h2>
                <div className="flex flex-col xs:flex-row gap-2 w-full xs:w-auto">
                  <Button variant="ghost" className="w-full xs:w-auto" onClick={handleClosePanel}>Close</Button>
                </div>
              </div>
              {selectedCoach && (
                <div>
                  <div className="flex flex-col md:flex-row items-center md:items-start gap-6 mb-6">
                    <Avatar className="w-24 h-24 border-2 border-elev-background-accent">
                      <AvatarImage src={selectedCoach.image} alt={selectedCoach.name} />
                      <AvatarFallback>{selectedCoach.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                    </Avatar>
                    <div>
                      <h2 className="text-2xl font-bold text-elev-navy mb-2">{selectedCoach.name}</h2>
                      <p className="text-elev-blue font-medium mb-2">{selectedCoach.title}</p>
                      <p className="text-elev-text-secondary mb-4">{selectedCoach.bio}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>

          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default CoachesPage;
