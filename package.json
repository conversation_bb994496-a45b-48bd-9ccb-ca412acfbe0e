{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-aspect-ratio": "^1.1.6", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@tanstack/react-query": "^5.76.1", "@types/react-helmet": "^6.1.11", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-ga4": "^2.1.0", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "react-social-media-embed": "^2.5.18", "recharts": "^2.15.3", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "vaul": "^0.9.3", "zod": "^3.24.4"}, "devDependencies": {"@eslint/js": "^9.27.0", "@stagewise-plugins/react": "^0.6.1", "@stagewise/toolbar-react": "^0.6.1", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^22.15.18", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "caniuse-lite": "^1.0.30001718", "depcheck": "^1.4.7", "eslint": "^9.28.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^15.9.0", "jsdom": "^26.1.0", "postcss": "^8.4.47", "rollup-plugin-visualizer": "^5.14.0", "tailwindcss": "^3.4.11", "tailwindcss-animate": "^1.0.7", "terser": "^5.43.1", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1", "vite": "^6.3.5", "vitest": "^3.2.4"}}