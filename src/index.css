
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-montserrat font-bold;
  }

  .section-padding {
    @apply py-12 md:py-16 lg:py-20;
  }

  .container {
    @apply px-4 md:px-8 lg:px-6;
  }
}

@layer components {
  /* Responsive Container Classes */
  .container-responsive {
    @apply w-full mx-auto px-4 sm:px-6 md:px-8 lg:px-6 xl:px-8;
    max-width: 1280px;
  }

  .container-fluid {
    @apply w-full px-4 sm:px-6 md:px-8;
  }

  .container-narrow {
    @apply w-full mx-auto px-4 sm:px-6 md:px-8;
    max-width: 768px;
  }

  .container-wide {
    @apply w-full mx-auto px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16;
    max-width: 1440px;
  }

  /* Responsive Section Padding */
  .section-padding-sm {
    @apply py-8 md:py-12 lg:py-16;
  }

  .section-padding-md {
    @apply py-12 md:py-16 lg:py-20;
  }

  .section-padding-lg {
    @apply py-16 md:py-20 lg:py-24;
  }

  .section-padding-xl {
    @apply py-20 md:py-24 lg:py-32;
  }

  /* Responsive Typography Utilities */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl md:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl md:text-3xl;
  }

  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl;
  }

  .text-responsive-3xl {
    @apply text-3xl sm:text-4xl md:text-5xl lg:text-6xl;
  }

  /* Responsive Heading Utilities */
  .heading-responsive-h1 {
    @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight;
  }

  .heading-responsive-h2 {
    @apply text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold leading-tight;
  }

  .heading-responsive-h3 {
    @apply text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-bold leading-tight;
  }

  .heading-responsive-h4 {
    @apply text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl font-bold leading-tight;
  }

  /* Responsive Spacing Utilities */
  .space-responsive-xs {
    @apply space-y-2 sm:space-y-3 md:space-y-4;
  }

  .space-responsive-sm {
    @apply space-y-4 sm:space-y-6 md:space-y-8;
  }

  .space-responsive-md {
    @apply space-y-6 sm:space-y-8 md:space-y-12;
  }

  .space-responsive-lg {
    @apply space-y-8 sm:space-y-12 md:space-y-16;
  }

  .space-responsive-xl {
    @apply space-y-12 sm:space-y-16 md:space-y-20;
  }

  /* Responsive Margin Utilities */
  .margin-responsive-xs {
    @apply m-2 sm:m-3 md:m-4;
  }

  .margin-responsive-sm {
    @apply m-4 sm:m-6 md:m-8;
  }

  .margin-responsive-md {
    @apply m-6 sm:m-8 md:m-12;
  }

  .margin-responsive-lg {
    @apply m-8 sm:m-12 md:m-16;
  }

  .margin-responsive-xl {
    @apply m-12 sm:m-16 md:m-20;
  }

  /* Responsive Padding Utilities */
  .padding-responsive-xs {
    @apply p-2 sm:p-3 md:p-4;
  }

  .padding-responsive-sm {
    @apply p-4 sm:p-6 md:p-8;
  }

  .padding-responsive-md {
    @apply p-6 sm:p-8 md:p-12;
  }

  .padding-responsive-lg {
    @apply p-8 sm:p-12 md:p-16;
  }

  .padding-responsive-xl {
    @apply p-12 sm:p-16 md:p-20;
  }

  /* Responsive Gap Utilities */
  .gap-responsive-xs {
    @apply gap-2 sm:gap-3 md:gap-4;
  }

  .gap-responsive-sm {
    @apply gap-4 sm:gap-6 md:gap-8;
  }

  .gap-responsive-md {
    @apply gap-6 sm:gap-8 md:gap-12;
  }

  .gap-responsive-lg {
    @apply gap-8 sm:gap-12 md:gap-16;
  }

  .gap-responsive-xl {
    @apply gap-12 sm:gap-16 md:gap-20;
  }

  /* Responsive Grid Utilities */
  .grid-responsive-1-2-3 {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3;
  }

  .grid-responsive-1-2-4 {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4;
  }

  .grid-responsive-1-3-4 {
    @apply grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-4;
  }

  .grid-responsive-2-4-6 {
    @apply grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6;
  }

  /* Responsive Flex Utilities */
  .flex-responsive-col-row {
    @apply flex flex-col sm:flex-row;
  }

  .flex-responsive-col-row-md {
    @apply flex flex-col md:flex-row;
  }

  .flex-responsive-wrap {
    @apply flex flex-wrap gap-4 sm:gap-6 md:gap-8;
  }

  /* Touch Target Utilities */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  .touch-target-lg {
    @apply min-h-[48px] min-w-[48px];
  }

  /* Responsive Button Utilities */
  .btn-responsive-sm {
    @apply px-3 py-2 text-sm sm:px-4 sm:py-2 sm:text-base;
  }

  .btn-responsive-md {
    @apply px-4 py-2 text-base sm:px-6 sm:py-3 sm:text-lg;
  }

  .btn-responsive-lg {
    @apply px-6 py-3 text-lg sm:px-8 sm:py-4 sm:text-xl;
  }

  /* Responsive Card Utilities */
  .card-responsive {
    @apply p-4 sm:p-6 md:p-8 rounded-lg shadow-sm hover:shadow-md transition-shadow;
  }

  .card-responsive-compact {
    @apply p-3 sm:p-4 md:p-6 rounded-md shadow-sm hover:shadow-md transition-shadow;
  }

  /* Enhanced Responsive Image Utilities */
  .img-responsive {
    @apply w-full h-auto object-cover;
  }

  .img-responsive-square {
    @apply w-full aspect-square object-cover;
  }

  .img-responsive-video {
    @apply w-full aspect-video object-cover;
  }

  .img-responsive-portrait {
    @apply w-full aspect-[3/4] object-cover;
  }

  .img-responsive-landscape {
    @apply w-full aspect-[4/3] object-cover;
  }

  /* Image loading states */
  .img-loading {
    @apply bg-gray-200 animate-pulse;
  }

  .img-error {
    @apply bg-gray-100 flex items-center justify-center text-gray-400;
  }

  /* Image hover effects */
  .img-hover-scale {
    @apply transition-transform duration-300 hover:scale-105;
  }

  .img-hover-zoom {
    @apply transition-transform duration-300 hover:scale-110;
  }

  /* Responsive image containers */
  .img-container-responsive {
    @apply relative overflow-hidden rounded-lg;
  }

  .img-container-card {
    @apply relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300;
  }

  /* Mobile-First Layout Utilities */
  .mobile-stack {
    @apply flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4;
  }

  .mobile-center {
    @apply text-center sm:text-left;
  }

  .mobile-full-width {
    @apply w-full sm:w-auto;
  }

  /* Responsive Visibility Utilities */
  .mobile-only {
    @apply block sm:hidden;
  }

  .tablet-up {
    @apply hidden sm:block;
  }

  .desktop-only {
    @apply hidden lg:block;
  }

  .mobile-tablet {
    @apply block lg:hidden;
  }
}

.reveal {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.reveal.active {
  opacity: 1;
  transform: translateY(0);
}

.text-gradient {
  @apply bg-gradient-to-r from-elev-navy to-elev-blue bg-clip-text text-transparent;
}

/* Prevent horizontal scrolling on mobile */
@layer utilities {
  .prevent-scroll {
    overflow-x: hidden;
    max-width: 100vw;
  }
  
  /* Ensure mobile menu doesn't cause horizontal scroll */
  .mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    overflow-x: hidden;
  }
}
