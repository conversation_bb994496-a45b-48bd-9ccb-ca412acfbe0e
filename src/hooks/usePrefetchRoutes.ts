import { useEffect } from 'react';
import { prefetchData } from '@/lib/queryClient';

// Route configuration type that accepts both mutable and readonly string arrays
type RouteConfig = {
  path: string;
  prefetchKey: string[] | readonly string[];
  prefetchQuery: () => Promise<unknown>;
};

type RouteElement = {
  element: Element;
  handler: () => Promise<void>;
};

export function usePrefetchRoutes(routes: RouteConfig[]): void {
  useEffect(() => {
    // Function to handle mouse enter events for prefetching
    const handleMouseEnter = async (route: RouteConfig) => {
      try {
        await prefetchData(route.prefetchKey, route.prefetchQuery);
      } catch (error) {
        console.error(`Failed to prefetch data for ${route.path}:`, error);
      }
    };

    // Add event listeners for each route with prefetching
    const elements = routes
      .map(route => {
        const element = document.querySelector(`[href="${route.path}"]`);
        if (element) {
          const handler = () => handleMouseEnter(route);
          element.addEventListener('mouseenter', handler, { once: true });
          return { element, handler };
        }
        return null;
      })
      .filter((item): item is RouteElement => item !== null);

    // Cleanup event listeners
    return () => {
      elements.forEach(({ element, handler }) => {
        element.removeEventListener('mouseenter', handler);
      });
    };
  }, [routes]);
};

// Example usage in a component:
/*
const routes = [
  { 
    path: '/coaches',
    prefetchKey: ['coaches'],
    prefetchQuery: () => fetch('/api/coaches').then(res => res.json())
  },
  // Add more routes as needed
];

usePrefetchRoutes(routes);
*/
