import { Helmet } from "react-helmet-async";

interface SEOProps {
  title: string;
  description: string;
  canonical?: string;
  keywords?: string;
  image?: string;
  type?: string;
  children?: React.ReactNode;
  language?: string;
  robots?: string;
  published?: string;
  modified?: string;
  author?: string;
  alternateLanguages?: { lang: string; url: string }[];
}

/**
 * Reusable SEO component for consistent metadata across pages
 * Enhanced with additional SEO-focused meta tags and structured data support
 */
export function SEO({
  title,
  description,
  canonical = "/",
  keywords = "ELEV802, hockey training, Las Vegas hockey, ice hockey coaching, hockey skills development, 702Hockey, youth hockey training",
  image = "/images/facility.jpg",
  type = "website",
  children,
  language = "en",
  robots = "index, follow",
  published,
  modified,
  author = "ELEV802 | 702HOCKEY",
  alternateLanguages
}: SEOProps) {
  // Base URL for the website
  const baseUrl = "https://702hockey.com";
  const fullCanonical = canonical.startsWith("http") ? canonical : `${baseUrl}${canonical}`;
  const fullImage = image.startsWith("http") ? image : `${baseUrl}${image}`;
  
  return (
    <Helmet>
      {/* Basic Metadata */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="robots" content={robots} />
      <meta name="language" content={language} />
      <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
      <meta name="author" content={author} />
      {published && <meta name="article:published_time" content={published} />}
      {modified && <meta name="article:modified_time" content={modified} />}
      <link rel="canonical" href={fullCanonical} />
      
      {/* Alternate languages for international SEO */}
      {alternateLanguages?.map(({ lang, url }) => (
        <link rel="alternate" hrefLang={lang} href={url} key={lang} />
      ))}
      
      {/* OpenGraph Metadata for better social sharing */}
      <meta property="og:site_name" content="ELEV802 | 702HOCKEY" />
      <meta property="og:locale" content="en_US" />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={type} />
      <meta property="og:url" content={fullCanonical} />
      <meta property="og:image" content={fullImage} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      
      {/* Twitter Card Metadata */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content="@elev802" />
      <meta name="twitter:creator" content="@elev802" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullImage} />
      
      {children}
    </Helmet>
  );
}

export default SEO;
