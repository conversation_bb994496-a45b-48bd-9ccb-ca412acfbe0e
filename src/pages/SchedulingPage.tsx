import React from 'react';
import Header from '../components/Header';
import Footer from '../components/Footer';
import EmbeddedSchedulingPage from '../components/EmbeddedSchedulingPage';
import { Breadcrumbs } from '../components/ui/Breadcrumbs';
import { useBreadcrumbs, useShouldShowBreadcrumbs } from '../hooks/useBreadcrumbs';

const SchedulingPage: React.FC = () => {
  const breadcrumbs = useBreadcrumbs();
  const shouldShowBreadcrumbs = useShouldShowBreadcrumbs();
  
  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      {shouldShowBreadcrumbs && (
        <Breadcrumbs
          items={breadcrumbs}
          showHomeIcon={true}
          separator="chevron"
        />
      )}
      <main className="flex-grow">
        <EmbeddedSchedulingPage />
      </main>
      <Footer />
    </div>
  );
};

export default SchedulingPage;
