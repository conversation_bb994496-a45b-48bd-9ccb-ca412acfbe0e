import { describe, it, expect, beforeEach } from 'vitest';
import { ContentStandardizationTools } from '../content-standardization';
import { IssueType, Severity, Category } from '../../types';

describe('ContentStandardizationTools', () => {
  let tools: ContentStandardizationTools;

  beforeEach(() => {
    tools = new ContentStandardizationTools();
  });

  describe('generateContentRevisions', () => {
    it('should suggest tone improvements for error messages', () => {
      const text = "This is terrible! Your input is stupid.";
      const revisions = tools.generateContentRevisions(text, 'error_message');
      
      expect(revisions.length).toBeGreaterThan(0);
      const toneRevision = revisions.find(r => r.type === 'tone');
      expect(toneRevision).toBeDefined();
      expect(toneRevision?.revised).not.toContain('terrible');
      expect(toneRevision?.revised).not.toContain('stupid');
    });

    it('should suggest formality improvements for formal content', () => {
      const text = "Hey, gonna need you to fix this.";
      const revisions = tools.generateContentRevisions(text, 'help_text');
      
      expect(revisions.length).toBeGreaterThan(0);
      const toneRevision = revisions.find(r => r.type === 'tone');
      expect(toneRevision).toBeDefined();
      expect(toneRevision?.revised).not.toContain('Hey');
      expect(toneRevision?.revised).not.toContain('gonna');
    });

    it('should suggest grammar improvements', () => {
      const text = "i need help with this...";
      const revisions = tools.generateContentRevisions(text, 'help_text');
      
      expect(revisions.length).toBeGreaterThan(0);
      const grammarRevision = revisions.find(r => r.type === 'grammar');
      expect(grammarRevision).toBeDefined();
      expect(grammarRevision?.revised).toContain('I need');
    });

    it('should sort revisions by confidence', () => {
      const text = "i think this is terrible and gonna break.";
      const revisions = tools.generateContentRevisions(text, 'error_message');
      
      expect(revisions.length).toBeGreaterThan(1);
      // Should be sorted by confidence (descending)
      for (let i = 1; i < revisions.length; i++) {
        expect(revisions[i-1].confidence).toBeGreaterThanOrEqual(revisions[i].confidence);
      }
    });
  });

  describe('standardizeTerminology', () => {
    it('should identify terminology that needs standardization', () => {
      const text = "Please login to your account and check your e-mail.";
      const standardizations = tools.standardizeTerminology(text);
      
      expect(standardizations.length).toBeGreaterThan(0);
      
      const loginStd = standardizations.find(s => s.term === 'login');
      expect(loginStd).toBeDefined();
      expect(loginStd?.standardTerm).toBe('log in');
      
      const emailStd = standardizations.find(s => s.term === 'e-mail');
      expect(emailStd).toBeDefined();
      expect(emailStd?.standardTerm).toBe('email address');
    });

    it('should categorize terminology correctly', () => {
      const text = "Click the button to purchase a subscription.";
      const standardizations = tools.standardizeTerminology(text);
      
      const buttonStd = standardizations.find(s => s.term === 'button');
      if (buttonStd) {
        expect(buttonStd.category).toBe('ui');
      }
      
      const purchaseStd = standardizations.find(s => s.term === 'purchase');
      if (purchaseStd) {
        expect(purchaseStd.category).toBe('business');
      }
    });

    it('should handle case-insensitive matching', () => {
      const text = "LOGIN to your account";
      const standardizations = tools.standardizeTerminology(text);
      
      const loginStd = standardizations.find(s => s.term.toLowerCase() === 'login');
      expect(loginStd).toBeDefined();
    });
  });

  describe('enforceMessagingPatterns', () => {
    it('should suggest pattern compliance for error messages', () => {
      const text = "Something went wrong with your request.";
      const revisions = tools.enforceMessagingPatterns(text, 'error');
      
      expect(revisions.length).toBeGreaterThan(0);
      expect(revisions[0].revised).toContain('Error:');
      expect(revisions[0].type).toBe('messaging');
    });

    it('should suggest pattern compliance for success messages', () => {
      const text = "Your account has been created.";
      const revisions = tools.enforceMessagingPatterns(text, 'success');
      
      expect(revisions.length).toBeGreaterThan(0);
      expect(revisions[0].revised).toContain('successful');
      expect(revisions[0].type).toBe('messaging');
    });

    it('should not suggest changes for compliant messages', () => {
      const text = "Error: Invalid email address. Please check and try again.";
      const revisions = tools.enforceMessagingPatterns(text, 'error');
      
      expect(revisions.length).toBe(0);
    });
  });

  describe('applyAutomaticFixes', () => {
    it('should apply terminology standardization', () => {
      const text = "Please login to check your e-mail.";
      const fixed = tools.applyAutomaticFixes(text, 'help_text');
      
      expect(fixed).toContain('log in');
      expect(fixed).toContain('email address');
    });

    it('should apply grammar fixes', () => {
      const text = "i need help...";
      const fixed = tools.applyAutomaticFixes(text, 'help_text');
      
      expect(fixed).toContain('I need');
      expect(fixed).not.toContain('...');
    });

    it('should apply tone adjustments for error messages', () => {
      const text = "Error: Invalid input. Try again.";
      const fixed = tools.applyAutomaticFixes(text, 'error_message');
      
      expect(fixed).toContain('Please try');
    });

    it('should preserve original text when no fixes needed', () => {
      const text = "This is a well-formatted message.";
      const fixed = tools.applyAutomaticFixes(text, 'help_text');
      
      expect(fixed).toBe(text);
    });
  });

  describe('validateContent', () => {
    it('should identify terminology issues', () => {
      const text = "Please login to your account.";
      const issues = tools.validateContent(text, 'help_text');
      
      const terminologyIssues = issues.filter(issue => 
        issue.description.includes('Non-standard terminology')
      );
      expect(terminologyIssues.length).toBeGreaterThan(0);
      expect(terminologyIssues[0].autoFixable).toBe(true);
    });

    it('should identify tone issues', () => {
      const text = "This is terrible and stupid.";
      const issues = tools.validateContent(text, 'error_message');
      
      const toneIssues = issues.filter(issue => 
        issue.description.includes('Tone violation')
      );
      expect(toneIssues.length).toBeGreaterThan(0);
      expect(toneIssues[0].severity).toBe(Severity.MEDIUM);
    });

    it('should identify messaging pattern issues', () => {
      const text = "Something went wrong.";
      const issues = tools.validateContent(text, 'error_message');
      
      const messagingIssues = issues.filter(issue => 
        issue.description.includes('standard error pattern')
      );
      expect(messagingIssues.length).toBeGreaterThan(0);
      expect(messagingIssues[0].category).toBe(Category.CONTENT);
    });

    it('should return no issues for compliant content', () => {
      const text = "Please enter your email address to continue.";
      const issues = tools.validateContent(text, 'help_text');
      
      // Should have minimal issues for well-formatted content
      // May have messaging pattern issues but terminology should be clean
      expect(issues.length).toBeLessThanOrEqual(2);
      
      // Check that there are no high-severity issues
      const highSeverityIssues = issues.filter(issue => issue.severity === Severity.HIGH);
      expect(highSeverityIssues.length).toBe(0);
    });

    it('should assign appropriate severity levels', () => {
      const text = "Hey, this is gonna be terrible!";
      const issues = tools.validateContent(text, 'error_message');
      
      expect(issues.length).toBeGreaterThan(0);
      issues.forEach(issue => {
        expect([Severity.LOW, Severity.MEDIUM, Severity.HIGH]).toContain(issue.severity);
      });
    });
  });

  describe('edge cases', () => {
    it('should handle empty text', () => {
      const revisions = tools.generateContentRevisions('', 'help_text');
      expect(revisions).toEqual([]);
      
      const standardizations = tools.standardizeTerminology('');
      expect(standardizations).toEqual([]);
      
      const issues = tools.validateContent('', 'help_text');
      expect(issues).toEqual([]);
    });

    it('should handle unknown content types gracefully', () => {
      const text = "Some text content.";
      // @ts-ignore - testing with invalid content type
      const revisions = tools.generateContentRevisions(text, 'unknown_type');
      expect(revisions).toEqual([]);
    });

    it('should handle text with special characters', () => {
      const text = "Error: Invalid input! @#$%^&*()";
      const fixed = tools.applyAutomaticFixes(text, 'error_message');
      expect(fixed).toContain('Error:');
      expect(fixed).not.toContain('!!');
    });

    it('should handle very long text', () => {
      const longText = "login ".repeat(100);
      const standardizations = tools.standardizeTerminology(longText);
      expect(standardizations.length).toBeGreaterThan(0);
    });
  });
});