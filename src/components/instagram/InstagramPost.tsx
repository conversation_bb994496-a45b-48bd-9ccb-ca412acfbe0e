import React, { StrictMode } from 'react';

/**
 * Instagram post component for custom element
 */
export interface InstagramPostProps {
  postUrl?: string;
  captions?: boolean;
}

export const InstagramPost: React.FC<InstagramPostProps> = ({ 
  postUrl = 'https://www.instagram.com/p/C78_gA7yZ6J/',
  captions = false 
}) => {
  return (
    <StrictMode>
      <div
        dangerouslySetInnerHTML={{
          __html: `<blockquote 
            class="instagram-media" 
            data-instgrm-permalink="${postUrl}" 
            data-instgrm-version="14"
            ${captions ? '' : 'data-instgrm-captioned'}
          ></blockquote>`
        }}
      />
    </StrictMode>
  );
};
