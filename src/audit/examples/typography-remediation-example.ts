/**
 * Typography Remediation System Example
 * 
 * This example demonstrates how to use the typography remediation system
 * to automatically fix typography inconsistencies in a React application.
 */

import { TypographyRemediationEngine } from '../remediation/typography-remediation';
import { TypographyAnalyzer } from '../analyzers/typography-analyzer';
import { DesignTokenSystem } from '../design-tokens/types';
import { FileInfo, Issue } from '../types';
import { auditLogger } from '../core/logger';

// Example design token system
const exampleDesignTokens: DesignTokenSystem = {
  colors: {
    primary: {
      50: '#f0f9ff',
      100: '#e0f2fe',
      200: '#bae6fd',
      300: '#7dd3fc',
      400: '#38bdf8',
      500: '#0ea5e9',
      600: '#0284c7',
      700: '#0369a1',
      800: '#075985',
      900: '#0c4a6e',
      950: '#082f49'
    },
    secondary: {
      50: '#fafafa',
      100: '#f4f4f5',
      200: '#e4e4e7',
      300: '#d4d4d8',
      400: '#a1a1aa',
      500: '#71717a',
      600: '#52525b',
      700: '#3f3f46',
      800: '#27272a',
      900: '#18181b',
      950: '#09090b'
    },
    semantic: {
      success: {
        light: '#dcfce7',
        default: '#16a34a',
        dark: '#15803d',
        contrast: '#ffffff'
      },
      warning: {
        light: '#fef3c7',
        default: '#f59e0b',
        dark: '#d97706',
        contrast: '#ffffff'
      },
      error: {
        light: '#fee2e2',
        default: '#dc2626',
        dark: '#b91c1c',
        contrast: '#ffffff'
      },
      info: {
        light: '#dbeafe',
        default: '#3b82f6',
        dark: '#2563eb',
        contrast: '#ffffff'
      }
    },
    neutral: {
      50: '#fafafa',
      100: '#f4f4f5',
      200: '#e4e4e7',
      300: '#d4d4d8',
      400: '#a1a1aa',
      500: '#71717a',
      600: '#52525b',
      700: '#3f3f46',
      800: '#27272a',
      900: '#18181b',
      950: '#09090b'
    },
    surface: {
      background: '#ffffff',
      foreground: '#09090b',
      card: '#ffffff',
      cardForeground: '#09090b',
      popover: '#ffffff',
      popoverForeground: '#09090b',
      muted: '#f4f4f5',
      mutedForeground: '#71717a',
      accent: '#f4f4f5',
      accentForeground: '#18181b',
      border: '#e4e4e7',
      input: '#e4e4e7',
      ring: '#3b82f6'
    }
  },
  typography: {
    fontFamilies: {
      sans: ['Inter', 'ui-sans-serif', 'system-ui', 'sans-serif'],
      serif: ['Georgia', 'ui-serif', 'serif'],
      mono: ['JetBrains Mono', 'ui-monospace', 'monospace'],
      display: ['Inter Display', 'Inter', 'sans-serif']
    },
    fontSizes: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
      '5xl': '3rem',
      '6xl': '3.75rem',
      '7xl': '4.5rem',
      '8xl': '6rem',
      '9xl': '8rem'
    },
    fontWeights: {
      thin: 100,
      extralight: 200,
      light: 300,
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
      extrabold: 800,
      black: 900
    },
    lineHeights: {
      none: 1,
      tight: 1.25,
      snug: 1.375,
      normal: 1.5,
      relaxed: 1.625,
      loose: 2
    },
    letterSpacing: {
      tighter: '-0.05em',
      tight: '-0.025em',
      normal: '0em',
      wide: '0.025em',
      wider: '0.05em',
      widest: '0.1em'
    },
    typeScale: {
      h1: {
        fontSize: '2.25rem',
        fontWeight: 700,
        lineHeight: 1.2,
        letterSpacing: '-0.025em',
        fontFamily: ['Inter Display', 'Inter', 'sans-serif']
      },
      h2: {
        fontSize: '1.875rem',
        fontWeight: 600,
        lineHeight: 1.3,
        letterSpacing: '-0.025em',
        fontFamily: ['Inter Display', 'Inter', 'sans-serif']
      },
      h3: {
        fontSize: '1.5rem',
        fontWeight: 600,
        lineHeight: 1.4,
        letterSpacing: '0em',
        fontFamily: ['Inter', 'sans-serif']
      },
      h4: {
        fontSize: '1.25rem',
        fontWeight: 500,
        lineHeight: 1.4,
        letterSpacing: '0em',
        fontFamily: ['Inter', 'sans-serif']
      },
      h5: {
        fontSize: '1.125rem',
        fontWeight: 500,
        lineHeight: 1.5,
        letterSpacing: '0em',
        fontFamily: ['Inter', 'sans-serif']
      },
      h6: {
        fontSize: '1rem',
        fontWeight: 500,
        lineHeight: 1.5,
        letterSpacing: '0em',
        fontFamily: ['Inter', 'sans-serif']
      },
      body: {
        fontSize: '1rem',
        fontWeight: 400,
        lineHeight: 1.5,
        letterSpacing: '0em',
        fontFamily: ['Inter', 'sans-serif']
      },
      bodyLarge: {
        fontSize: '1.125rem',
        fontWeight: 400,
        lineHeight: 1.5,
        letterSpacing: '0em',
        fontFamily: ['Inter', 'sans-serif']
      },
      bodySmall: {
        fontSize: '0.875rem',
        fontWeight: 400,
        lineHeight: 1.5,
        letterSpacing: '0em',
        fontFamily: ['Inter', 'sans-serif']
      },
      caption: {
        fontSize: '0.75rem',
        fontWeight: 400,
        lineHeight: 1.4,
        letterSpacing: '0.025em',
        fontFamily: ['Inter', 'sans-serif']
      },
      overline: {
        fontSize: '0.75rem',
        fontWeight: 600,
        lineHeight: 1.4,
        letterSpacing: '0.1em',
        fontFamily: ['Inter', 'sans-serif']
      }
    }
  },
  spacing: {
    scale: {
      0: '0px',
      px: '1px',
      0.5: '0.125rem',
      1: '0.25rem',
      1.5: '0.375rem',
      2: '0.5rem',
      2.5: '0.625rem',
      3: '0.75rem',
      3.5: '0.875rem',
      4: '1rem',
      5: '1.25rem',
      6: '1.5rem',
      7: '1.75rem',
      8: '2rem',
      9: '2.25rem',
      10: '2.5rem',
      11: '2.75rem',
      12: '3rem',
      14: '3.5rem',
      16: '4rem',
      20: '5rem',
      24: '6rem',
      28: '7rem',
      32: '8rem',
      36: '9rem',
      40: '10rem',
      44: '11rem',
      48: '12rem',
      52: '13rem',
      56: '14rem',
      60: '15rem',
      64: '16rem',
      72: '18rem',
      80: '20rem',
      96: '24rem'
    },
    semantic: {
      component: {
        padding: {
          xs: '0.5rem',
          sm: '0.75rem',
          md: '1rem',
          lg: '1.5rem',
          xl: '2rem'
        },
        margin: {
          xs: '0.5rem',
          sm: '0.75rem',
          md: '1rem',
          lg: '1.5rem',
          xl: '2rem'
        },
        gap: {
          xs: '0.5rem',
          sm: '0.75rem',
          md: '1rem',
          lg: '1.5rem',
          xl: '2rem'
        }
      },
      layout: {
        section: '4rem',
        container: '2rem',
        content: '1.5rem'
      }
    },
    layout: {
      containerMaxWidth: {
        sm: '640px',
        md: '768px',
        lg: '1024px',
        xl: '1280px',
        '2xl': '1536px'
      },
      containerPadding: {
        mobile: '1rem',
        tablet: '2rem',
        desktop: '3rem'
      }
    }
  },
  layout: {
    grid: {
      columns: {
        default: 12,
        sm: 4,
        md: 8,
        lg: 12,
        xl: 12
      },
      gutters: {
        xs: '0.5rem',
        sm: '1rem',
        md: '1.5rem',
        lg: '2rem',
        xl: '3rem'
      },
      margins: {
        mobile: '1rem',
        tablet: '2rem',
        desktop: '3rem',
        wide: '4rem'
      },
      baseline: {
        unit: '0.25rem',
        scale: 4
      }
    },
    breakpoints: {
      xs: '475px',
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1280px',
      '2xl': '1536px',
      ranges: {
        mobile: '0-640px',
        tablet: '641px-1024px',
        desktop: '1025px-1536px',
        wide: '1537px+'
      }
    },
    containers: {
      maxWidths: {
        xs: '475px',
        sm: '640px',
        md: '768px',
        lg: '1024px',
        xl: '1280px',
        '2xl': '1536px',
        full: '100%'
      },
      padding: {
        mobile: '1rem',
        tablet: '2rem',
        desktop: '3rem'
      },
      centering: {
        auto: 'margin: 0 auto',
        flex: {
          horizontal: 'justify-content: center',
          vertical: 'align-items: center',
          both: 'justify-content: center; align-items: center'
        },
        grid: {
          horizontal: 'justify-items: center',
          vertical: 'align-items: center',
          both: 'place-items: center'
        }
      }
    },
    patterns: {
      stack: {
        gap: {
          xs: '0.5rem',
          sm: '1rem',
          md: '1.5rem',
          lg: '2rem',
          xl: '3rem'
        },
        alignment: {
          start: 'align-items: flex-start',
          center: 'align-items: center',
          end: 'align-items: flex-end',
          stretch: 'align-items: stretch'
        }
      },
      cluster: {
        gap: {
          xs: '0.5rem',
          sm: '1rem',
          md: '1.5rem',
          lg: '2rem',
          xl: '3rem'
        },
        justify: {
          start: 'justify-content: flex-start',
          center: 'justify-content: center',
          end: 'justify-content: flex-end',
          between: 'justify-content: space-between',
          around: 'justify-content: space-around',
          evenly: 'justify-content: space-evenly'
        },
        align: {
          start: 'align-items: flex-start',
          center: 'align-items: center',
          end: 'align-items: flex-end',
          baseline: 'align-items: baseline'
        }
      },
      sidebar: {
        sidebarWidth: {
          narrow: '200px',
          default: '300px',
          wide: '400px'
        },
        gap: '1rem',
        breakpoint: '768px',
        contentMinWidth: '50%'
      },
      switcher: {
        threshold: '768px',
        gap: '1rem',
        limit: 4
      },
      cover: {
        minHeight: {
          viewport: '100vh',
          container: '100%',
          content: 'auto'
        },
        padding: {
          top: '2rem',
          bottom: '2rem',
          sides: '1rem'
        }
      },
      grid: {
        minItemWidth: {
          xs: '200px',
          sm: '250px',
          md: '300px',
          lg: '350px'
        },
        gap: {
          xs: '0.5rem',
          sm: '1rem',
          md: '1.5rem',
          lg: '2rem',
          xl: '3rem'
        },
        autoFit: true,
        autoFill: false
      }
    },
    validation: {
      rules: [],
      patterns: [],
      accessibility: {
        focusManagement: {
          tabOrder: true,
          focusVisible: true,
          skipLinks: true
        },
        semanticStructure: {
          headingHierarchy: true,
          landmarkRoles: true,
          listStructure: true
        },
        responsiveDesign: {
          minTouchTarget: '44px',
          maxLineLength: '75ch',
          scalableText: true
        }
      }
    }
  },
  metadata: {
    version: '1.0.0',
    generatedAt: new Date().toISOString(),
    source: 'typography-remediation-example'
  }
};

// Example files with typography issues
const exampleFiles: FileInfo[] = [
  {
    path: 'src/components/Button.tsx',
    extension: '.tsx',
    content: `
import React from 'react';

interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
}

const Button: React.FC<ButtonProps> = ({ children, variant = 'primary', size = 'md' }) => {
  const baseStyles = {
    fontSize: '16px', // Should use design token
    fontWeight: '600', // Should use design token
    lineHeight: '1.6', // Should be standardized
    padding: '12px 24px',
    borderRadius: '8px',
    border: 'none',
    cursor: 'pointer'
  };

  const variantStyles = {
    primary: {
      backgroundColor: '#3b82f6',
      color: '#ffffff'
    },
    secondary: {
      backgroundColor: '#f3f4f6',
      color: '#374151'
    }
  };

  const sizeStyles = {
    sm: { fontSize: '14px', padding: '8px 16px' }, // Hardcoded font size
    md: { fontSize: '16px', padding: '12px 24px' }, // Hardcoded font size
    lg: { fontSize: '18px', padding: '16px 32px' }  // Hardcoded font size
  };

  return (
    <button 
      style={{
        ...baseStyles,
        ...variantStyles[variant],
        ...sizeStyles[size]
      }}
    >
      {children}
    </button>
  );
};

export default Button;
    `.trim()
  },
  {
    path: 'src/components/Typography.tsx',
    extension: '.tsx',
    content: `
import React from 'react';

const Typography: React.FC = () => {
  return (
    <div>
      <h1 style={{ fontSize: '32px', fontWeight: '800', lineHeight: '1.1' }}>
        Main Heading
      </h1>
      <h2 style={{ fontSize: '28px', fontWeight: '700', lineHeight: '1.2' }}>
        Secondary Heading
      </h2>
      <h3 style={{ fontSize: '24px', fontWeight: '600' }}>
        Tertiary Heading
      </h3>
      <p style={{ fontSize: '16px', lineHeight: '1.7', color: '#374151' }}>
        This is body text with inconsistent typography values.
      </p>
      <small style={{ fontSize: '12px', lineHeight: '1.4' }}>
        Small text with hardcoded values
      </small>
    </div>
  );
};

export default Typography;
    `.trim()
  },
  {
    path: 'src/styles/components.css',
    extension: '.css',
    content: `
.card {
  font-size: 16px;
  line-height: 1.6;
  font-weight: 400;
  color: #374151;
}

.card-title {
  font-size: 20px;
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 8px;
}

.card-subtitle {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  color: #6b7280;
}

.alert {
  font-size: 14px;
  line-height: 1.5;
  font-weight: 500;
}

.alert-title {
  font-size: 16px;
  font-weight: 600;
  line-height: 1.25;
}

/* Some inconsistent typography */
.special-text {
  font-size: 15px; /* Non-standard size */
  line-height: 1.65; /* Non-standard line height */
  font-weight: 450; /* Non-standard weight */
}
    `.trim()
  }
];

/**
 * Complete Typography Remediation Workflow
 */
export async function runTypographyRemediationExample(): Promise<void> {
  console.log('🔍 Starting Typography Remediation Example\n');

  try {
    // Step 1: Analyze typography issues
    console.log('Step 1: Analyzing typography issues...');
    const analyzer = new TypographyAnalyzer();
    const analysisResult = await analyzer.analyze(exampleFiles);
    
    console.log(`Found ${analysisResult.issues.length} typography issues:`);
    analysisResult.issues.forEach((issue, index) => {
      console.log(`  ${index + 1}. ${issue.description}`);
      console.log(`     Location: ${issue.location.filePath}:${issue.location.lineNumber}`);
      console.log(`     Severity: ${issue.severity}`);
      console.log(`     Auto-fixable: ${issue.autoFixable ? '✅' : '❌'}\n`);
    });

    // Step 2: Initialize remediation engine
    console.log('Step 2: Initializing typography remediation engine...');
    const remediationEngine = new TypographyRemediationEngine(exampleDesignTokens);

    // Step 3: Standardize font sizes
    console.log('Step 3: Standardizing font sizes...');
    const fontSizeReplacements = await remediationEngine.standardizeFontSizes(exampleFiles);
    console.log(`Generated ${fontSizeReplacements.length} font size replacements:`);
    fontSizeReplacements.slice(0, 5).forEach((replacement, index) => {
      console.log(`  ${index + 1}. ${replacement.filePath}:${replacement.lineNumber}`);
      console.log(`     ${replacement.originalValue} → ${replacement.tokenValue}`);
    });
    if (fontSizeReplacements.length > 5) {
      console.log(`     ... and ${fontSizeReplacements.length - 5} more\n`);
    } else {
      console.log('');
    }

    // Step 4: Correct typography hierarchy
    console.log('Step 4: Correcting typography hierarchy...');
    const hierarchyCorrections = await remediationEngine.correctTypographyHierarchy(exampleFiles);
    console.log(`Generated ${hierarchyCorrections.length} hierarchy corrections:`);
    hierarchyCorrections.slice(0, 3).forEach((correction, index) => {
      console.log(`  ${index + 1}. ${correction.element} ${correction.property}:`);
      console.log(`     ${correction.currentValue} → ${correction.correctedValue} (${correction.tokenName})`);
    });
    if (hierarchyCorrections.length > 3) {
      console.log(`     ... and ${hierarchyCorrections.length - 3} more\n`);
    } else {
      console.log('');
    }

    // Step 5: Standardize line heights
    console.log('Step 5: Standardizing line heights...');
    const lineHeightStandardizations = await remediationEngine.standardizeLineHeights(exampleFiles);
    console.log(`Generated ${lineHeightStandardizations.length} line height standardizations:`);
    lineHeightStandardizations.slice(0, 3).forEach((standardization, index) => {
      console.log(`  ${index + 1}. ${standardization.location.filePath}:${standardization.location.lineNumber}`);
      console.log(`     ${standardization.currentValue} → ${standardization.standardizedValue} (${standardization.tokenName})`);
    });
    if (lineHeightStandardizations.length > 3) {
      console.log(`     ... and ${lineHeightStandardizations.length - 3} more\n`);
    } else {
      console.log('');
    }

    // Step 6: Run comprehensive remediation
    console.log('Step 6: Running comprehensive remediation...');
    const autoFixableIssues = analysisResult.issues.filter(issue => issue.autoFixable);
    const remediationResult = await remediationEngine.remediateTypographyIssues(
      autoFixableIssues, 
      exampleFiles
    );

    console.log('Remediation Results:');
    console.log(`  ✅ Success: ${remediationResult.success}`);
    console.log(`  📁 Files Modified: ${remediationResult.filesModified.length}`);
    console.log(`  🔄 Total Replacements: ${remediationResult.summary.totalReplacements}`);
    console.log(`  🏗️  Tokens Created: ${remediationResult.summary.tokensCreated}`);
    console.log(`  📊 Hierarchy Corrections: ${remediationResult.summary.hierarchyCorrections}`);
    console.log(`  📏 Line Height Standardizations: ${remediationResult.summary.lineHeightStandardizations}`);
    console.log(`  ❌ Errors: ${remediationResult.errors.length}\n`);

    // Step 7: Validate token usage
    console.log('Step 7: Validating typography token usage...');
    const validation = await remediationEngine.validateTypographyTokenUsage(exampleFiles);
    console.log('Token Usage Validation:');
    console.log(`  ✅ Valid Usages: ${validation.validUsages}`);
    console.log(`  ❌ Invalid Usages: ${validation.invalidUsages}`);
    console.log(`  📊 Coverage:`);
    console.log(`     Font Size: ${validation.coverage.fontSize} tokens`);
    console.log(`     Font Family: ${validation.coverage.fontFamily} tokens`);
    console.log(`     Font Weight: ${validation.coverage.fontWeight} tokens`);
    console.log(`     Line Height: ${validation.coverage.lineHeight} tokens`);

    if (validation.suggestions.length > 0) {
      console.log(`  💡 Suggestions:`);
      validation.suggestions.slice(0, 3).forEach((suggestion, index) => {
        console.log(`     ${index + 1}. ${suggestion}`);
      });
      if (validation.suggestions.length > 3) {
        console.log(`     ... and ${validation.suggestions.length - 3} more`);
      }
    }

    console.log('\n🎉 Typography Remediation Example Completed Successfully!');
    console.log('\nNext Steps:');
    console.log('1. Review the generated replacements and corrections');
    console.log('2. Apply the changes to your actual files');
    console.log('3. Update your design system documentation');
    console.log('4. Set up automated checks to prevent future inconsistencies');

  } catch (error) {
    console.error('❌ Typography Remediation Example Failed:', error);
    auditLogger.error('Typography remediation example failed', error as Error);
  }
}

/**
 * Utility function to demonstrate specific remediation features
 */
export async function demonstrateSpecificFeatures(): Promise<void> {
  console.log('\n🔧 Demonstrating Specific Typography Remediation Features\n');

  const remediationEngine = new TypographyRemediationEngine(exampleDesignTokens);

  // Demonstrate font size standardization
  console.log('1. Font Size Standardization:');
  const testFile: FileInfo = {
    path: 'test.tsx',
    extension: '.tsx',
    content: `
      const styles = {
        small: { fontSize: '12px' },
        medium: { fontSize: '16px' },
        large: { fontSize: '20px' },
        xlarge: { fontSize: '24px' }
      };
    `.trim()
  };

  const fontSizeReplacements = await remediationEngine.standardizeFontSizes([testFile]);
  fontSizeReplacements.forEach(replacement => {
    console.log(`   ${replacement.originalValue} → ${replacement.tokenValue}`);
  });

  // Demonstrate line height standardization
  console.log('\n2. Line Height Standardization:');
  const lineHeightFile: FileInfo = {
    path: 'test-lineheight.css',
    extension: '.css',
    content: `
      .text1 { line-height: 1.6; }
      .text2 { line-height: 1.7; }
      .text3 { line-height: 1.8; }
    `.trim()
  };

  const lineHeightStandardizations = await remediationEngine.standardizeLineHeights([lineHeightFile]);
  lineHeightStandardizations.forEach(standardization => {
    console.log(`   ${standardization.currentValue} → ${standardization.standardizedValue} (${standardization.tokenName})`);
  });

  console.log('\n✨ Feature Demonstration Complete!');
}

// Export the main function for use in other examples
export { exampleDesignTokens, exampleFiles };

// Run the example if this file is executed directly
if (require.main === module) {
  runTypographyRemediationExample()
    .then(() => demonstrateSpecificFeatures())
    .catch(console.error);
}