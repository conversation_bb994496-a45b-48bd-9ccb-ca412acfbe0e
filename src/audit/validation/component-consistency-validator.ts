/**
 * Component Consistency Validator
 * Validates consistent component usage and patterns across the codebase
 */

import { ValidationRule, ValidationViolation, Validator, ValidationResult, ValidationCategory, ValidationFile, FileType } from './types';
import { DesignTokenSystem } from '../design-tokens/types';
import { Severity } from '../types';

export class ComponentConsistencyValidator implements Validator {
  name = 'Component Consistency Validator';
  category = ValidationCategory.COMPONENT_CONSISTENCY;
  rules: ValidationRule[] = [
    new ComponentNamingRule(),
    new PropConsistencyRule(),
    new StateManagementRule(),
    new StyleConsistencyRule(),
    new AccessibilityPatternRule()
  ];

  async validate(files: ValidationFile[], tokens?: DesignTokenSystem): Promise<ValidationResult> {
    const startTime = Date.now();
    const violations: ValidationViolation[] = [];

    const componentFiles = files.filter(file => file.type === FileType.COMPONENT);

    for (const file of componentFiles) {
      for (const rule of this.rules) {
        const ruleViolations = rule.validate(file.content, file.path, tokens);
        violations.push(...ruleViolations);
      }
    }

    const executionTime = Date.now() - startTime;

    return {
      isValid: violations.length === 0,
      violations,
      summary: this.generateSummary(violations),
      executionTime
    };
  }

  private generateSummary(violations: ValidationViolation[]) {
    const severityBreakdown = violations.reduce((acc, v) => {
      acc[v.severity] = (acc[v.severity] || 0) + 1;
      return acc;
    }, {} as Record<Severity, number>);

    const ruleBreakdown = violations.reduce((acc, v) => {
      acc[v.rule] = (acc[v.rule] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const fileBreakdown = violations.reduce((acc, v) => {
      acc[v.location.filePath] = (acc[v.location.filePath] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalViolations: violations.length,
      severityBreakdown,
      ruleBreakdown,
      fileBreakdown
    };
  }
}

class ComponentNamingRule implements ValidationRule {
  id = 'component-naming';
  name = 'Component Naming Convention';
  description = 'Ensures components follow consistent naming conventions';
  category = ValidationCategory.COMPONENT_CONSISTENCY;
  severity = Severity.LOW;

  validate(content: string, filePath: string): ValidationViolation[] {
    const violations: ValidationViolation[] = [];
    
    // Extract component name from file path
    const fileName = filePath.split('/').pop()?.replace(/\.(tsx|jsx)$/, '');
    if (!fileName) return violations;

    // Check if component name follows PascalCase
    if (!this.isPascalCase(fileName)) {
      violations.push({
        id: `${filePath}:1:1:component-naming`,
        rule: this.id,
        severity: this.severity,
        message: `Component file name '${fileName}' should follow PascalCase convention`,
        location: {
          filePath,
          lineNumber: 1,
          columnNumber: 1,
          context: `File: ${fileName}`
        },
        actualValue: fileName,
        suggestion: `Rename to ${this.toPascalCase(fileName)}`,
        autoFixable: false
      });
    }

    // Check if the main export matches the file name
    const exportPattern = new RegExp(`export\\s+(?:default\\s+)?(?:function\\s+|const\\s+|class\\s+)([A-Za-z][A-Za-z0-9]*)`);
    const lines = content.split('\n');
    
    lines.forEach((line, lineIndex) => {
      const match = exportPattern.exec(line);
      if (match) {
        const exportedName = match[1];
        if (exportedName !== fileName && !line.includes('export default')) {
          violations.push({
            id: `${filePath}:${lineIndex + 1}:1:component-naming-mismatch`,
            rule: this.id,
            severity: this.severity,
            message: `Exported component name '${exportedName}' should match file name '${fileName}'`,
            location: {
              filePath,
              lineNumber: lineIndex + 1,
              columnNumber: 1,
              context: line.trim()
            },
            actualValue: exportedName,
            suggestion: `Rename component to '${fileName}' or rename file to '${exportedName}'`,
            autoFixable: false
          });
        }
      }
    });

    return violations;
  }

  private isPascalCase(str: string): boolean {
    return /^[A-Z][a-zA-Z0-9]*$/.test(str);
  }

  private toPascalCase(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }
}

class PropConsistencyRule implements ValidationRule {
  id = 'prop-consistency';
  name = 'Prop Interface Consistency';
  description = 'Ensures consistent prop patterns across similar components';
  category = ValidationCategory.COMPONENT_CONSISTENCY;
  severity = Severity.MEDIUM;

  validate(content: string, filePath: string): ValidationViolation[] {
    const violations: ValidationViolation[] = [];
    
    // Look for common prop patterns that should be consistent
    const commonProps = ['className', 'children', 'onClick', 'disabled', 'variant', 'size'];
    const lines = content.split('\n');
    
    // Find interface definitions
    const interfacePattern = /interface\s+(\w+Props)\s*{([^}]*)}/gs;
    let match;
    
    while ((match = interfacePattern.exec(content)) !== null) {
      const interfaceName = match[1];
      const interfaceBody = match[2];
      const interfaceStart = content.substring(0, match.index).split('\n').length;
      
      // Check for missing common props
      commonProps.forEach(prop => {
        if (this.shouldHaveProp(prop, interfaceBody, content) && !interfaceBody.includes(prop)) {
          violations.push({
            id: `${filePath}:${interfaceStart}:1:missing-common-prop`,
            rule: this.id,
            severity: this.severity,
            message: `Interface '${interfaceName}' is missing common prop '${prop}'`,
            location: {
              filePath,
              lineNumber: interfaceStart,
              columnNumber: 1,
              context: `interface ${interfaceName}`
            },
            actualValue: interfaceBody.trim(),
            suggestion: `Consider adding '${prop}?: ${this.getPropType(prop)}' to the interface`,
            autoFixable: false
          });
        }
      });
    }

    return violations;
  }

  private shouldHaveProp(prop: string, interfaceBody: string, content: string): boolean {
    // Simple heuristics to determine if a component should have certain props
    switch (prop) {
      case 'className':
        return true; // Most components should accept className
      case 'children':
        return content.includes('children') || content.includes('{children}');
      case 'onClick':
        return content.includes('button') || content.includes('click');
      case 'disabled':
        return content.includes('button') || content.includes('input');
      case 'variant':
        return content.includes('variant') || interfaceBody.includes('type');
      case 'size':
        return content.includes('size') || content.includes('small') || content.includes('large');
      default:
        return false;
    }
  }

  private getPropType(prop: string): string {
    const propTypes: Record<string, string> = {
      className: 'string',
      children: 'React.ReactNode',
      onClick: '() => void',
      disabled: 'boolean',
      variant: 'string',
      size: 'string'
    };
    return propTypes[prop] || 'unknown';
  }
}

class StateManagementRule implements ValidationRule {
  id = 'state-management';
  name = 'State Management Consistency';
  description = 'Ensures consistent state management patterns';
  category = ValidationCategory.COMPONENT_CONSISTENCY;
  severity = Severity.MEDIUM;

  validate(content: string, filePath: string): ValidationViolation[] {
    const violations: ValidationViolation[] = [];
    const lines = content.split('\n');
    
    // Check for mixed state management approaches
    const hasUseState = content.includes('useState');
    const hasUseReducer = content.includes('useReducer');
    const hasClassState = content.includes('this.state');
    
    if (hasClassState && (hasUseState || hasUseReducer)) {
      violations.push({
        id: `${filePath}:1:1:mixed-state-approaches`,
        rule: this.id,
        severity: this.severity,
        message: 'Component mixes class-based and hook-based state management',
        location: {
          filePath,
          lineNumber: 1,
          columnNumber: 1,
          context: 'Component definition'
        },
        actualValue: 'Mixed state management',
        suggestion: 'Use consistent state management approach (prefer hooks)',
        autoFixable: false
      });
    }

    // Check for state naming consistency
    const statePattern = /const\s+\[(\w+),\s*set(\w+)\]\s*=\s*useState/g;
    let stateMatch;
    
    while ((stateMatch = statePattern.exec(content)) !== null) {
      const stateName = stateMatch[1];
      const setterName = stateMatch[2];
      const expectedSetter = this.getExpectedSetterName(stateName);
      
      if (setterName !== expectedSetter) {
        const lineNumber = content.substring(0, stateMatch.index).split('\n').length;
        violations.push({
          id: `${filePath}:${lineNumber}:1:state-naming`,
          rule: this.id,
          severity: Severity.LOW,
          message: `State setter '${setterName}' should be '${expectedSetter}'`,
          location: {
            filePath,
            lineNumber,
            columnNumber: 1,
            context: stateMatch[0]
          },
          actualValue: setterName,
          expectedValue: expectedSetter,
          suggestion: `Rename setter to '${expectedSetter}'`,
          autoFixable: true
        });
      }
    }

    return violations;
  }

  private getExpectedSetterName(stateName: string): string {
    // Convert camelCase state name to setter name
    const capitalized = stateName.charAt(0).toUpperCase() + stateName.slice(1);
    return `set${capitalized}`;
  }
}

class StyleConsistencyRule implements ValidationRule {
  id = 'style-consistency';
  name = 'Style Implementation Consistency';
  description = 'Ensures consistent styling approaches across components';
  category = ValidationCategory.COMPONENT_CONSISTENCY;
  severity = Severity.MEDIUM;

  validate(content: string, filePath: string): ValidationViolation[] {
    const violations: ValidationViolation[] = [];
    
    // Check for mixed styling approaches
    const hasInlineStyles = content.includes('style={{');
    const hasCSSModules = content.includes('.module.css') || content.includes('styles.');
    const hasTailwind = /className="[^"]*(?:bg-|text-|p-|m-|flex|grid)/.test(content);
    const hasStyledComponents = content.includes('styled.') || content.includes('styled(');
    
    const stylingApproaches = [
      { name: 'inline styles', present: hasInlineStyles },
      { name: 'CSS modules', present: hasCSSModules },
      { name: 'Tailwind', present: hasTailwind },
      { name: 'styled-components', present: hasStyledComponents }
    ].filter(approach => approach.present);

    if (stylingApproaches.length > 1) {
      violations.push({
        id: `${filePath}:1:1:mixed-styling`,
        rule: this.id,
        severity: this.severity,
        message: `Component uses multiple styling approaches: ${stylingApproaches.map(a => a.name).join(', ')}`,
        location: {
          filePath,
          lineNumber: 1,
          columnNumber: 1,
          context: 'Component styling'
        },
        actualValue: stylingApproaches.map(a => a.name).join(', '),
        suggestion: 'Use a consistent styling approach throughout the component',
        autoFixable: false
      });
    }

    return violations;
  }
}

class AccessibilityPatternRule implements ValidationRule {
  id = 'accessibility-pattern';
  name = 'Accessibility Pattern Consistency';
  description = 'Ensures consistent accessibility patterns across components';
  category = ValidationCategory.COMPONENT_CONSISTENCY;
  severity = Severity.HIGH;

  validate(content: string, filePath: string): ValidationViolation[] {
    const violations: ValidationViolation[] = [];
    const lines = content.split('\n');
    
    // Check for missing accessibility attributes on interactive elements
    const interactiveElements = ['button', 'input', 'select', 'textarea', 'a'];
    
    lines.forEach((line, lineIndex) => {
      interactiveElements.forEach(element => {
        const elementPattern = new RegExp(`<${element}(?![^>]*(?:aria-label|aria-labelledby|aria-describedby))`, 'g');
        
        if (elementPattern.test(line) && !line.includes('aria-') && !line.includes('alt=')) {
          violations.push({
            id: `${filePath}:${lineIndex + 1}:1:missing-accessibility`,
            rule: this.id,
            severity: this.severity,
            message: `Interactive element '${element}' is missing accessibility attributes`,
            location: {
              filePath,
              lineNumber: lineIndex + 1,
              columnNumber: 1,
              context: line.trim()
            },
            actualValue: line.trim(),
            suggestion: `Add appropriate aria-label, aria-labelledby, or aria-describedby attributes`,
            autoFixable: false
          });
        }
      });
    });

    // Check for missing alt text on images
    const imgPattern = /<img(?![^>]*alt=)/g;
    lines.forEach((line, lineIndex) => {
      if (imgPattern.test(line)) {
        violations.push({
          id: `${filePath}:${lineIndex + 1}:1:missing-alt-text`,
          rule: this.id,
          severity: this.severity,
          message: 'Image element is missing alt attribute',
          location: {
            filePath,
            lineNumber: lineIndex + 1,
            columnNumber: 1,
            context: line.trim()
          },
          actualValue: line.trim(),
          suggestion: 'Add alt attribute with descriptive text or empty string for decorative images',
          autoFixable: false
        });
      }
    });

    return violations;
  }
}