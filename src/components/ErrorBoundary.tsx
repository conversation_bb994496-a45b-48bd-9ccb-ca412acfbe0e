import { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from "@/components/ui/button";
import { AlertCircle, RefreshCw, Home } from "lucide-react";
import { cn } from "@/lib/utils";

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onReset?: () => void;
  className?: string;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

/**
 * Enhanced ErrorBoundary component with responsive error display
 * Provides different layouts for mobile, tablet, and desktop screens
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  public state: ErrorBoundaryState = {
    hasError: false,
  };

  public static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("Uncaught error:", error, errorInfo);
    this.setState({ errorInfo });
  }

  private handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
    if (this.props.onReset) {
      this.props.onReset();
    }
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className={cn(
          "min-h-screen flex items-center justify-center bg-elev-background-muted",
          "px-4 sm:px-6 lg:px-8",
          this.props.className
        )}>
          <div className="max-w-md w-full space-y-6 text-center">
            {/* Error Icon - Responsive sizing */}
            <div className="flex justify-center">
              <div className="rounded-full bg-red-100 p-3 sm:p-4">
                <AlertCircle className="h-8 w-8 sm:h-12 sm:w-12 text-red-500" />
              </div>
            </div>

            {/* Error Title - Responsive typography */}
            <div className="space-y-2">
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-elev-navy">
                Oops! Something went wrong
              </h1>
              <p className="text-sm sm:text-base text-elev-text-secondary leading-relaxed">
                We're sorry for the inconvenience. Please try refreshing the page or go back to the homepage.
              </p>
            </div>

            {/* Development Error Details - Responsive layout */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <div className="mt-6 p-3 sm:p-4 bg-gray-100 rounded-lg text-left">
                <div className="space-y-3">
                  <h3 className="text-sm font-semibold text-gray-800">
                    Error Details (Development Mode)
                  </h3>
                  <div className="bg-red-50 border border-red-200 rounded p-2 sm:p-3">
                    <p className="font-mono text-xs sm:text-sm text-red-700 break-all">
                      {this.state.error.toString()}
                    </p>
                  </div>
                  <details className="group">
                    <summary className="text-xs sm:text-sm cursor-pointer text-gray-700 hover:text-gray-900 transition-colors">
                      <span className="group-open:hidden">Show stack trace</span>
                      <span className="hidden group-open:inline">Hide stack trace</span>
                    </summary>
                    <div className="mt-2 p-2 sm:p-3 bg-gray-800 rounded text-gray-100 overflow-auto max-h-32 sm:max-h-40">
                      <pre className="text-xs leading-tight whitespace-pre-wrap break-all">
                        {this.state.errorInfo?.componentStack}
                      </pre>
                    </div>
                  </details>
                </div>
              </div>
            )}

            {/* Action Buttons - Responsive layout */}
            <div className="space-y-3 sm:space-y-0 sm:space-x-3 sm:flex sm:justify-center">
              <Button 
                onClick={this.handleReset}
                className="w-full sm:w-auto bg-elev-navy hover:bg-elev-navy/90 text-white px-6 py-2.5"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
              <Button 
                onClick={this.handleGoHome}
                variant="outline"
                className="w-full sm:w-auto border-elev-navy text-elev-navy hover:bg-elev-navy hover:text-white px-6 py-2.5"
              >
                <Home className="h-4 w-4 mr-2" />
                Go Home
              </Button>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Helper function to handle optional chaining with TypeScript
declare const process: {
  env: {
    NODE_ENV: string;
  };
} & NodeJS.Process;

// Add cn utility type if not already imported
type ClassValue = string | number | boolean | null | undefined;

declare function cn(...inputs: ClassValue[]): string;
