import { useEffect } from "react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import TrainingProgramSEO from "@/components/TrainingProgramSEO";
import { Breadcrumbs } from "@/components/ui/Breadcrumbs";
import { useBreadcrumbs, useShouldShowBreadcrumbs } from "@/hooks/useBreadcrumbs";

const GoalieTraining = () => {
  const breadcrumbs = useBreadcrumbs();
  const shouldShowBreadcrumbs = useShouldShowBreadcrumbs();
  
  useEffect(() => {
    // Page title now handled by SEO component

    // Implement scroll reveal
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("active");
          }
        });
      },
      { threshold: 0.1 }
    );

    const revealElements = document.querySelectorAll(".reveal");
    revealElements.forEach((el) => observer.observe(el));

    return () => {
      revealElements.forEach((el) => observer.unobserve(el));
    };
  }, []);

  return (
    <div className="flex flex-col min-h-screen font-sans">
      <TrainingProgramSEO
        title="Goalie Training - ELEV802 | 702HOCKEY - Premier Hockey Training in Las Vegas"
        programName="Elite Goalie Training Program"
        programDescription="Specialized goalie training programs designed to enhance positioning, movement, and save techniques for hockey goalies of all ages and skill levels at ELEV802's premier facility."
        programImage="/images/goalie-training.jpg"
        programType="goalie"
        price={225}
        slug="goalie-training"
        reviews={[
          {
            reviewRating: 5,
            author: "Mike S.",
            reviewBody: "The goalie training at 702HOCKEY transformed my son's game. Their specialized techniques and video analysis helped him improve his save percentage dramatically.",
            datePublished: "2025-04-15"
          },
          {
            reviewRating: 5,
            author: "Jennifer L.",
            reviewBody: "As a female goaltender, I found the training exceptionally well-tailored to my style. The coaches understand the technical aspects of the position better than anyone in Las Vegas.",
            datePublished: "2025-03-22"
          }
        ]}
      />
      <Header />
      {shouldShowBreadcrumbs && (
        <Breadcrumbs
          items={breadcrumbs}
          showHomeIcon={true}
          separator="chevron"
        />
      )}
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="relative h-[70vh] bg-gradient-to-b from-gray-100 via-gray-50 to-white text-white reveal overflow-hidden flex items-center">
          {/* Background Video */}
          <div className="absolute inset-0 w-full h-full">
            <iframe
              className="absolute top-1/2 left-1/2 w-full h-full min-w-full min-h-full transform -translate-x-1/2 -translate-y-1/2 scale-150"
              src="https://www.youtube.com/embed/7tIRJaEatB8?autoplay=1&mute=1&loop=1&playlist=7tIRJaEatB8&controls=0&showinfo=0"
              title="Goalie Training Background Video"
              frameBorder="0"
              allow="autoplay; encrypted-media"
              allowFullScreen
            />
            {/* Dark overlay removed as requested */}
          </div>
          {/* Content overlay */}
          <div className="relative z-10">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-5xl md:text-6xl font-bold mb-6">ELITE GOALIE TRAINING</h1>
            <p className="text-xl md:text-2xl max-w-4xl mx-auto mb-8 leading-relaxed">
              Develop the skills, confidence, and mental toughness needed to excel between the pipes
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-8 mt-8 md:mt-12 max-w-4xl mx-auto">
              <div className="bg-gradient-to-b from-gray-100 via-gray-50 to-white/80 backdrop-blur-sm rounded-lg p-4 md:p-6">
                <div className="text-xl md:text-3xl font-bold text-white mb-1 md:mb-2">BEGINNER</div>
                <div className="text-sm md:text-lg text-white">TO PRO</div>
              </div>
              <div className="bg-gradient-to-b from-gray-100 via-gray-50 to-white/80 backdrop-blur-sm rounded-lg p-4 md:p-6">
                <div className="text-xl md:text-3xl font-bold text-white mb-1 md:mb-2">PERSONALIZED</div>
                <div className="text-sm md:text-lg text-white">COACHING</div>
              </div>
              <div className="bg-gradient-to-b from-gray-100 via-gray-50 to-white/80 backdrop-blur-sm rounded-lg p-4 md:p-6">
                <div className="text-xl md:text-3xl font-bold text-white mb-1 md:mb-2">VIDEO</div>
                <div className="text-sm md:text-lg text-white">ANALYSIS</div>
              </div>
            </div>
          </div>
          </div>
          {/* Decorative bottom element */}
          <div className="absolute bottom-0 left-0 w-full h-16 bg-gradient-to-b from-gray-100 via-gray-50 to-white transform -skew-y-2 translate-y-8"></div>
        </section>

        {/* Training Components Grid */}
        <section className="py-20 bg-gradient-to-b from-gray-100 via-gray-50 to-white reveal">
          <div className="container mx-auto px-4">
            <h2 className="text-4xl font-bold text-center mb-16 text-elev-navy">COMPREHENSIVE TRAINING PROGRAM</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              <div className="bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow">
                <div className="w-16 h-16 bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-full flex items-center justify-center mb-6">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-4 text-elev-text-primary">Skating & Stance</h3>
                <p className="text-elev-text-secondary">Master powerful movement and efficient positioning to dominate your crease</p>
              </div>
              
              <div className="bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow">
                <div className="w-16 h-16 bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-full flex items-center justify-center mb-6">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-4 text-elev-text-primary">Puck Handling</h3>
                <p className="text-elev-text-secondary">Become an asset to your team with confident breakout plays and puck control</p>
              </div>
              
              <div className="bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow">
                <div className="w-16 h-16 bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-full flex items-center justify-center mb-6">
                  <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-4 text-elev-text-primary">Tracking Drills</h3>
                <p className="text-elev-text-secondary">Develop superior puck tracking skills to control rebounds and make key saves</p>
              </div>
              
              <div className="bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow">
                <div className="w-16 h-16 bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-full flex items-center justify-center mb-6">
                  <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-4 text-elev-text-primary">Mental Toughness</h3>
                <p className="text-elev-text-secondary">Build the mental resilience and focus needed for clutch performances</p>
              </div>
              
              <div className="bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow">
                <div className="w-16 h-16 bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-full flex items-center justify-center mb-6">
                  <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-4 text-elev-text-primary">Video Analysis</h3>
                <p className="text-elev-text-secondary">Review and improve your technique with detailed video breakdown sessions</p>
              </div>
              
              <div className="bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow">
                <div className="w-16 h-16 bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-full flex items-center justify-center mb-6">
                  <svg className="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-4 text-elev-text-primary">Team Integration</h3>
                <p className="text-elev-text-secondary">Learn to communicate effectively and work seamlessly with your defense</p>
              </div>
            </div>
          </div>
          {/* Decorative bottom element */}
          <div className="absolute bottom-0 left-0 w-full h-16 bg-gradient-to-b from-gray-100 via-gray-50 to-white transform -skew-y-2 translate-y-8"></div>
        </section>

        {/* Our Approach Section */}
        <section className="py-20 bg-gradient-to-b from-gray-100 via-gray-50 to-white reveal">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <div className="text-center mb-16">
                <h2 className="text-4xl font-bold mb-6 text-elev-navy">OUR COACHING APPROACH</h2>
                <p className="text-xl text-elev-text-secondary max-w-3xl mx-auto">
                  Every goaltender is unique. Our personalized approach helps you develop your individual style while mastering fundamental techniques.
                </p>
              </div>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div className="space-y-8">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-16 h-16 bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-bold text-2xl">1</span>
                    </div>
                    <div>
                      <p className="text-elev-text-secondary">We evaluate your current skills, strengths, and areas for improvement to create a personalized development plan.</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-16 h-16 bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-bold text-2xl">2</span>
                    </div>
                    <div>
                      <p className="text-elev-text-secondary">Focus on fundamental techniques while adapting to your natural playing style and athletic abilities.</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-16 h-16 bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-bold text-2xl">3</span>
                    </div>
                    <div>
                      <p className="text-elev-text-secondary">Real-time video analysis and immediate coaching feedback to accelerate your learning and improvement.</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-16 h-16 bg-gradient-to-b from-gray-100 via-gray-50 to-white rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-bold text-2xl">4</span>
                    </div>
                    <div>
                      <p className="text-elev-text-secondary">Building lasting relationships with players and families to guide your hockey journey at every level.</p>
                    </div>
                  </div>
                </div>
                
                <div className="lg:pl-8">
                  <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-4">
                    <div className="rounded-lg shadow-lg overflow-hidden mb-6 aspect-[9/16] max-w-2xl mx-auto">
                      <iframe
                        className="w-full h-full scale-150"
                        src="https://www.youtube.com/embed/9C36syiwxnI?autoplay=1&mute=1&loop=1&playlist=9C36syiwxnI&controls=0&showinfo=0&rel=0&iv_load_policy=3&modestbranding=1"
                        title="Goalie Training Video"
                        frameBorder="0"
                        allow="autoplay; encrypted-media"
                        allowFullScreen
                      />
                    </div>
                    <div className="text-center">

                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Decorative bottom element */}
          <div className="absolute bottom-0 left-0 w-full h-16 bg-gradient-to-b from-gray-100 via-gray-50 to-white transform -skew-y-2 translate-y-8"></div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-gradient-to-b from-gray-100 via-gray-50 to-white text-white reveal">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto text-center">
              <div>
                <div className="text-5xl font-bold mb-2 text-blue-300">95%</div>
                <div className="text-xl">Save Percentage Improvement</div>
              </div>
              <div>
                <div className="text-5xl font-bold mb-2 text-blue-300">50+</div>
                <div className="text-xl">Goalies Trained</div>
              </div>
              <div>
                <div className="text-5xl font-bold mb-2 text-blue-300">10+</div>
                <div className="text-xl">Years Experience</div>
              </div>
            </div>
          </div>
          {/* Decorative bottom element */}
          <div className="absolute bottom-0 left-0 w-full h-16 bg-gradient-to-b from-gray-100 via-gray-50 to-white transform -skew-y-2 translate-y-8"></div>
        </section>


      </main>
      <Footer />
    </div>
  );
};

export default GoalieTraining;
