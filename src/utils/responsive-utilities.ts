/**
 * Responsive Utilities
 * 
 * This module provides type-safe utilities for responsive design patterns
 * and consistent spacing/layout across the application.
 */

// Container size variants
export const containerVariants = {
  responsive: 'container-responsive',
  fluid: 'container-fluid',
  narrow: 'container-narrow',
  wide: 'container-wide',
} as const;

// Section padding variants
export const sectionPaddingVariants = {
  sm: 'section-padding-sm',
  md: 'section-padding-md',
  lg: 'section-padding-lg',
  xl: 'section-padding-xl',
} as const;

// Typography variants
export const textResponsiveVariants = {
  xs: 'text-responsive-xs',
  sm: 'text-responsive-sm',
  base: 'text-responsive-base',
  lg: 'text-responsive-lg',
  xl: 'text-responsive-xl',
  '2xl': 'text-responsive-2xl',
  '3xl': 'text-responsive-3xl',
} as const;

// Heading variants
export const headingResponsiveVariants = {
  h1: 'heading-responsive-h1',
  h2: 'heading-responsive-h2',
  h3: 'heading-responsive-h3',
  h4: 'heading-responsive-h4',
} as const;

// Spacing variants
export const spaceResponsiveVariants = {
  xs: 'space-responsive-xs',
  sm: 'space-responsive-sm',
  md: 'space-responsive-md',
  lg: 'space-responsive-lg',
  xl: 'space-responsive-xl',
} as const;

// Margin variants
export const marginResponsiveVariants = {
  xs: 'margin-responsive-xs',
  sm: 'margin-responsive-sm',
  md: 'margin-responsive-md',
  lg: 'margin-responsive-lg',
  xl: 'margin-responsive-xl',
} as const;

// Padding variants
export const paddingResponsiveVariants = {
  xs: 'padding-responsive-xs',
  sm: 'padding-responsive-sm',
  md: 'padding-responsive-md',
  lg: 'padding-responsive-lg',
  xl: 'padding-responsive-xl',
} as const;

// Gap variants
export const gapResponsiveVariants = {
  xs: 'gap-responsive-xs',
  sm: 'gap-responsive-sm',
  md: 'gap-responsive-md',
  lg: 'gap-responsive-lg',
  xl: 'gap-responsive-xl',
} as const;

// Grid variants
export const gridResponsiveVariants = {
  '1-2-3': 'grid-responsive-1-2-3',
  '1-2-4': 'grid-responsive-1-2-4',
  '1-3-4': 'grid-responsive-1-3-4',
  '2-4-6': 'grid-responsive-2-4-6',
} as const;

// Flex variants
export const flexResponsiveVariants = {
  'col-row': 'flex-responsive-col-row',
  'col-row-md': 'flex-responsive-col-row-md',
  wrap: 'flex-responsive-wrap',
} as const;

// Button variants
export const buttonResponsiveVariants = {
  sm: 'btn-responsive-sm',
  md: 'btn-responsive-md',
  lg: 'btn-responsive-lg',
} as const;

// Card variants
export const cardResponsiveVariants = {
  default: 'card-responsive',
  compact: 'card-responsive-compact',
} as const;

// Touch target variants
export const touchTargetVariants = {
  default: 'touch-target',
  lg: 'touch-target-lg',
} as const;

// Visibility variants
export const visibilityVariants = {
  'mobile-only': 'mobile-only',
  'tablet-up': 'tablet-up',
  'desktop-only': 'desktop-only',
  'mobile-tablet': 'mobile-tablet',
} as const;

// Layout utility variants
export const layoutUtilityVariants = {
  'mobile-stack': 'mobile-stack',
  'mobile-center': 'mobile-center',
  'mobile-full-width': 'mobile-full-width',
} as const;

// Image variants
export const imageResponsiveVariants = {
  default: 'img-responsive',
  square: 'img-responsive-square',
  video: 'img-responsive-video',
} as const;

// Type definitions for better TypeScript support
export type ContainerVariant = keyof typeof containerVariants;
export type SectionPaddingVariant = keyof typeof sectionPaddingVariants;
export type TextResponsiveVariant = keyof typeof textResponsiveVariants;
export type HeadingResponsiveVariant = keyof typeof headingResponsiveVariants;
export type SpaceResponsiveVariant = keyof typeof spaceResponsiveVariants;
export type MarginResponsiveVariant = keyof typeof marginResponsiveVariants;
export type PaddingResponsiveVariant = keyof typeof paddingResponsiveVariants;
export type GapResponsiveVariant = keyof typeof gapResponsiveVariants;
export type GridResponsiveVariant = keyof typeof gridResponsiveVariants;
export type FlexResponsiveVariant = keyof typeof flexResponsiveVariants;
export type ButtonResponsiveVariant = keyof typeof buttonResponsiveVariants;
export type CardResponsiveVariant = keyof typeof cardResponsiveVariants;
export type TouchTargetVariant = keyof typeof touchTargetVariants;
export type VisibilityVariant = keyof typeof visibilityVariants;
export type LayoutUtilityVariant = keyof typeof layoutUtilityVariants;
export type ImageResponsiveVariant = keyof typeof imageResponsiveVariants;

// Utility functions for getting class names
export const getContainerClass = (variant: ContainerVariant = 'responsive'): string => {
  return containerVariants[variant];
};

export const getSectionPaddingClass = (variant: SectionPaddingVariant = 'md'): string => {
  return sectionPaddingVariants[variant];
};

export const getTextResponsiveClass = (variant: TextResponsiveVariant = 'base'): string => {
  return textResponsiveVariants[variant];
};

export const getHeadingResponsiveClass = (variant: HeadingResponsiveVariant): string => {
  return headingResponsiveVariants[variant];
};

export const getSpaceResponsiveClass = (variant: SpaceResponsiveVariant = 'md'): string => {
  return spaceResponsiveVariants[variant];
};

export const getMarginResponsiveClass = (variant: MarginResponsiveVariant = 'md'): string => {
  return marginResponsiveVariants[variant];
};

export const getPaddingResponsiveClass = (variant: PaddingResponsiveVariant = 'md'): string => {
  return paddingResponsiveVariants[variant];
};

export const getGapResponsiveClass = (variant: GapResponsiveVariant = 'md'): string => {
  return gapResponsiveVariants[variant];
};

export const getGridResponsiveClass = (variant: GridResponsiveVariant): string => {
  return gridResponsiveVariants[variant];
};

export const getFlexResponsiveClass = (variant: FlexResponsiveVariant): string => {
  return flexResponsiveVariants[variant];
};

export const getButtonResponsiveClass = (variant: ButtonResponsiveVariant = 'md'): string => {
  return buttonResponsiveVariants[variant];
};

export const getCardResponsiveClass = (variant: CardResponsiveVariant = 'default'): string => {
  return cardResponsiveVariants[variant];
};

export const getTouchTargetClass = (variant: TouchTargetVariant = 'default'): string => {
  return touchTargetVariants[variant];
};

export const getVisibilityClass = (variant: VisibilityVariant): string => {
  return visibilityVariants[variant];
};

export const getLayoutUtilityClass = (variant: LayoutUtilityVariant): string => {
  return layoutUtilityVariants[variant];
};

export const getImageResponsiveClass = (variant: ImageResponsiveVariant = 'default'): string => {
  return imageResponsiveVariants[variant];
};

// Breakpoint constants for JavaScript usage
export const breakpoints = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

export type Breakpoint = keyof typeof breakpoints;

// Utility function to check current breakpoint (for client-side usage)
export const getCurrentBreakpoint = (): Breakpoint => {
  if (typeof window === 'undefined') return 'sm';
  
  const width = window.innerWidth;
  
  if (width >= breakpoints['2xl']) return '2xl';
  if (width >= breakpoints.xl) return 'xl';
  if (width >= breakpoints.lg) return 'lg';
  if (width >= breakpoints.md) return 'md';
  return 'sm';
};

// Utility function to check if current viewport matches a breakpoint
export const isBreakpoint = (breakpoint: Breakpoint): boolean => {
  if (typeof window === 'undefined') return false;
  return window.innerWidth >= breakpoints[breakpoint];
};

// Common responsive patterns as utility functions
export const getResponsiveGridCols = (mobile: number = 1, tablet: number = 2, desktop: number = 3): string => {
  return `grid-cols-${mobile} md:grid-cols-${tablet} lg:grid-cols-${desktop}`;
};

export const getResponsiveTextSize = (mobile: string, tablet?: string, desktop?: string): string => {
  let classes = `text-${mobile}`;
  if (tablet) classes += ` md:text-${tablet}`;
  if (desktop) classes += ` lg:text-${desktop}`;
  return classes;
};

export const getResponsivePadding = (mobile: string, tablet?: string, desktop?: string): string => {
  let classes = `p-${mobile}`;
  if (tablet) classes += ` md:p-${tablet}`;
  if (desktop) classes += ` lg:p-${desktop}`;
  return classes;
};

export const getResponsiveMargin = (mobile: string, tablet?: string, desktop?: string): string => {
  let classes = `m-${mobile}`;
  if (tablet) classes += ` md:m-${tablet}`;
  if (desktop) classes += ` lg:m-${desktop}`;
  return classes;
};