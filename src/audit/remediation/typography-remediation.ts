import { Issue, FileInfo, IssueType, Severity } from '../types/index';
import { DesignTokenSystem, TypographyTokens } from '../design-tokens/types';
import { auditLogger } from '../core/logger';

export interface TypographyRemediationResult {
  success: boolean;
  filesModified: string[];
  replacements: TypographyReplacement[];
  errors: RemediationError[];
  summary: {
    totalReplacements: number;
    filesAffected: number;
    tokensCreated: number;
    hierarchyCorrections: number;
    lineHeightStandardizations: number;
  };
}

export interface TypographyReplacement {
  filePath: string;
  lineNumber: number;
  columnStart: number;
  columnEnd: number;
  originalValue: string;
  tokenValue: string;
  property: string;
  context: string;
  replacementType: 'font-size' | 'font-family' | 'font-weight' | 'line-height' | 'hierarchy-correction';
}

export interface RemediationError {
  filePath: string;
  lineNumber: number;
  error: string;
  originalValue: string;
  property: string;
}

export interface TypographyHierarchyCorrection {
  element: string;
  property: string;
  currentValue: string;
  correctedValue: string;
  tokenName: string;
  location: {
    filePath: string;
    lineNumber: number;
    columnNumber: number;
  };
}

export interface LineHeightStandardization {
  currentValue: string;
  standardizedValue: string;
  tokenName: string;
  location: {
    filePath: string;
    lineNumber: number;
    columnStart: number;
    columnEnd: number;
  };
}

export class TypographyRemediationEngine {
  private fontSizeMap = new Map<string, string>();
  private fontFamilyMap = new Map<string, string>();
  private fontWeightMap = new Map<string, string>();
  private lineHeightMap = new Map<string, string>();
  private hierarchyRules = new Map<string, TypographyHierarchyRule>();
  private standardLineHeights = [1, 1.2, 1.25, 1.3, 1.4, 1.5, 1.6, 1.75, 2];

  constructor(designTokens: DesignTokenSystem) {
    this.buildTypographyMaps(designTokens);
    this.initializeHierarchyRules();
  }

  /**
   * Main remediation method that handles all typography issues
   */
  async remediateTypographyIssues(issues: Issue[], files: FileInfo[]): Promise<TypographyRemediationResult> {
    auditLogger.info('Starting typography remediation', { 
      issueCount: issues.length, 
      fileCount: files.length 
    });

    const typographyIssues = issues.filter(issue => 
      issue.type === IssueType.TYPOGRAPHY_INCONSISTENCY && issue.autoFixable
    );

    const result: TypographyRemediationResult = {
      success: true,
      filesModified: [],
      replacements: [],
      errors: [],
      summary: { 
        totalReplacements: 0, 
        filesAffected: 0, 
        tokensCreated: 0,
        hierarchyCorrections: 0,
        lineHeightStandardizations: 0
      }
    };

    // Process each file for typography remediation
    for (const file of files) {
      try {
        const fileReplacements = await this.processFileForRemediation(file, typographyIssues);
        result.replacements.push(...fileReplacements);
        
        if (fileReplacements.length > 0 && !result.filesModified.includes(file.path)) {
          result.filesModified.push(file.path);
        }
      } catch (error) {
        result.errors.push({
          filePath: file.path,
          lineNumber: 0,
          error: `Failed to process file: ${(error as Error).message}`,
          originalValue: '',
          property: 'file-processing'
        });
        result.success = false;
      }
    }

    // Apply replacements to file contents
    await this.applyReplacements(result.replacements, files);

    // Calculate summary
    result.summary = {
      totalReplacements: result.replacements.length,
      filesAffected: result.filesModified.length,
      tokensCreated: this.countUniqueTokens(result.replacements),
      hierarchyCorrections: result.replacements.filter(r => r.replacementType === 'hierarchy-correction').length,
      lineHeightStandardizations: result.replacements.filter(r => r.replacementType === 'line-height').length
    };

    auditLogger.info('Typography remediation completed', result.summary);
    return result;
  }

  /**
   * Standardize font sizes by converting hardcoded values to design tokens
   */
  async standardizeFontSizes(files: FileInfo[]): Promise<TypographyReplacement[]> {
    const replacements: TypographyReplacement[] = [];

    auditLogger.info('Starting font size standardization', { fileCount: files.length });

    for (const file of files) {
      const lines = file.content.split('\n');
      
      for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
        const line = lines[lineIndex];
        const lineNumber = lineIndex + 1;

        // Skip comments
        if (this.isCommentLine(line)) continue;

        // Find CSS font-size declarations
        const cssMatches = this.findCSSFontSizes(line, lineNumber, file.path);
        replacements.push(...cssMatches);

        // Find React inline style font sizes
        const reactMatches = this.findReactFontSizes(line, lineNumber, file.path);
        replacements.push(...reactMatches);

        // Find Tailwind-style font sizes
        const tailwindMatches = this.findTailwindFontSizes(line, lineNumber, file.path);
        replacements.push(...tailwindMatches);
      }
    }

    auditLogger.info('Font size standardization completed', { 
      replacements: replacements.length 
    });

    return replacements;
  }

  /**
   * Correct typography hierarchy violations
   */
  async correctTypographyHierarchy(files: FileInfo[]): Promise<TypographyHierarchyCorrection[]> {
    const corrections: TypographyHierarchyCorrection[] = [];

    auditLogger.info('Starting typography hierarchy correction', { fileCount: files.length });

    for (const file of files) {
      const lines = file.content.split('\n');
      
      for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
        const line = lines[lineIndex];
        const lineNumber = lineIndex + 1;

        // Find heading elements
        const headingMatches = this.findHeadingElements(line, lineNumber, file.path);
        
        for (const heading of headingMatches) {
          const rule = this.hierarchyRules.get(heading.element);
          if (rule) {
            const hierarchyCorrections = this.generateHierarchyCorrections(heading, rule);
            corrections.push(...hierarchyCorrections);
          }
        }
      }
    }

    auditLogger.info('Typography hierarchy correction completed', { 
      corrections: corrections.length 
    });

    return corrections;
  }

  /**
   * Standardize line-height values to consistent scale
   */
  async standardizeLineHeights(files: FileInfo[]): Promise<LineHeightStandardization[]> {
    const standardizations: LineHeightStandardization[] = [];

    auditLogger.info('Starting line-height standardization', { fileCount: files.length });

    for (const file of files) {
      const lines = file.content.split('\n');
      
      for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
        const line = lines[lineIndex];
        const lineNumber = lineIndex + 1;

        // Skip comments
        if (this.isCommentLine(line)) continue;

        // Find line-height declarations
        const lineHeightMatches = this.findLineHeightDeclarations(line, lineNumber, file.path);
        
        for (const match of lineHeightMatches) {
          const standardized = this.getStandardizedLineHeight(match.value);
          if (standardized && standardized !== match.value) {
            standardizations.push({
              currentValue: match.value,
              standardizedValue: standardized,
              tokenName: this.getLineHeightTokenName(standardized),
              location: {
                filePath: file.path,
                lineNumber,
                columnStart: match.columnStart,
                columnEnd: match.columnEnd
              }
            });
          }
        }
      }
    }

    auditLogger.info('Line-height standardization completed', { 
      standardizations: standardizations.length 
    });

    return standardizations;
  }

  /**
   * Validate typography token usage across files
   */
  async validateTypographyTokenUsage(files: FileInfo[]): Promise<{
    validUsages: number;
    invalidUsages: number;
    suggestions: string[];
    coverage: {
      fontSize: number;
      fontFamily: number;
      fontWeight: number;
      lineHeight: number;
    };
  }> {
    let validUsages = 0;
    let invalidUsages = 0;
    const suggestions: string[] = [];
    const coverage = { fontSize: 0, fontFamily: 0, fontWeight: 0, lineHeight: 0 };

    for (const file of files) {
      // Check font-size token usage
      const fontSizeTokens = file.content.match(/var\(--font-size-[^)]+\)/g) || [];
      fontSizeTokens.forEach(token => {
        if (this.isValidFontSizeToken(token)) {
          validUsages++;
          coverage.fontSize++;
        } else {
          invalidUsages++;
          suggestions.push(`Invalid font-size token '${token}' in ${file.path}`);
        }
      });

      // Check font-family token usage
      const fontFamilyTokens = file.content.match(/var\(--font-family-[^)]+\)/g) || [];
      fontFamilyTokens.forEach(token => {
        if (this.isValidFontFamilyToken(token)) {
          validUsages++;
          coverage.fontFamily++;
        } else {
          invalidUsages++;
          suggestions.push(`Invalid font-family token '${token}' in ${file.path}`);
        }
      });

      // Check font-weight token usage
      const fontWeightTokens = file.content.match(/var\(--font-weight-[^)]+\)/g) || [];
      fontWeightTokens.forEach(token => {
        if (this.isValidFontWeightToken(token)) {
          validUsages++;
          coverage.fontWeight++;
        } else {
          invalidUsages++;
          suggestions.push(`Invalid font-weight token '${token}' in ${file.path}`);
        }
      });

      // Check line-height token usage
      const lineHeightTokens = file.content.match(/var\(--line-height-[^)]+\)/g) || [];
      lineHeightTokens.forEach(token => {
        if (this.isValidLineHeightToken(token)) {
          validUsages++;
          coverage.lineHeight++;
        } else {
          invalidUsages++;
          suggestions.push(`Invalid line-height token '${token}' in ${file.path}`);
        }
      });
    }

    return { validUsages, invalidUsages, suggestions, coverage };
  }

  private async processFileForRemediation(file: FileInfo, issues: Issue[]): Promise<TypographyReplacement[]> {
    const replacements: TypographyReplacement[] = [];
    const fileIssues = issues.filter(issue => issue.location.filePath === file.path);

    for (const issue of fileIssues) {
      try {
        const replacement = this.createReplacementFromIssue(issue, file);
        if (replacement) {
          replacements.push(replacement);
        }
      } catch (error) {
        // Log error but continue processing other issues
        auditLogger.error('Failed to create replacement from issue', error as Error, {
          issueId: issue.id,
          filePath: file.path
        });
      }
    }

    return replacements;
  }

  private createReplacementFromIssue(issue: Issue, file: FileInfo): TypographyReplacement | null {
    const line = file.content.split('\n')[issue.location.lineNumber - 1];
    if (!line) return null;

    // Extract typography value from issue description or context
    const typographyValue = this.extractTypographyValueFromIssue(issue);
    if (!typographyValue) return null;

    // Determine property type
    const property = this.determineTypographyProperty(issue, line);
    if (!property) return null;

    // Find best token match
    const tokenValue = this.findBestTypographyToken(typographyValue, property);
    if (!tokenValue) return null;

    // Find position in line
    const position = this.findValuePosition(line, typographyValue);
    if (!position) return null;

    return {
      filePath: file.path,
      lineNumber: issue.location.lineNumber,
      columnStart: position.start,
      columnEnd: position.end,
      originalValue: typographyValue,
      tokenValue,
      property,
      context: line.trim(),
      replacementType: property as TypographyReplacement['replacementType']
    };
  }

  private findCSSFontSizes(line: string, lineNumber: number, filePath: string): TypographyReplacement[] {
    const replacements: TypographyReplacement[] = [];
    const cssPattern = /font-size\s*:\s*(\d+(?:\.\d+)?)(px|rem|em)/g;
    let match: RegExpExecArray | null;

    while ((match = cssPattern.exec(line)) !== null) {
      const value = match[1];
      const unit = match[2];
      const fullValue = `${value}${unit}`;
      
      const tokenValue = this.findBestFontSizeToken(fullValue);
      if (tokenValue) {
        replacements.push({
          filePath,
          lineNumber,
          columnStart: match.index,
          columnEnd: match.index + match[0].length,
          originalValue: fullValue,
          tokenValue,
          property: 'font-size',
          context: line.trim(),
          replacementType: 'font-size'
        });
      }
    }

    return replacements;
  }

  private findReactFontSizes(line: string, lineNumber: number, filePath: string): TypographyReplacement[] {
    const replacements: TypographyReplacement[] = [];
    const reactPattern = /fontSize\s*:\s*['"`](\d+(?:\.\d+)?)(px|rem|em)['"`]/g;
    let match: RegExpExecArray | null;

    while ((match = reactPattern.exec(line)) !== null) {
      const value = match[1];
      const unit = match[2];
      const fullValue = `${value}${unit}`;
      
      const tokenValue = this.findBestFontSizeToken(fullValue);
      if (tokenValue) {
        replacements.push({
          filePath,
          lineNumber,
          columnStart: match.index,
          columnEnd: match.index + match[0].length,
          originalValue: `fontSize: '${fullValue}'`,
          tokenValue: `fontSize: '${tokenValue}'`,
          property: 'font-size',
          context: line.trim(),
          replacementType: 'font-size'
        });
      }
    }

    return replacements;
  }

  private findTailwindFontSizes(line: string, lineNumber: number, filePath: string): TypographyReplacement[] {
    const replacements: TypographyReplacement[] = [];
    // This would handle Tailwind classes like text-lg, text-xl, etc.
    // For now, we'll focus on CSS and inline styles
    return replacements;
  }

  private findHeadingElements(line: string, lineNumber: number, filePath: string): Array<{
    element: string;
    styles: Record<string, string>;
    position: { start: number; end: number };
  }> {
    const headings: Array<{
      element: string;
      styles: Record<string, string>;
      position: { start: number; end: number };
    }> = [];
    
    // Find JSX heading elements with inline styles
    const jsxHeadingPattern = /<(h[1-6])\s+style\s*=\s*\{\{([^}]+)\}\}/gi;
    let match: RegExpExecArray | null;
    
    while ((match = jsxHeadingPattern.exec(line)) !== null) {
      const element = match[1].toLowerCase();
      const styleContent = match[2];
      const styles = this.parseJSXInlineStyles(styleContent);
      
      headings.push({
        element,
        styles,
        position: { start: match.index, end: match.index + match[0].length }
      });
    }

    // Also find regular HTML heading elements
    const htmlHeadingPattern = /<(h[1-6])([^>]*)>/gi;
    while ((match = htmlHeadingPattern.exec(line)) !== null) {
      const element = match[1].toLowerCase();
      const attributes = match[2];
      const styles = this.extractInlineStyles(attributes);
      
      // Only add if we found some styles or if it's not already added by JSX pattern
      if (Object.keys(styles).length > 0) {
        headings.push({
          element,
          styles,
          position: { start: match.index, end: match.index + match[0].length }
        });
      }
    }

    return headings;
  }

  private findLineHeightDeclarations(line: string, lineNumber: number, filePath: string): Array<{
    value: string;
    columnStart: number;
    columnEnd: number;
  }> {
    const declarations: Array<{
      value: string;
      columnStart: number;
      columnEnd: number;
    }> = [];
    
    // CSS line-height declarations
    const cssPattern = /line-height\s*:\s*([^;]+)/g;
    let match: RegExpExecArray | null;
    
    while ((match = cssPattern.exec(line)) !== null) {
      const value = match[1].trim();
      declarations.push({
        value,
        columnStart: match.index,
        columnEnd: match.index + match[0].length
      });
    }

    // React inline styles
    const reactPattern = /lineHeight\s*:\s*['"`]?([^'"`\s,}]+)['"`]?/g;
    while ((match = reactPattern.exec(line)) !== null) {
      const value = match[1];
      declarations.push({
        value,
        columnStart: match.index,
        columnEnd: match.index + match[0].length
      });
    }

    return declarations;
  }

  private generateHierarchyCorrections(
    heading: { element: string; styles: Record<string, string>; position: { start: number; end: number } },
    rule: TypographyHierarchyRule
  ): TypographyHierarchyCorrection[] {
    const corrections: TypographyHierarchyCorrection[] = [];

    // Check font size
    if (heading.styles.fontSize && heading.styles.fontSize !== rule.fontSize) {
      corrections.push({
        element: heading.element,
        property: 'fontSize',
        currentValue: heading.styles.fontSize,
        correctedValue: rule.fontSize,
        tokenName: this.getFontSizeTokenName(rule.fontSize),
        location: {
          filePath: '',
          lineNumber: 0,
          columnNumber: heading.position.start
        }
      });
    }

    // Check font weight
    if (heading.styles.fontWeight && heading.styles.fontWeight !== rule.fontWeight) {
      corrections.push({
        element: heading.element,
        property: 'fontWeight',
        currentValue: heading.styles.fontWeight,
        correctedValue: rule.fontWeight,
        tokenName: this.getFontWeightTokenName(rule.fontWeight),
        location: {
          filePath: '',
          lineNumber: 0,
          columnNumber: heading.position.start
        }
      });
    }

    // Check line height
    if (heading.styles.lineHeight && heading.styles.lineHeight !== rule.lineHeight) {
      corrections.push({
        element: heading.element,
        property: 'lineHeight',
        currentValue: heading.styles.lineHeight,
        correctedValue: rule.lineHeight,
        tokenName: this.getLineHeightTokenName(rule.lineHeight),
        location: {
          filePath: '',
          lineNumber: 0,
          columnNumber: heading.position.start
        }
      });
    }

    return corrections;
  }

  private async applyReplacements(replacements: TypographyReplacement[], files: FileInfo[]): Promise<void> {
    // Group replacements by file
    const replacementsByFile = new Map<string, TypographyReplacement[]>();
    
    replacements.forEach(replacement => {
      if (!replacementsByFile.has(replacement.filePath)) {
        replacementsByFile.set(replacement.filePath, []);
      }
      replacementsByFile.get(replacement.filePath)!.push(replacement);
    });

    // Apply replacements to each file
    for (const [filePath, fileReplacements] of replacementsByFile.entries()) {
      const file = files.find(f => f.path === filePath);
      if (!file) continue;

      // Sort replacements by position (reverse order to avoid offset issues)
      const sortedReplacements = fileReplacements.sort((a, b) => {
        if (a.lineNumber !== b.lineNumber) {
          return b.lineNumber - a.lineNumber;
        }
        return b.columnStart - a.columnStart;
      });

      // Apply each replacement
      const lines = file.content.split('\n');
      
      for (const replacement of sortedReplacements) {
        const lineIndex = replacement.lineNumber - 1;
        if (lineIndex >= 0 && lineIndex < lines.length) {
          const line = lines[lineIndex];
          const before = line.substring(0, replacement.columnStart);
          const after = line.substring(replacement.columnEnd);
          lines[lineIndex] = before + replacement.tokenValue + after;
        }
      }

      file.content = lines.join('\n');
    }
  }

  private buildTypographyMaps(designTokens: DesignTokenSystem): void {
    const typography = designTokens.typography;

    // Build font size map
    Object.entries(typography.fontSizes).forEach(([key, value]) => {
      this.fontSizeMap.set(value, `var(--font-size-${key})`);
    });

    // Build font family map
    Object.entries(typography.fontFamilies).forEach(([key, value]) => {
      const familyString = Array.isArray(value) ? value.join(', ') : value;
      this.fontFamilyMap.set(familyString, `var(--font-family-${key})`);
    });

    // Build font weight map
    Object.entries(typography.fontWeights).forEach(([key, value]) => {
      this.fontWeightMap.set(value.toString(), `var(--font-weight-${key})`);
    });

    // Build line height map
    Object.entries(typography.lineHeights).forEach(([key, value]) => {
      this.lineHeightMap.set(value.toString(), `var(--line-height-${key})`);
    });
  }

  private initializeHierarchyRules(): void {
    const rules: TypographyHierarchyRule[] = [
      { element: 'h1', fontSize: '2.25rem', lineHeight: '1.2', fontWeight: '700' },
      { element: 'h2', fontSize: '1.875rem', lineHeight: '1.3', fontWeight: '600' },
      { element: 'h3', fontSize: '1.5rem', lineHeight: '1.4', fontWeight: '600' },
      { element: 'h4', fontSize: '1.25rem', lineHeight: '1.4', fontWeight: '500' },
      { element: 'h5', fontSize: '1.125rem', lineHeight: '1.5', fontWeight: '500' },
      { element: 'h6', fontSize: '1rem', lineHeight: '1.5', fontWeight: '500' }
    ];

    rules.forEach(rule => {
      this.hierarchyRules.set(rule.element, rule);
    });
  }

  private findBestFontSizeToken(value: string): string | null {
    // Direct match
    const directMatch = this.fontSizeMap.get(value);
    if (directMatch) return directMatch;

    // Convert to rem for comparison
    const remValue = this.convertToRem(value);
    if (!remValue) return null;

    // Find closest match
    let closestToken: string | null = null;
    let closestDistance = Infinity;

    this.fontSizeMap.forEach((token, tokenValue) => {
      const tokenRem = this.convertToRem(tokenValue);
      if (tokenRem) {
        const distance = Math.abs(parseFloat(remValue) - parseFloat(tokenRem));
        if (distance < closestDistance && distance < 0.125) { // Within 0.125rem
          closestDistance = distance;
          closestToken = token;
        }
      }
    });

    return closestToken;
  }

  private findBestTypographyToken(value: string, property: string): string | null {
    switch (property) {
      case 'font-size':
        return this.findBestFontSizeToken(value);
      case 'font-family':
        return this.fontFamilyMap.get(value) || null;
      case 'font-weight':
        return this.fontWeightMap.get(value) || null;
      case 'line-height':
        return this.lineHeightMap.get(value) || null;
      default:
        return null;
    }
  }

  private getStandardizedLineHeight(value: string): string | null {
    const numericValue = parseFloat(value);
    
    if (!isNaN(numericValue)) {
      // Find closest standard line-height
      const closest = this.standardLineHeights.reduce((prev, curr) => 
        Math.abs(curr - numericValue) < Math.abs(prev - numericValue) ? curr : prev
      );
      
      // Only suggest change if the difference is significant
      if (Math.abs(closest - numericValue) > 0.05) {
        return closest.toString();
      }
    }

    return null;
  }

  private getLineHeightTokenName(value: string): string {
    const lineHeightMap: Record<string, string> = {
      '1': 'none',
      '1.2': 'tight',
      '1.25': 'snug',
      '1.4': 'normal',
      '1.5': 'relaxed',
      '1.75': 'loose'
    };

    return lineHeightMap[value] || 'custom';
  }

  private getFontSizeTokenName(value: string): string {
    // Reverse lookup in font size map
    for (const [tokenValue, tokenName] of this.fontSizeMap.entries()) {
      if (tokenValue === value) {
        return tokenName.replace('var(--font-size-', '').replace(')', '');
      }
    }
    return 'custom';
  }

  private getFontWeightTokenName(value: string): string {
    const weightMap: Record<string, string> = {
      '100': 'thin',
      '200': 'extralight',
      '300': 'light',
      '400': 'normal',
      '500': 'medium',
      '600': 'semibold',
      '700': 'bold',
      '800': 'extrabold',
      '900': 'black'
    };

    return weightMap[value] || 'custom';
  }

  private convertToRem(value: string): string | null {
    const match = value.match(/^(\d+(?:\.\d+)?)(px|rem|em)$/);
    if (!match) return null;

    const num = parseFloat(match[1]);
    const unit = match[2];

    switch (unit) {
      case 'rem':
        return `${num}rem`;
      case 'px':
        return `${num / 16}rem`; // Assuming 16px base
      case 'em':
        return `${num}rem`; // Approximate conversion
      default:
        return null;
    }
  }

  private extractTypographyValueFromIssue(issue: Issue): string | null {
    // Try to extract from description
    const descriptionMatch = issue.description.match(/'([^']+)'/);
    if (descriptionMatch) return descriptionMatch[1];

    // Try to extract from context
    const contextMatch = issue.location.context.match(/:\s*([^;,}]+)/);
    if (contextMatch) return contextMatch[1].trim();

    return null;
  }

  private determineTypographyProperty(issue: Issue, line: string): string | null {
    if (issue.description.includes('font-size') || line.includes('font-size') || line.includes('fontSize')) {
      return 'font-size';
    }
    if (issue.description.includes('font-family') || line.includes('font-family') || line.includes('fontFamily')) {
      return 'font-family';
    }
    if (issue.description.includes('font-weight') || line.includes('font-weight') || line.includes('fontWeight')) {
      return 'font-weight';
    }
    if (issue.description.includes('line-height') || line.includes('line-height') || line.includes('lineHeight')) {
      return 'line-height';
    }
    return null;
  }

  private findValuePosition(line: string, value: string): { start: number; end: number } | null {
    const index = line.indexOf(value);
    if (index === -1) return null;
    
    return {
      start: index,
      end: index + value.length
    };
  }

  private extractInlineStyles(attributes: string): Record<string, string> {
    const styles: Record<string, string> = {};
    
    const styleMatch = attributes.match(/style\s*=\s*['"`]([^'"`]+)['"`]/);
    if (styleMatch) {
      const styleContent = styleMatch[1];
      const declarations = styleContent.split(';');
      
      declarations.forEach(declaration => {
        const [property, value] = declaration.split(':').map(s => s.trim());
        if (property && value) {
          styles[this.toCamelCase(property)] = value;
        }
      });
    }

    return styles;
  }

  private parseJSXInlineStyles(styleContent: string): Record<string, string> {
    const styles: Record<string, string> = {};
    
    // Parse JSX inline styles like { fontSize: '16px', fontWeight: '600' }
    const declarations = styleContent.split(',');
    
    declarations.forEach(declaration => {
      const match = declaration.match(/(\w+)\s*:\s*['"`]?([^'"`\s,}]+)['"`]?/);
      if (match) {
        const [, property, value] = match;
        styles[property.trim()] = value.trim();
      }
    });

    return styles;
  }

  private isCommentLine(line: string): boolean {
    const trimmed = line.trim();
    return trimmed.startsWith('//') || 
           trimmed.startsWith('/*') || 
           trimmed.startsWith('*') ||
           trimmed.startsWith('<!--');
  }

  private toCamelCase(str: string): string {
    return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
  }

  private countUniqueTokens(replacements: TypographyReplacement[]): number {
    const uniqueTokens = new Set(replacements.map(r => r.tokenValue));
    return uniqueTokens.size;
  }

  private isValidFontSizeToken(token: string): boolean {
    const tokenName = token.match(/var\(--font-size-([^)]+)\)/)?.[1];
    if (!tokenName) return false;

    const validSizes = ['xs', 'sm', 'base', 'lg', 'xl', '2xl', '3xl', '4xl', '5xl', '6xl', '7xl', '8xl', '9xl'];
    return validSizes.includes(tokenName);
  }

  private isValidFontFamilyToken(token: string): boolean {
    const tokenName = token.match(/var\(--font-family-([^)]+)\)/)?.[1];
    if (!tokenName) return false;

    const validFamilies = ['sans', 'serif', 'mono', 'display'];
    return validFamilies.includes(tokenName);
  }

  private isValidFontWeightToken(token: string): boolean {
    const tokenName = token.match(/var\(--font-weight-([^)]+)\)/)?.[1];
    if (!tokenName) return false;

    const validWeights = ['thin', 'extralight', 'light', 'normal', 'medium', 'semibold', 'bold', 'extrabold', 'black'];
    return validWeights.includes(tokenName);
  }

  private isValidLineHeightToken(token: string): boolean {
    const tokenName = token.match(/var\(--line-height-([^)]+)\)/)?.[1];
    if (!tokenName) return false;

    const validLineHeights = ['none', 'tight', 'snug', 'normal', 'relaxed', 'loose'];
    return validLineHeights.includes(tokenName);
  }
}

interface TypographyHierarchyRule {
  element: string;
  fontSize: string;
  lineHeight: string;
  fontWeight: string;
}