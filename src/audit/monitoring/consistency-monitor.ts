import { MonitoringConfig, MonitoringResult, Alert, Issue, ComplianceMetrics } from './types';
import { AuditEngine } from '../core/audit-engine';
import { AuditLogger } from '../core/logger';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as crypto from 'crypto';

export class ConsistencyMonitor {
  private config: MonitoringConfig;
  private auditEngine: AuditEngine;
  private logger: AuditLogger;
  private previousResults: Issue[] = [];
  private monitoringInterval?: NodeJS.Timeout;

  constructor(config: MonitoringConfig, auditEngine: AuditEngine) {
    this.config = config;
    this.auditEngine = auditEngine;
    this.logger = new AuditLogger();
  }

  /**
   * Start continuous monitoring
   */
  public startMonitoring(): void {
    if (!this.config.enabled) {
      this.logger.info('Monitoring is disabled');
      return;
    }

    this.logger.info(`Starting consistency monitoring with ${this.config.checkInterval}ms interval`);
    
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.performCheck();
      } catch (error) {
        this.logger.error('Error during monitoring check:', error);
      }
    }, this.config.checkInterval);
  }

  /**
   * Stop continuous monitoring
   */
  public stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
      this.logger.info('Stopped consistency monitoring');
    }
  }

  /**
   * Perform a single monitoring check
   */
  public async performCheck(projectPath: string = process.cwd()): Promise<MonitoringResult> {
    this.logger.info('Performing consistency check...');

    // Run audit
    const auditResult = await this.auditEngine.scanProject(projectPath);
    const currentIssues = auditResult.issues;

    // Compare with previous results
    const result = this.compareResults(currentIssues);

    // Check thresholds and generate alerts
    const alerts = this.checkThresholds(result);
    
    // Send notifications if needed
    if (alerts.length > 0) {
      await this.sendAlerts(alerts);
    }

    // Store results for next comparison
    this.previousResults = currentIssues;

    // Save monitoring data
    await this.saveMonitoringData(result);

    this.logger.info(`Check completed: ${result.totalIssues} total issues, ${result.newIssues.length} new, ${result.resolvedIssues.length} resolved`);

    return result;
  }

  /**
   * Compare current results with previous results
   */
  private compareResults(currentIssues: Issue[]): MonitoringResult {
    const currentIssueHashes = new Set(currentIssues.map(issue => this.getIssueHash(issue)));
    const previousIssueHashes = new Set(this.previousResults.map(issue => this.getIssueHash(issue)));

    const newIssues = currentIssues.filter(issue => 
      !previousIssueHashes.has(this.getIssueHash(issue))
    );

    const resolvedIssues = this.previousResults.filter(issue => 
      !currentIssueHashes.has(this.getIssueHash(issue))
    );

    const severityBreakdown = this.calculateSeverityBreakdown(currentIssues);
    const complianceScore = this.calculateComplianceScore(currentIssues);
    const trend = this.calculateTrend(currentIssues.length, this.previousResults.length);

    return {
      timestamp: new Date(),
      newIssues,
      resolvedIssues,
      totalIssues: currentIssues.length,
      severityBreakdown,
      complianceScore,
      trend
    };
  }

  /**
   * Generate a hash for an issue to track it across runs
   */
  private getIssueHash(issue: Issue): string {
    const hashInput = `${issue.type}-${issue.location.filePath}-${issue.location.lineNumber}-${issue.description}`;
    return crypto.createHash('md5').update(hashInput).digest('hex');
  }

  /**
   * Calculate severity breakdown
   */
  private calculateSeverityBreakdown(issues: Issue[]): Record<string, number> {
    const breakdown: Record<string, number> = {
      low: 0,
      medium: 0,
      high: 0,
      critical: 0
    };

    issues.forEach(issue => {
      breakdown[issue.severity]++;
    });

    return breakdown;
  }

  /**
   * Calculate compliance score (0-100)
   */
  private calculateComplianceScore(issues: Issue[]): number {
    // Simple scoring: reduce score based on issue severity
    const weights = { low: 1, medium: 2, high: 4, critical: 8 };
    const totalWeight = issues.reduce((sum, issue) => sum + weights[issue.severity], 0);
    
    // Assume base score of 100, reduce based on weighted issues
    const maxPenalty = 100;
    const penalty = Math.min(totalWeight * 2, maxPenalty);
    
    return Math.max(0, 100 - penalty);
  }

  /**
   * Calculate trend based on issue count changes
   */
  private calculateTrend(currentCount: number, previousCount: number): 'improving' | 'degrading' | 'stable' {
    const threshold = 2; // Allow small fluctuations
    
    if (currentCount < previousCount - threshold) {
      return 'improving';
    } else if (currentCount > previousCount + threshold) {
      return 'degrading';
    } else {
      return 'stable';
    }
  }

  /**
   * Check if thresholds are exceeded and generate alerts
   */
  private checkThresholds(result: MonitoringResult): Alert[] {
    const alerts: Alert[] = [];

    // Check error count threshold
    if (result.totalIssues > this.config.thresholds.errorCount) {
      alerts.push({
        id: crypto.randomUUID(),
        type: 'threshold_exceeded',
        severity: 'high',
        message: `Total issue count (${result.totalIssues}) exceeds threshold (${this.config.thresholds.errorCount})`,
        timestamp: new Date(),
        metadata: { issueCount: result.totalIssues, threshold: this.config.thresholds.errorCount }
      });
    }

    // Check critical issues
    const criticalCount = result.severityBreakdown.critical || 0;
    if (criticalCount > this.config.thresholds.criticalSeverityLimit) {
      alerts.push({
        id: crypto.randomUUID(),
        type: 'new_critical_issue',
        severity: 'critical',
        message: `Critical issues (${criticalCount}) exceed limit (${this.config.thresholds.criticalSeverityLimit})`,
        timestamp: new Date(),
        metadata: { criticalCount, limit: this.config.thresholds.criticalSeverityLimit }
      });
    }

    // Check compliance degradation
    if (result.trend === 'degrading') {
      alerts.push({
        id: crypto.randomUUID(),
        type: 'compliance_degraded',
        severity: 'medium',
        message: `Design system compliance is degrading (score: ${result.complianceScore})`,
        timestamp: new Date(),
        metadata: { complianceScore: result.complianceScore, trend: result.trend }
      });
    }

    return alerts;
  }

  /**
   * Send alerts via configured channels
   */
  private async sendAlerts(alerts: Alert[]): Promise<void> {
    for (const alert of alerts) {
      this.logger.warn(`ALERT: ${alert.message}`);

      // Send to webhook if configured
      if (this.config.notifications.webhook) {
        await this.sendWebhookAlert(alert);
      }

      // Send to Slack if configured
      if (this.config.notifications.slack) {
        await this.sendSlackAlert(alert);
      }
    }
  }

  /**
   * Send alert to webhook
   */
  private async sendWebhookAlert(alert: Alert): Promise<void> {
    try {
      const response = await fetch(this.config.notifications.webhook!, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(alert)
      });

      if (!response.ok) {
        this.logger.error(`Failed to send webhook alert: ${response.statusText}`);
      }
    } catch (error) {
      this.logger.error('Error sending webhook alert:', error);
    }
  }

  /**
   * Send alert to Slack
   */
  private async sendSlackAlert(alert: Alert): Promise<void> {
    try {
      const slackMessage = {
        channel: this.config.notifications.slack!.channel,
        text: `🚨 Design System Alert: ${alert.message}`,
        attachments: [{
          color: this.getSlackColor(alert.severity),
          fields: [
            { title: 'Severity', value: alert.severity, short: true },
            { title: 'Type', value: alert.type, short: true },
            { title: 'Time', value: alert.timestamp.toISOString(), short: true }
          ]
        }]
      };

      const response = await fetch(this.config.notifications.slack!.webhookUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(slackMessage)
      });

      if (!response.ok) {
        this.logger.error(`Failed to send Slack alert: ${response.statusText}`);
      }
    } catch (error) {
      this.logger.error('Error sending Slack alert:', error);
    }
  }

  /**
   * Get Slack color for alert severity
   */
  private getSlackColor(severity: string): string {
    const colors = {
      low: 'good',
      medium: 'warning',
      high: 'danger',
      critical: '#ff0000'
    };
    return colors[severity as keyof typeof colors] || 'warning';
  }

  /**
   * Save monitoring data for historical tracking
   */
  private async saveMonitoringData(result: MonitoringResult): Promise<void> {
    try {
      const dataDir = path.join(process.cwd(), '.audit-monitoring');
      await fs.mkdir(dataDir, { recursive: true });

      const filename = `monitoring-${result.timestamp.toISOString().split('T')[0]}.json`;
      const filepath = path.join(dataDir, filename);

      // Load existing data for the day
      let dayData: MonitoringResult[] = [];
      try {
        const existingData = await fs.readFile(filepath, 'utf-8');
        dayData = JSON.parse(existingData);
      } catch {
        // File doesn't exist, start fresh
      }

      dayData.push(result);
      await fs.writeFile(filepath, JSON.stringify(dayData, null, 2));
    } catch (error) {
      this.logger.error('Error saving monitoring data:', error);
    }
  }

  /**
   * Get compliance metrics for reporting
   */
  public async getComplianceMetrics(days: number = 30): Promise<ComplianceMetrics> {
    const dataDir = path.join(process.cwd(), '.audit-monitoring');
    const trendData: { date: Date; complianceScore: number; issueCount: number; }[] = [];

    try {
      const files = await fs.readdir(dataDir);
      const recentFiles = files
        .filter(f => f.startsWith('monitoring-') && f.endsWith('.json'))
        .sort()
        .slice(-days);

      for (const file of recentFiles) {
        const filepath = path.join(dataDir, file);
        const data = JSON.parse(await fs.readFile(filepath, 'utf-8')) as MonitoringResult[];
        
        // Get the latest result for each day
        const latestResult = data[data.length - 1];
        if (latestResult) {
          trendData.push({
            date: new Date(latestResult.timestamp),
            complianceScore: latestResult.complianceScore,
            issueCount: latestResult.totalIssues
          });
        }
      }
    } catch (error) {
      this.logger.error('Error loading compliance metrics:', error);
    }

    // Calculate current metrics from latest data
    const latestData = trendData[trendData.length - 1];
    const currentIssues = this.previousResults;

    const issuesByCategory: Record<string, number> = {};
    const issuesBySeverity: Record<string, number> = { low: 0, medium: 0, high: 0, critical: 0 };

    currentIssues.forEach(issue => {
      issuesByCategory[issue.category] = (issuesByCategory[issue.category] || 0) + 1;
      issuesBySeverity[issue.severity]++;
    });

    return {
      totalFiles: 0, // Would need file scanner integration
      compliantFiles: 0, // Would need file scanner integration
      compliancePercentage: latestData?.complianceScore || 0,
      issuesByCategory,
      issuesBySeverity,
      trendData
    };
  }
}