/**
 * Design Token Compliance Validator
 * Validates usage of design tokens across the codebase
 */

import { ValidationRule, ValidationViolation, Validator, ValidationResult, ValidationCategory, ValidationFile, FileType } from './types';
import { DesignTokenSystem } from '../design-tokens/types';
import { Severity } from '../types';

export class DesignTokenValidator implements Validator {
  name = 'Design Token Validator';
  category = ValidationCategory.DESIGN_TOKENS;
  rules: ValidationRule[] = [
    new HardcodedColorRule(),
    new HardcodedSpacingRule(),
    new HardcodedTypographyRule(),
    new InvalidTokenUsageRule(),
    new MissingTokenRule()
  ];

  async validate(files: ValidationFile[], tokens?: DesignTokenSystem): Promise<ValidationResult> {
    const startTime = Date.now();
    const violations: ValidationViolation[] = [];

    if (!tokens) {
      throw new Error('Design tokens are required for validation');
    }

    for (const file of files) {
      if (file.type === FileType.COMPONENT || file.type === FileType.STYLE) {
        for (const rule of this.rules) {
          const ruleViolations = rule.validate(file.content, file.path, tokens);
          violations.push(...ruleViolations);
        }
      }
    }

    const executionTime = Date.now() - startTime;

    return {
      isValid: violations.length === 0,
      violations,
      summary: this.generateSummary(violations),
      executionTime
    };
  }

  private generateSummary(violations: ValidationViolation[]) {
    const severityBreakdown = violations.reduce((acc, v) => {
      acc[v.severity] = (acc[v.severity] || 0) + 1;
      return acc;
    }, {} as Record<Severity, number>);

    const ruleBreakdown = violations.reduce((acc, v) => {
      acc[v.rule] = (acc[v.rule] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const fileBreakdown = violations.reduce((acc, v) => {
      acc[v.location.filePath] = (acc[v.location.filePath] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalViolations: violations.length,
      severityBreakdown,
      ruleBreakdown,
      fileBreakdown
    };
  }
}

class HardcodedColorRule implements ValidationRule {
  id = 'hardcoded-color';
  name = 'Hardcoded Color Usage';
  description = 'Detects hardcoded color values that should use design tokens';
  category = ValidationCategory.DESIGN_TOKENS;
  severity = Severity.MEDIUM;

  validate(content: string, filePath: string, tokens?: DesignTokenSystem): ValidationViolation[] {
    const violations: ValidationViolation[] = [];
    const colorPatterns = [
      /#[0-9a-fA-F]{3,8}/g, // Hex colors
      /rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)/g, // RGB colors
      /rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\)/g, // RGBA colors
      /hsl\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*\)/g, // HSL colors
      /hsla\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*,\s*[\d.]+\s*\)/g // HSLA colors
    ];

    const lines = content.split('\n');
    
    lines.forEach((line, lineIndex) => {
      colorPatterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(line)) !== null) {
          const colorValue = match[0];
          const columnIndex = match.index;
          
          // Skip if it's in a comment
          if (this.isInComment(line, columnIndex)) {
            continue;
          }

          violations.push({
            id: `${filePath}:${lineIndex + 1}:${columnIndex}:hardcoded-color`,
            rule: this.id,
            severity: this.severity,
            message: `Hardcoded color value '${colorValue}' should use design token`,
            location: {
              filePath,
              lineNumber: lineIndex + 1,
              columnNumber: columnIndex,
              context: line.trim()
            },
            actualValue: colorValue,
            suggestion: this.suggestToken(colorValue, tokens),
            autoFixable: true
          });
        }
      });
    });

    return violations;
  }

  private isInComment(line: string, position: number): boolean {
    const beforePosition = line.substring(0, position);
    return beforePosition.includes('//') || beforePosition.includes('/*');
  }

  private suggestToken(colorValue: string, tokens?: DesignTokenSystem): string {
    if (!tokens) return 'Use appropriate design token';
    
    // Simple color matching - in a real implementation, you'd want more sophisticated color matching
    const normalizedColor = colorValue.toLowerCase();
    
    if (normalizedColor.includes('ff0000') || normalizedColor.includes('red')) {
      return 'Consider using tokens.colors.semantic.error.default';
    }
    if (normalizedColor.includes('00ff00') || normalizedColor.includes('green')) {
      return 'Consider using tokens.colors.semantic.success.default';
    }
    if (normalizedColor.includes('0000ff') || normalizedColor.includes('blue')) {
      return 'Consider using tokens.colors.primary[500]';
    }
    
    return 'Use appropriate color token from design system';
  }
}

class HardcodedSpacingRule implements ValidationRule {
  id = 'hardcoded-spacing';
  name = 'Hardcoded Spacing Usage';
  description = 'Detects hardcoded spacing values that should use design tokens';
  category = ValidationCategory.DESIGN_TOKENS;
  severity = Severity.MEDIUM;

  validate(content: string, filePath: string, tokens?: DesignTokenSystem): ValidationViolation[] {
    const violations: ValidationViolation[] = [];
    const spacingProperties = ['margin', 'padding', 'gap', 'top', 'right', 'bottom', 'left'];
    const spacingPattern = new RegExp(`(${spacingProperties.join('|')})\\s*:\\s*([0-9]+(?:px|rem|em))`, 'g');

    const lines = content.split('\n');
    
    lines.forEach((line, lineIndex) => {
      let match;
      while ((match = spacingPattern.exec(line)) !== null) {
        const property = match[1];
        const value = match[2];
        const columnIndex = match.index;
        
        // Skip if it's in a comment
        if (this.isInComment(line, columnIndex)) {
          continue;
        }

        violations.push({
          id: `${filePath}:${lineIndex + 1}:${columnIndex}:hardcoded-spacing`,
          rule: this.id,
          severity: this.severity,
          message: `Hardcoded spacing value '${value}' for '${property}' should use design token`,
          location: {
            filePath,
            lineNumber: lineIndex + 1,
            columnNumber: columnIndex,
            context: line.trim()
          },
          actualValue: value,
          suggestion: this.suggestSpacingToken(value, tokens),
          autoFixable: true
        });
      }
    });

    return violations;
  }

  private isInComment(line: string, position: number): boolean {
    const beforePosition = line.substring(0, position);
    return beforePosition.includes('//') || beforePosition.includes('/*');
  }

  private suggestSpacingToken(value: string, tokens?: DesignTokenSystem): string {
    if (!tokens) return 'Use appropriate spacing token';
    
    const numericValue = parseInt(value);
    
    // Map common pixel values to token suggestions
    const spacingMap: Record<number, string> = {
      4: 'tokens.spacing.scale[1]',
      8: 'tokens.spacing.scale[2]',
      12: 'tokens.spacing.scale[3]',
      16: 'tokens.spacing.scale[4]',
      20: 'tokens.spacing.scale[5]',
      24: 'tokens.spacing.scale[6]',
      32: 'tokens.spacing.scale[8]',
      48: 'tokens.spacing.scale[12]',
      64: 'tokens.spacing.scale[16]'
    };

    return spacingMap[numericValue] || 'Use appropriate spacing token from design system';
  }
}

class HardcodedTypographyRule implements ValidationRule {
  id = 'hardcoded-typography';
  name = 'Hardcoded Typography Usage';
  description = 'Detects hardcoded typography values that should use design tokens';
  category = ValidationCategory.DESIGN_TOKENS;
  severity = Severity.MEDIUM;

  validate(content: string, filePath: string, tokens?: DesignTokenSystem): ValidationViolation[] {
    const violations: ValidationViolation[] = [];
    const typographyProperties = ['font-size', 'font-weight', 'line-height', 'font-family'];
    const typographyPattern = new RegExp(`(${typographyProperties.join('|')})\\s*:\\s*([^;\\n}]+)`, 'g');

    const lines = content.split('\n');
    
    lines.forEach((line, lineIndex) => {
      let match;
      while ((match = typographyPattern.exec(line)) !== null) {
        const property = match[1];
        const value = match[2].trim();
        const columnIndex = match.index;
        
        // Skip if it's in a comment or uses CSS variables
        if (this.isInComment(line, columnIndex) || value.includes('var(')) {
          continue;
        }

        violations.push({
          id: `${filePath}:${lineIndex + 1}:${columnIndex}:hardcoded-typography`,
          rule: this.id,
          severity: this.severity,
          message: `Hardcoded typography value '${value}' for '${property}' should use design token`,
          location: {
            filePath,
            lineNumber: lineIndex + 1,
            columnNumber: columnIndex,
            context: line.trim()
          },
          actualValue: value,
          suggestion: this.suggestTypographyToken(property, value, tokens),
          autoFixable: true
        });
      }
    });

    return violations;
  }

  private isInComment(line: string, position: number): boolean {
    const beforePosition = line.substring(0, position);
    return beforePosition.includes('//') || beforePosition.includes('/*');
  }

  private suggestTypographyToken(property: string, value: string, tokens?: DesignTokenSystem): string {
    if (!tokens) return 'Use appropriate typography token';
    
    switch (property) {
      case 'font-size':
        return 'Use tokens.typography.fontSizes or tokens.typography.typeScale';
      case 'font-weight':
        return 'Use tokens.typography.fontWeights';
      case 'line-height':
        return 'Use tokens.typography.lineHeights';
      case 'font-family':
        return 'Use tokens.typography.fontFamilies';
      default:
        return 'Use appropriate typography token from design system';
    }
  }
}

class InvalidTokenUsageRule implements ValidationRule {
  id = 'invalid-token-usage';
  name = 'Invalid Token Usage';
  description = 'Detects usage of non-existent or deprecated design tokens';
  category = ValidationCategory.DESIGN_TOKENS;
  severity = Severity.HIGH;

  validate(content: string, filePath: string, tokens?: DesignTokenSystem): ValidationViolation[] {
    const violations: ValidationViolation[] = [];
    
    // Pattern to match CSS custom properties that look like design tokens
    const tokenPattern = /var\(--[^)]+\)/g;
    const lines = content.split('\n');
    
    lines.forEach((line, lineIndex) => {
      let match;
      while ((match = tokenPattern.exec(line)) !== null) {
        const tokenUsage = match[0];
        const columnIndex = match.index;
        
        if (this.isInComment(line, columnIndex)) {
          continue;
        }

        // Extract token name
        const tokenName = tokenUsage.match(/--([^)]+)/)?.[1];
        
        if (tokenName && !this.isValidToken(tokenName, tokens)) {
          violations.push({
            id: `${filePath}:${lineIndex + 1}:${columnIndex}:invalid-token`,
            rule: this.id,
            severity: this.severity,
            message: `Invalid or non-existent design token '${tokenUsage}'`,
            location: {
              filePath,
              lineNumber: lineIndex + 1,
              columnNumber: columnIndex,
              context: line.trim()
            },
            actualValue: tokenUsage,
            suggestion: 'Use a valid design token from the design system',
            autoFixable: false
          });
        }
      }
    });

    return violations;
  }

  private isInComment(line: string, position: number): boolean {
    const beforePosition = line.substring(0, position);
    return beforePosition.includes('//') || beforePosition.includes('/*');
  }

  private isValidToken(tokenName: string, tokens?: DesignTokenSystem): boolean {
    if (!tokens) return true; // Can't validate without tokens
    
    // This is a simplified validation - in a real implementation,
    // you'd want to maintain a comprehensive list of valid token names
    const validPrefixes = [
      'color-primary',
      'color-secondary',
      'color-semantic',
      'color-neutral',
      'color-surface',
      'spacing',
      'font-size',
      'font-weight',
      'line-height',
      'font-family'
    ];

    return validPrefixes.some(prefix => tokenName.startsWith(prefix));
  }
}

class MissingTokenRule implements ValidationRule {
  id = 'missing-token';
  name = 'Missing Token Usage';
  description = 'Detects places where design tokens should be used but are not';
  category = ValidationCategory.DESIGN_TOKENS;
  severity = Severity.LOW;

  validate(content: string, filePath: string, tokens?: DesignTokenSystem): ValidationViolation[] {
    const violations: ValidationViolation[] = [];
    
    // Look for CSS properties that should typically use design tokens
    const shouldUseTokens = [
      { property: 'color', pattern: /color\s*:\s*(?!var\()[^;]+/g },
      { property: 'background-color', pattern: /background-color\s*:\s*(?!var\()[^;]+/g },
      { property: 'border-color', pattern: /border-color\s*:\s*(?!var\()[^;]+/g }
    ];

    const lines = content.split('\n');
    
    lines.forEach((line, lineIndex) => {
      shouldUseTokens.forEach(({ property, pattern }) => {
        let match;
        while ((match = pattern.exec(line)) !== null) {
          const value = match[0];
          const columnIndex = match.index;
          
          if (this.isInComment(line, columnIndex)) {
            continue;
          }

          violations.push({
            id: `${filePath}:${lineIndex + 1}:${columnIndex}:missing-token`,
            rule: this.id,
            severity: this.severity,
            message: `Property '${property}' should use design token instead of direct value`,
            location: {
              filePath,
              lineNumber: lineIndex + 1,
              columnNumber: columnIndex,
              context: line.trim()
            },
            actualValue: value,
            suggestion: `Consider using a design token for ${property}`,
            autoFixable: false
          });
        }
      });
    });

    return violations;
  }

  private isInComment(line: string, position: number): boolean {
    const beforePosition = line.substring(0, position);
    return beforePosition.includes('//') || beforePosition.includes('/*');
  }
}