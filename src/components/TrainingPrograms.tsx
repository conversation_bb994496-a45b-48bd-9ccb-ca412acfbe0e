// SECTION: Imports
// ==================================================================================
import { Button } from "@/components/ui/button";
import NavigationButton from "@/components/ui/NavigationButton";
import { cn } from "@/lib/utils";
import { useRef, useState, useEffect } from "react";
import { useRevealOnScroll } from "@/hooks/use-reveal-on-scroll";
import { PlayCircle, Award, Calendar, Users } from "lucide-react";
import { Link } from "react-router-dom";
import styles from "./TrainingPrograms.module.css";
import programsData, { Program, Testimonial, SkillLevel } from "@/data/programs"; // Import centralized data
import ResponsiveImage from "@/components/ui/responsive-image";
import TrainingProgramsSkeleton from "./skeletons/TrainingProgramsSkeleton";
import { getResponsiveGridCols, getResponsiveTextSize, getResponsivePadding } from "@/utils/responsive-utilities";

// SECTION: Type and Interface Definitions
// ==================================================================================
// Props for ProgramCard - delay is now generated.
// Other props come from the Program interface.
interface ProgramCardProps extends Omit<Program, 'id'> {
  delay: number;
}

// SECTION: ProgramCard Component
// ==================================================================================
const ProgramCard = ({ 
  title, 
  description, 
  image, 
  features, 
  delay, // This is the dynamically generated delay
  ageGroup = 'All Ages', 
  skillLevel = 'All Levels',
  priceInfo,
  testimonial 
}: ProgramCardProps) => {
  // --- Hooks ---
  const { ref: cardRef, isVisible } = useRevealOnScroll<HTMLDivElement>({
    threshold: 0.1,
    delay: delay, // Pass the delay from props
  });

  // --- Helper Functions ---
  const scrollToContactForm = () => {
    document.getElementById("contact-form")?.scrollIntoView({ behavior: "smooth" });
  };


  // --- Component JSX ---
  return (
    <div
      ref={cardRef}
      className={cn(
        "bg-white rounded-lg overflow-hidden shadow-lg reveal flex flex-col h-full",
        "hover:shadow-xl hover:translate-y-[-5px] hover:scale-[1.01] transition-all duration-300",
        "touch-manipulation", // Improve touch responsiveness
        { active: isVisible } // Apply active class based on visibility
      )}
    >
      <div className="relative">
        <div className="h-48 sm:h-56 md:h-64 overflow-hidden">
          <ResponsiveImage
            src={image}
            alt={`${title} training program`}
            className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
            loading="lazy"
            sizes="(max-width: 768px) 100vw, 50vw"
            sources={[
              { src: image, width: 400 },  // Mobile/small
              { src: image, width: 800 },  // Tablet/medium
              { src: image, width: 1200 }, // Desktop/large
            ]}
          />
        </div>
        <div className="absolute top-3 left-3 md:top-4 md:left-4 flex flex-wrap gap-1 md:gap-2">
          <span className="bg-elev-navy/90 text-white text-xs px-2 py-1 md:px-3 md:py-1 rounded-full">
            {ageGroup}
          </span>
          <span className="bg-elev-navy/90 text-white text-xs px-2 py-1 md:px-3 md:py-1 rounded-full">
            {skillLevel}
          </span>
        </div>
        {title.includes("Small Group") && (
          <span className="absolute top-3 right-3 md:top-4 md:right-4 bg-amber-500/90 text-white text-xs px-2 py-1 md:px-3 md:py-1 rounded-full flex items-center">
            <Award className="w-3 h-3 mr-1" /> Most Popular
          </span>
        )}
      </div>
      <div className={cn("flex flex-col flex-grow", getResponsivePadding("4", "5", "6"))}>
        <div className="flex-grow">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start mb-3 md:mb-4 gap-2">
            <h3 className={cn("font-bold text-elev-navy", getResponsiveTextSize("lg", "xl", "xl"))}>{title}</h3>
            {priceInfo && (
              <span className="text-elev-navy font-medium text-sm md:text-base bg-blue-50 px-3 py-1 rounded-full self-start whitespace-nowrap">
                {priceInfo}
              </span>
            )}
          </div>
          <p className={cn("text-gray-600 mb-4 md:mb-5", getResponsiveTextSize("sm", "base", "base"))}>{description}</p>

          <ul className="mb-5 md:mb-6 space-y-2 md:space-y-3">
            {features.map((feature, index) => (
              <li key={index} className="flex items-start">
                <span className="text-elev-navy mr-2 md:mr-3 flex-shrink-0 text-sm md:text-base font-semibold">✓</span>
                <span className={cn("text-gray-700", getResponsiveTextSize("sm", "base", "base"))}>{feature}</span>
              </li>
            ))}
          </ul>
          
          {testimonial && (
            <div className={cn("mb-5 md:mb-6 bg-gray-50 rounded-lg border-l-4 border-elev-navy/70 italic", getResponsivePadding("3", "4", "4"))}>
              <blockquote className={cn("text-gray-700", getResponsiveTextSize("sm", "base", "base"))}>"{testimonial.quote}"</blockquote>
              {testimonial.author && (
                <p className={cn("text-gray-600 font-semibold not-italic mt-2", getResponsiveTextSize("xs", "sm", "sm"))}>
                  {testimonial.author}{testimonial.role && <span className="font-normal"> - {testimonial.role}</span>}
                </p>
              )}
            </div>
          )}
        </div>

        <div className="flex gap-2 mt-auto">
          {title.includes("Individual") ? (
            <Button
              onClick={scrollToContactForm}
              className={cn(
                "w-full bg-elev-navy hover:bg-elev-navy/80 text-white transition-colors duration-200",
                "min-h-[44px] md:min-h-[48px] text-sm md:text-base font-medium",
                "touch-manipulation active:scale-95"
              )}
            >
              Schedule Your Personal Assessment
            </Button>
          ) : title.includes("Private") || title.includes("Team") ? (
            <Button
              onClick={scrollToContactForm}
              className={cn(
                "w-full bg-elev-navy hover:bg-elev-navy/80 text-white transition-colors duration-200",
                "min-h-[44px] md:min-h-[48px] text-sm md:text-base font-medium",
                "touch-manipulation active:scale-95"
              )}
            >
              Request More Information
            </Button>
          ) : (
            <NavigationButton 
              to="/scheduling" 
              className={cn(
                "w-full bg-elev-navy hover:bg-elev-navy/80 text-white transition-colors duration-200",
                "min-h-[44px] md:min-h-[48px] text-sm md:text-base font-medium",
                "touch-manipulation active:scale-95"
              )}
            >
              Book Now
            </NavigationButton>
          )}
          {/* Commented out video preview button removed */}
        </div>
      </div>
    </div>
  );
};

// SECTION: TrainingPrograms Component
// ==================================================================================
const TrainingPrograms = () => {
  // --- State ---
  const [loading, setLoading] = useState(true);
  const [programs, setPrograms] = useState<Program[]>([]);
  
  // --- Hooks ---
  const { ref: sectionRef, isVisible: isSectionVisible } = useRevealOnScroll<HTMLDivElement>({ threshold: 0.1 });
  
  // --- Effects ---
  useEffect(() => {
    // Simulate data loading delay (remove in production and use actual data fetching)
    const timer = setTimeout(() => {
      setPrograms(programsData);
      setLoading(false);
    }, 1200); // Simulate network delay for demonstration
    
    return () => clearTimeout(timer);
  }, []);

  // Constants for skill levels are now available from programsData if needed,
  // but ProgramCard directly uses the skillLevel prop from the data.

  // --- Component JSX ---
  // Early return while loading
  if (loading) {
    return <TrainingProgramsSkeleton />;
  }
  
  return (
    <section className="section-padding bg-gradient-to-b from-gray-50 to-white" id="programs">
      <div className="container mx-auto px-4 md:px-6 lg:px-8">
        <div
          ref={sectionRef}
          className={cn("text-center mb-8 md:mb-12 lg:mb-16 reveal", { active: isSectionVisible })}
        >
          <h2 className={cn("font-bold mb-4 md:mb-6 text-elev-navy px-4", getResponsiveTextSize("2xl", "3xl", "4xl"))}>
            On-Ice <span className="text-elev-navy">Training Programs</span>
          </h2>
          <p className={cn("text-gray-600 max-w-3xl mx-auto mb-6 md:mb-8 px-4", getResponsiveTextSize("base", "lg", "xl"))}>
            Our comprehensive range of on-ice training programs are designed to develop players of all ages and skill levels. Our expert coaches use proven techniques to enhance skating, stickhandling, shooting, and game awareness.
          </p>
          <div className="flex flex-wrap justify-center gap-4 md:gap-6 mb-8 md:mb-12">
            <div className={cn("flex items-center bg-white rounded-lg shadow-sm min-h-[60px]", getResponsivePadding("3", "4", "4"))}>
              <Award className="h-5 w-5 md:h-6 md:w-6 text-amber-500 mr-2 md:mr-3 flex-shrink-0" />
              <div className="text-left">
                <p className="text-xs text-gray-500">Expert Coaches</p>
                <p className="font-semibold text-sm md:text-base">USA Hockey Certified</p>
              </div>
            </div>
            <div className={cn("flex items-center bg-white rounded-lg shadow-sm min-h-[60px]", getResponsivePadding("3", "4", "4"))}>
              <Users className="h-5 w-5 md:h-6 md:w-6 text-elev-navy mr-2 md:mr-3 flex-shrink-0" />
              <div className="text-left">
                <p className="text-xs text-gray-500">Player Success</p>
                <p className="font-semibold text-sm md:text-base">500+ Trained Athletes</p>
              </div>
            </div>
            <div className={cn("flex items-center bg-white rounded-lg shadow-sm min-h-[60px]", getResponsivePadding("3", "4", "4"))}>
              <Calendar className="h-5 w-5 md:h-6 md:w-6 text-elev-navy mr-2 md:mr-3 flex-shrink-0" />
              <div className="text-left">
                <p className="text-xs text-gray-500">Availability</p>
                <p className="font-semibold text-sm md:text-base">Year-Round Programs</p>
              </div>
            </div>
          </div>
        </div>

        <div className={cn("grid gap-6 md:gap-8 lg:gap-10", getResponsiveGridCols(1, 2, 3))}>
          {programs.map((program, index) => (
            <ProgramCard
              key={program.id} // Use program.id from data
              title={program.title}
              description={program.description}
              image={program.image}
              features={program.features}
              ageGroup={program.ageGroup}
              skillLevel={program.skillLevel}
              priceInfo={program.priceInfo}
              testimonial={program.testimonial}
              delay={index * 150} // Generate delay dynamically
            />
          ))}
        </div>

      </div>
    </section>
  );
};

export default TrainingPrograms;
