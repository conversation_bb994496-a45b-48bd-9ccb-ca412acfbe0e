import { Skeleton } from "@/components/ui/skeleton";

/**
 * Enhanced skeleton loader for the Coaches section with responsive design
 * Adapts layout and sizing based on screen size
 */
export function CoachesSkeleton() {
  return (
    <section className="py-8 sm:py-12 lg:py-16 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header skeleton - Responsive sizing */}
        <div className="text-center mb-8 sm:mb-12">
          <Skeleton className="h-6 sm:h-8 w-48 sm:w-64 mx-auto mb-3 sm:mb-4" />
          <Skeleton className="h-3 sm:h-4 w-full max-w-sm sm:max-w-xl mx-auto mb-2" />
          <Skeleton className="h-3 sm:h-4 w-3/4 max-w-xs sm:max-w-lg mx-auto mb-6 sm:mb-8" />
        </div>
        
        {/* Coach cards grid - Responsive grid and card sizing */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6 lg:gap-8">
          {[1, 2, 3, 4, 5, 6, 7, 8].map((_, index) => (
            <div key={index} className="bg-white rounded-lg shadow-lg overflow-hidden">
              {/* Coach image - Responsive height */}
              <Skeleton className="h-48 sm:h-56 lg:h-64 w-full" />
              
              {/* Coach info - Responsive padding and sizing */}
              <div className="p-4 sm:p-6">
                <Skeleton className="h-4 sm:h-5 w-3/4 mb-2" />
                <Skeleton className="h-3 sm:h-4 w-1/2 mb-3 sm:mb-4" />
                <Skeleton className="h-3 sm:h-4 w-full mb-1.5 sm:mb-2" />
                <Skeleton className="h-3 sm:h-4 w-11/12 mb-1.5 sm:mb-2" />
                <Skeleton className="h-3 sm:h-4 w-10/12" />
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

export default CoachesSkeleton;
