/**
 * Validation System Types
 * Defines interfaces for design system compliance validation
 */

import { Issue, Severity, FileLocation } from '../types';
import { DesignTokenSystem } from '../design-tokens/types';

export interface ValidationResult {
  isValid: boolean;
  violations: ValidationViolation[];
  summary: ValidationSummary;
  executionTime: number;
}

export interface ValidationViolation {
  id: string;
  rule: string;
  severity: Severity;
  message: string;
  location: FileLocation;
  expectedValue?: string;
  actualValue: string;
  suggestion: string;
  autoFixable: boolean;
}

export interface ValidationSummary {
  totalViolations: number;
  severityBreakdown: Record<Severity, number>;
  ruleBreakdown: Record<string, number>;
  fileBreakdown: Record<string, number>;
}

export interface ValidationRule {
  id: string;
  name: string;
  description: string;
  category: ValidationCategory;
  severity: Severity;
  validate(content: string, filePath: string, tokens?: DesignTokenSystem): ValidationViolation[];
}

export enum ValidationCategory {
  DESIGN_TOKENS = 'design_tokens',
  COMPONENT_CONSISTENCY = 'component_consistency',
  VISUAL_REGRESSION = 'visual_regression',
  ACCESSIBILITY = 'accessibility',
  PERFORMANCE = 'performance'
}

export interface Validator {
  name: string;
  category: ValidationCategory;
  rules: ValidationRule[];
  validate(files: ValidationFile[], tokens?: DesignTokenSystem): Promise<ValidationResult>;
}

export interface ValidationFile {
  path: string;
  content: string;
  type: FileType;
  lastModified: Date;
}

export enum FileType {
  COMPONENT = 'component',
  STYLE = 'style',
  CONFIG = 'config',
  TEST = 'test'
}

export interface ValidationConfig {
  rules: string[];
  severity: Severity;
  autoFix: boolean;
  excludePatterns: string[];
  includePatterns: string[];
}

export interface ComplianceReport {
  overall: ValidationResult;
  byCategory: Record<ValidationCategory, ValidationResult>;
  byFile: Record<string, ValidationResult>;
  recommendations: ComplianceRecommendation[];
  generatedAt: Date;
}

export interface ComplianceRecommendation {
  id: string;
  title: string;
  description: string;
  category: ValidationCategory;
  priority: number;
  effort: number;
  violations: string[];
  autoFixable: boolean;
}

export interface VisualRegressionTest {
  id: string;
  name: string;
  component: string;
  viewport: Viewport;
  baseline: string;
  threshold: number;
}

export interface Viewport {
  width: number;
  height: number;
  deviceScaleFactor: number;
}

export interface RegressionResult {
  testId: string;
  passed: boolean;
  difference: number;
  diffImage?: string;
  actualImage: string;
  expectedImage: string;
}