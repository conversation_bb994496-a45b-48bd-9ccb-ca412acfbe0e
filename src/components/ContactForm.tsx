import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import { useF<PERSON>, SubmitHandler, Controller } from "react-hook-form";
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useRevealOnScroll } from "@/hooks/use-reveal-on-scroll";
import { FaHockeyPuck, FaCheckCircle } from "react-icons/fa";
import LevelUpImage from "@/assets/A92462C0-.jpg";
import { getResponsiveTextSize, getResponsivePadding, getTouchTargetClass, getContainerClass, getGridResponsiveClass, getFlexResponsiveClass } from "@/utils/responsive-utilities";

// Define the form schema using Zod
const formSchema = z.object({
  athleteName: z.string().min(1, "Athlete name is required").max(100, "Name is too long"),
  parentName: z.string().optional().or(z.literal("")),
  email: z.string().min(1, "Email is required").email("Please enter a valid email address"),
  phone: z.string().min(1, "Phone number is required").regex(/^[\+]?[1-9][\d]{0,15}$|^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/, "Please enter a valid phone number"),
  age: z.string().min(1, "Age or birth year is required").max(4, "Please enter a valid age or birth year"),
  interest: z.string().min(1, "Please select a service"),
  message: z.string().optional().or(z.literal("")),
});

type FormValues = z.infer<typeof formSchema>;

const ContactForm = () => {
  const { toast } = useToast();
  const { register, handleSubmit, formState: { errors, isSubmitting }, reset, control } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
  });

  const { ref: sectionRef, isVisible: isSectionVisible } = useRevealOnScroll<HTMLDivElement>({ threshold: 0.1 });
  const { ref: formRef, isVisible: isFormVisible } = useRevealOnScroll<HTMLDivElement>({ threshold: 0.1, delay: 200 });
  const { ref: imageRef, isVisible: isImageVisible } = useRevealOnScroll<HTMLDivElement>({ threshold: 0.1, delay: 400 });

  const onSubmit: SubmitHandler<FormValues> = async (data) => {
    try {
      const formData = new FormData();
      Object.entries(data).forEach(([key, value]) => {
        if (value) {
          formData.append(key, value);
        }
      });
      formData.append('form-name', 'contact');
      
      // Submit to Netlify
      // Create URLSearchParams directly from FormData
      const searchParams = new URLSearchParams();
      // Manually append each entry from FormData to URLSearchParams
      for (const [key, value] of formData.entries()) {
        searchParams.append(key, value.toString());
      }
      
      const response = await fetch('/', {
        method: 'POST',
        body: searchParams.toString(),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Form submission failed: ${response.status} ${response.statusText}. ${errorText}`);
      }

      toast({
        title: "Inquiry Submitted Successfully",
        description: "Thank you for your interest! Our team will contact you within 24 hours.",
        variant: "default",
      });

      reset();

    } catch (error) {
      console.error('Form submission error:', error);
      toast({
        title: "Submission Error",
        description: error instanceof Error ? error.message : "There was a problem submitting your form. Please try again.",
        variant: "destructive",
      });
    }
  };

  // --- Component JSX ---
  return (
    <section 
      className="section-padding bg-white text-black relative overflow-hidden" 
      id="contact-form" 
      aria-labelledby="contact-form-heading"
    >
      {/* Hidden form for Netlify's build process */}
      <form name="contact" netlify="true" netlify-honeypot="bot-field" hidden>
        <input type="text" name="athleteName" />
        <input type="text" name="parentName" />
        <input type="email" name="email" />
        <input type="tel" name="phone" />
        <input type="text" name="age" />
        <input type="text" name="interest" />
        <textarea name="message"></textarea>
      </form>
      
      <div className={cn("mx-auto relative z-10", getContainerClass())}>
        <div
          ref={sectionRef}
          className={cn("text-center reveal", getResponsivePadding("8", "12", "16"), { active: isSectionVisible })}
        >
          <h1 id="contact-form-heading" className={cn("font-bold mb-4 md:mb-6 text-black", getResponsiveTextSize("3xl", "4xl", "5xl"))}>Contact Us</h1>
          <p className={cn("text-black max-w-3xl mx-auto mb-6 px-4", getResponsiveTextSize("base", "lg", "xl"))}>
            Fill out the form below, and our team will contact you to discuss your goals and how we can help you achieve them.
          </p>
          <div className="flex flex-wrap justify-center gap-3 md:gap-4 mt-4 text-black px-4" role="list" aria-label="Certifications and achievements">
            <span className={cn("flex items-center gap-1 md:gap-2", getResponsiveTextSize("sm", "base", "base"))} role="listitem">
              <FaCheckCircle className="text-black flex-shrink-0" aria-hidden="true" /> 
              <span>USA Hockey Certified</span>
            </span>
            <span className={cn("flex items-center gap-1 md:gap-2", getResponsiveTextSize("sm", "base", "base"))} role="listitem">
              <FaCheckCircle className="text-black flex-shrink-0" aria-hidden="true" /> 
              <span>Pro-Level Training</span>
            </span>
            <span className={cn("flex items-center gap-1 md:gap-2", getResponsiveTextSize("sm", "base", "base"))} role="listitem">
              <FaCheckCircle className="text-black flex-shrink-0" aria-hidden="true" /> 
              <span>500+ Trained Athletes</span>
            </span>
          </div>
        </div>

        <div className={cn("grid items-start", getGridResponsiveClass("1-2-3"), "gap-6 md:gap-8 lg:gap-16 px-4 lg:px-0")}>
          <div
            ref={formRef}
            className={cn(
              "reveal bg-white rounded-2xl shadow-2xl transform transition-all duration-500 hover:scale-[1.01] border border-elev-background-accent w-full",
              getResponsivePadding("4", "6", "8"),
              { active: isFormVisible }
            )}
          >
            <div className="flex items-center justify-between mb-4 md:mb-6 pb-3 md:pb-4 border-b border-elev-background-accent">
              <h3 className={cn("font-bold text-elev-text-primary", getResponsiveTextSize("lg", "xl", "xl"))}>Contact Us</h3>
            </div>
            
            <form 
              name="contact" 
              method="POST" 
              data-netlify="true"
              netlify-honeypot="bot-field"
              onSubmit={handleSubmit(onSubmit)}
              noValidate
              aria-label="Contact form"
            >
              <input type="hidden" name="form-name" value="contact" />
              
              {/* Honeypot field for spam protection */}
              <div className="hidden">
                <label>
                  Don't fill this out if you're human: <input name="bot-field" />
                </label>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="athleteName" className={cn("flex items-center font-medium", getResponsiveTextSize("sm", "base", "base"))}>
                    Athlete Name
                    <span className="text-red-500 ml-1" aria-hidden="true">*</span>
                    <span className="sr-only">required</span>
                  </Label>
                  <Input
                    id="athleteName"
                    name="athleteName"
                    placeholder="Athlete's full name"
                    required
                    aria-required="true"
                    aria-invalid={!!errors.athleteName}
                    aria-describedby={errors.athleteName ? "athleteName-error" : undefined}
                    {...register("athleteName")}
                    className={cn(
                      "focus:ring-2 focus:ring-elev-blue focus:border-elev-blue touch-manipulation",
                      getTouchTargetClass(),
                      errors.athleteName && "border-red-500"
                    )}
                  />
                  {errors.athleteName && (
                    <p id="athleteName-error" className={cn("text-red-500 mt-1", getResponsiveTextSize("xs", "sm", "sm"))} role="alert">
                      {errors.athleteName}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="parentName" className={cn("font-medium", getResponsiveTextSize("sm", "base", "base"))}>
                    Parent Name (if applicable)
                  </Label>
                  <Input
                    id="parentName"
                    name="parentName"
                    placeholder="Parent's full name"
                    aria-describedby="parentName-hint"
                    {...register("parentName")}
                    className={cn("focus:ring-2 focus:ring-elev-blue focus:border-elev-blue touch-manipulation", getTouchTargetClass())}
                  />
                  <p id="parentName-hint" className={cn("text-elev-text-muted", getResponsiveTextSize("xs", "sm", "sm"))}>
                    Optional for athletes under 18
                  </p>
                </div>

                <div className={cn("grid", getGridResponsiveClass("1-2-3"), "gap-4")}>
                  <div className="space-y-2">
                    <Label htmlFor="email" className={cn("flex items-center font-medium", getResponsiveTextSize("sm", "base", "base"))}>
                      Email
                      <span className="text-red-500 ml-1" aria-hidden="true">*</span>
                      <span className="sr-only">required</span>
                    </Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="<EMAIL>"
                      required
                      aria-required="true"
                      aria-invalid={!!errors.email}
                      aria-describedby={errors.email ? "email-error" : undefined}
                      {...register("email")}
                      className={cn(
                        "focus:ring-2 focus:ring-elev-blue focus:border-elev-blue touch-manipulation",
                        getTouchTargetClass(),
                        errors.email && "border-red-500"
                      )}
                    />
                    {errors.email && (
                      <p id="email-error" className={cn("text-red-500 mt-1", getResponsiveTextSize("xs", "sm", "sm"))} role="alert">
                        {errors.email}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone" className={cn("flex items-center font-medium", getResponsiveTextSize("sm", "base", "base"))}>
                      Phone
                      <span className="text-red-500 ml-1" aria-hidden="true">*</span>
                      <span className="sr-only">required</span>
                    </Label>
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      placeholder="(*************"
                      required
                      aria-required="true"
                      aria-invalid={!!errors.phone}
                      aria-describedby={errors.phone ? "phone-error" : undefined}
                      {...register("phone")}
                      className={cn(
                        "focus:ring-2 focus:ring-elev-blue focus:border-elev-blue touch-manipulation",
                        getTouchTargetClass(),
                        errors.phone && "border-red-500"
                      )}
                    />
                    {errors.phone && (
                      <p id="phone-error" className={cn("text-red-500 mt-1", getResponsiveTextSize("xs", "sm", "sm"))} role="alert">
                        {errors.phone}
                      </p>
                    )}
                  </div>
                </div>

                <div className={cn("grid", getGridResponsiveClass("1-2-3"), "gap-4")}>
                  <div className="space-y-2">
                    <Label htmlFor="age" className={cn("flex items-center font-medium", getResponsiveTextSize("sm", "base", "base"))}>
                      Age or Birth Year
                      <span className="text-red-500 ml-1" aria-hidden="true">*</span>
                      <span className="sr-only">required</span>
                    </Label>
                    <Input
                      id="age"
                      name="age"
                      placeholder="e.g., 15 or 2008"
                      required
                      aria-required="true"
                      aria-invalid={!!errors.age}
                      aria-describedby={errors.age ? "age-error" : undefined}
                      {...register("age")}
                      className={cn(
                        "focus:ring-2 focus:ring-elev-blue focus:border-elev-blue touch-manipulation",
                        getTouchTargetClass(),
                        errors.age && "border-red-500"
                      )}
                    />
                    {errors.age && (
                      <p id="age-error" className={cn("text-red-500 mt-1", getResponsiveTextSize("xs", "sm", "sm"))} role="alert">
                        {errors.age}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="interest" className={cn("flex items-center font-medium", getResponsiveTextSize("sm", "base", "base"))}>
                      I'm interested in
                      <span className="text-red-500 ml-1" aria-hidden="true">*</span>
                      <span className="sr-only">required</span>
                    </Label>
                    <Controller
                      name="interest"
                      control={control}
                      render={({ field }) => (
                        <Select onValueChange={field.onChange} value={field.value}>
                          <SelectTrigger
                            id="interest"
                            className={cn(
                              "focus:ring-2 focus:ring-elev-blue focus:border-elev-blue touch-manipulation text-left",
                              getTouchTargetClass(),
                              errors.interest && "border-red-500"
                            )}
                            aria-invalid={!!errors.interest}
                            aria-describedby={errors.interest ? "interest-error" : undefined}
                          >
                            <SelectValue placeholder="Select a program" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="individual">Individual Training</SelectItem>
                            <SelectItem value="group">Small Group Training</SelectItem>
                            <SelectItem value="both">Both Individual & Group</SelectItem>
                            <SelectItem value="other">Other (please specify in message)</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.interest && (
                      <p id="interest-error" className={cn("text-red-500 mt-1", getResponsiveTextSize("xs", "sm", "sm"))} role="alert">
                        {errors.interest}
                      </p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="message" className={cn("font-medium", getResponsiveTextSize("sm", "base", "base"))}>
                    Message (optional)
                  </Label>
                  <Textarea
                    id="message"
                    name="message"
                    placeholder="Tell us about your goals, experience level, and any questions you have..."
                    rows={4}
                    {...register("message")}
                    className={cn(
                      "focus:ring-2 focus:ring-elev-blue focus:border-elev-blue touch-manipulation min-h-[120px]",
                      getTouchTargetClass()
                    )}
                  />
                </div>

                <div className="pt-2">
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className={cn(
                      "w-full bg-elev-navy hover:bg-elev-navy/90 text-white font-medium",
                      "transition-colors duration-200",
                      "focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-elev-navy",
                      "disabled:opacity-70 disabled:cursor-not-allowed",
                      getTouchTargetClass()
                    )}
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Sending...
                      </>
                    ) : (
                      "Send Message"
                    )}
                  </Button>
                </div>

                <p className={cn("text-center text-elev-text-muted", getResponsiveTextSize("xs", "sm", "sm"))}>
                  We'll get back to you within 24 hours.
                </p>
              </div>
            </form>
          </div>

          <div
            ref={imageRef}
            className={cn(
              "reveal relative rounded-2xl overflow-hidden w-full max-w-md",
              "bg-gradient-to-br from-elev-navy/10 to-elev-navy/5",
              "flex items-center justify-center p-8",
              { active: isImageVisible }
            )}
          >
            <div className="text-center max-w-md">
  <img
    src={LevelUpImage}
    alt="Hockey training"
    className="mx-auto mb-6 rounded-xl shadow-lg w-full object-cover max-h-48"
  />
              <div className="mx-auto w-16 h-16 bg-elev-navy/10 rounded-full flex items-center justify-center mb-6">
                <FaHockeyPuck className="text-elev-navy text-2xl" />
              </div>
              <h3 className={cn("font-bold text-elev-text-primary mb-3", getResponsiveTextSize("xl", "2xl", "3xl"))}>
                Ready to take your game to the next level?
              </h3>
              <p className={cn("text-elev-text-secondary mb-6", getResponsiveTextSize("sm", "base", "lg"))}>
                Our expert coaches are here to help you develop your skills, build confidence, and achieve your hockey goals.
              </p>
              <div className="space-y-4 text-left max-w-xs mx-auto">
                <div className="flex items-start gap-3">
                  <FaCheckCircle className="text-elev-navy mt-1 flex-shrink-0" />
                  <div>
                    <h4 className={cn("font-semibold text-elev-text-primary", getResponsiveTextSize("sm", "base", "lg"))}>Personalized Training</h4>
                    <p className={cn("text-elev-text-muted", getResponsiveTextSize("xs", "sm", "sm"))}>Customized programs for all skill levels</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <FaCheckCircle className="text-elev-navy mt-1 flex-shrink-0" />
                  <div>
                    <h4 className={cn("font-semibold text-elev-text-primary", getResponsiveTextSize("sm", "base", "lg"))}>Experienced Coaches</h4>
                    <p className={cn("text-elev-text-muted", getResponsiveTextSize("xs", "sm", "sm"))}>Professional guidance from hockey experts</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <FaCheckCircle className="text-elev-navy mt-1 flex-shrink-0" />
                  <div>
                    <h4 className={cn("font-semibold text-elev-text-primary", getResponsiveTextSize("sm", "base", "lg"))}>Proven Results</h4>
                    <p className={cn("text-elev-text-muted", getResponsiveTextSize("xs", "sm", "sm"))}>See measurable improvement in your game</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactForm;
