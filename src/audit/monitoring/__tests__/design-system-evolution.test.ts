import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { DesignSystemEvolution, VersionChange } from '../design-system-evolution';
import * as fs from 'fs/promises';
import * as path from 'path';

// Mock dependencies
vi.mock('fs/promises');
vi.mock('../../core/logger', () => ({
    AuditLogger: vi.fn().mockImplementation(() => ({
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        debug: vi.fn()
    }))
}));

describe('DesignSystemEvolution', () => {
    let evolution: DesignSystemEvolution;
    const mockProjectPath = '/test/project';

    beforeEach(() => {
        evolution = new DesignSystemEvolution(mockProjectPath);
        vi.clearAllMocks();
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('initialize', () => {
        it('should create versions directory and initial version', async () => {
            (fs.mkdir as any).mockResolvedValue(undefined);
            (fs.readdir as any).mockResolvedValue([]);
            (fs.writeFile as any).mockResolvedValue(undefined);

            await evolution.initialize();

            expect(fs.mkdir).toHaveBeenCalledWith(
                path.join(mockProjectPath, '.design-system-versions'),
                { recursive: true }
            );
            expect(fs.writeFile).toHaveBeenCalled();
        });

        it('should not create initial version if versions already exist', async () => {
            (fs.mkdir as any).mockResolvedValue(undefined);
            (fs.readdir as any).mockResolvedValue(['1.0.0.json']);
            (fs.readFile as any).mockResolvedValue(JSON.stringify({
                version: '1.0.0',
                timestamp: new Date(),
                changes: [],
                tokens: { colors: {}, typography: {}, spacing: {}, layout: {}, hash: 'test' },
                components: [],
                metadata: { author: 'test', description: 'test', breaking: false }
            }));

            await evolution.initialize();

            expect(fs.writeFile).not.toHaveBeenCalled();
        });
    });

    describe('createVersion', () => {
        it('should create a new version with changes', async () => {
            const changes: VersionChange[] = [
                {
                    type: 'modified',
                    category: 'token',
                    path: 'colors.primary',
                    description: 'Updated primary color',
                    impact: 'medium'
                }
            ];

            (fs.writeFile as any).mockResolvedValue(undefined);

            const version = await evolution.createVersion('1.1.0', 'Color updates', changes, 'developer');

            expect(version.version).toBe('1.1.0');
            expect(version.changes).toEqual(changes);
            expect(version.metadata.author).toBe('developer');
            expect(version.metadata.description).toBe('Color updates');
            expect(fs.writeFile).toHaveBeenCalled();
        });

        it('should mark version as breaking when breaking changes exist', async () => {
            const changes: VersionChange[] = [
                {
                    type: 'removed',
                    category: 'token',
                    path: 'colors.deprecated',
                    description: 'Removed deprecated color',
                    impact: 'breaking'
                }
            ];

            (fs.writeFile as any).mockResolvedValue(undefined);

            const version = await evolution.createVersion('2.0.0', 'Breaking changes', changes);

            expect(version.metadata.breaking).toBe(true);
        });
    });

    describe('analyzeImpact', () => {
        it('should analyze impact of proposed changes', async () => {
            const changes: VersionChange[] = [
                {
                    type: 'modified',
                    category: 'token',
                    path: 'colors.primary',
                    description: 'Updated primary color',
                    impact: 'high'
                },
                {
                    type: 'removed',
                    category: 'component',
                    path: 'Button',
                    description: 'Removed old button',
                    impact: 'breaking'
                }
            ];

            const impact = await evolution.analyzeImpact(changes);

            expect(impact.breakingChanges).toHaveLength(1);
            expect(impact.migrationComplexity).toBe('high');
            expect(impact.estimatedEffort).toBeGreaterThan(0);
            expect(impact.affectedFiles).toContain('src/components/Button');
        });

        it('should calculate low complexity for simple changes', async () => {
            const changes: VersionChange[] = [
                {
                    type: 'added',
                    category: 'token',
                    path: 'colors.new',
                    description: 'Added new color',
                    impact: 'low'
                }
            ];

            const impact = await evolution.analyzeImpact(changes);

            expect(impact.migrationComplexity).toBe('low');
            expect(impact.breakingChanges).toHaveLength(0);
        });
    });

    describe('generateMigrationPlan', () => {
        it('should generate migration plan between versions', async () => {
            const fromVersion = {
                version: '1.0.0',
                timestamp: new Date(),
                changes: [],
                tokens: { colors: {}, typography: {}, spacing: {}, layout: {}, hash: 'old' },
                components: [],
                metadata: { author: 'test', description: 'old', breaking: false }
            };

            const toVersion = {
                version: '1.1.0',
                timestamp: new Date(),
                changes: [
                    {
                        type: 'modified' as const,
                        category: 'token' as const,
                        path: 'colors.primary',
                        description: 'Updated primary color',
                        impact: 'medium' as const,
                        migration: [
                            {
                                description: 'Update color references',
                                automated: true,
                                script: 'update-colors.js'
                            }
                        ]
                    }
                ],
                tokens: { colors: {}, typography: {}, spacing: {}, layout: {}, hash: 'new' },
                components: [],
                metadata: { author: 'test', description: 'new', breaking: false }
            };

            (fs.readFile as any)
                .mockResolvedValueOnce(JSON.stringify(fromVersion))
                .mockResolvedValueOnce(JSON.stringify(toVersion));

            (fs.readdir as any).mockResolvedValue(['1.0.0.json', '1.1.0.json']);
            (fs.readFile as any)
                .mockResolvedValueOnce(JSON.stringify(fromVersion))
                .mockResolvedValueOnce(JSON.stringify(toVersion));

            const plan = await evolution.generateMigrationPlan('1.0.0', '1.1.0');

            expect(plan.fromVersion).toBe('1.0.0');
            expect(plan.toVersion).toBe('1.1.0');
            expect(plan.steps).toHaveLength(1);
            expect(plan.rollbackPlan).toHaveLength(1);
            expect(plan.validationChecks).toHaveLength(1);
        });

        it('should throw error for non-existent versions', async () => {
            (fs.readFile as any).mockRejectedValue(new Error('File not found'));

            await expect(evolution.generateMigrationPlan('1.0.0', '2.0.0'))
                .rejects.toThrow('Version not found');
        });
    });

    describe('executeMigration', () => {
        it('should execute migration plan successfully', async () => {
            const plan = {
                fromVersion: '1.0.0',
                toVersion: '1.1.0',
                steps: [
                    {
                        description: 'Update colors',
                        automated: true,
                        script: 'update-colors.js',
                        validation: 'validate-colors.js'
                    }
                ],
                rollbackPlan: [],
                validationChecks: [
                    {
                        name: 'Color validation',
                        description: 'Validate colors',
                        script: 'validate.js',
                        critical: false
                    }
                ],
                estimatedDuration: 30
            };

            (fs.mkdir as any).mockResolvedValue(undefined);
            (fs.writeFile as any).mockResolvedValue(undefined);

            const result = await evolution.executeMigration(plan);

            expect(result).toBe(true);
        });

        it('should rollback on migration failure', async () => {
            const plan = {
                fromVersion: '1.0.0',
                toVersion: '1.1.0',
                steps: [
                    {
                        description: 'Failing step',
                        automated: true,
                        script: 'failing-script.js',
                        validation: 'failing-validation.js'
                    }
                ],
                rollbackPlan: [
                    {
                        description: 'Rollback step',
                        script: 'rollback.js',
                        validation: 'validate-rollback.js'
                    }
                ],
                validationChecks: [],
                estimatedDuration: 30
            };

            // Mock backup creation
            (fs.mkdir as any).mockResolvedValue(undefined);
            (fs.writeFile as any).mockResolvedValue(undefined);

            const result = await evolution.executeMigration(plan);

            expect(result).toBe(true); // Should succeed since we're mocking validation as successful
        });
    });

    describe('rollbackToVersion', () => {
        it('should rollback to specified version', async () => {
            const versionData = {
                version: '1.0.0',
                timestamp: new Date(),
                changes: [],
                tokens: { colors: {}, typography: {}, spacing: {}, layout: {}, hash: 'test' },
                components: [],
                metadata: { author: 'test', description: 'test', breaking: false }
            };

            (fs.readFile as any).mockResolvedValue(JSON.stringify(versionData));

            const result = await evolution.rollbackToVersion('1.0.0');

            expect(result).toBe(true);
        });

        it('should fail rollback for non-existent version', async () => {
            (fs.readFile as any).mockRejectedValue(new Error('File not found'));

            const result = await evolution.rollbackToVersion('999.0.0');

            expect(result).toBe(false);
        });
    });

    describe('getVersionHistory', () => {
        it('should return sorted version history', async () => {
            const version1 = {
                version: '1.0.0',
                timestamp: new Date('2024-01-01'),
                changes: [],
                tokens: { colors: {}, typography: {}, spacing: {}, layout: {}, hash: 'v1' },
                components: [],
                metadata: { author: 'test', description: 'v1', breaking: false }
            };

            const version2 = {
                version: '1.1.0',
                timestamp: new Date('2024-01-02'),
                changes: [],
                tokens: { colors: {}, typography: {}, spacing: {}, layout: {}, hash: 'v2' },
                components: [],
                metadata: { author: 'test', description: 'v2', breaking: false }
            };

            (fs.readdir as any).mockResolvedValue(['1.0.0.json', '1.1.0.json']);
            (fs.readFile as any)
                .mockResolvedValueOnce(JSON.stringify(version1))
                .mockResolvedValueOnce(JSON.stringify(version2));

            const history = await evolution.getVersionHistory();

            expect(history).toHaveLength(2);
            expect(history[0].version).toBe('1.1.0'); // Should be sorted by timestamp desc
            expect(history[1].version).toBe('1.0.0');
        });

        it('should return empty array on error', async () => {
            (fs.readdir as any).mockRejectedValue(new Error('Directory not found'));

            const history = await evolution.getVersionHistory();

            expect(history).toEqual([]);
        });
    });

    describe('getVersion', () => {
        it('should return specific version', async () => {
            const versionData = {
                version: '1.0.0',
                timestamp: new Date(),
                changes: [],
                tokens: { colors: {}, typography: {}, spacing: {}, layout: {}, hash: 'test' },
                components: [],
                metadata: { author: 'test', description: 'test', breaking: false }
            };

            (fs.readFile as any).mockResolvedValue(JSON.stringify(versionData));

            const version = await evolution.getVersion('1.0.0');

            expect(version?.version).toBe(versionData.version);
            expect(version?.metadata).toEqual(versionData.metadata);
            expect(version?.tokens).toEqual(versionData.tokens);
        });

        it('should return null for non-existent version', async () => {
            (fs.readFile as any).mockRejectedValue(new Error('File not found'));

            const version = await evolution.getVersion('999.0.0');

            expect(version).toBeNull();
        });
    });
});