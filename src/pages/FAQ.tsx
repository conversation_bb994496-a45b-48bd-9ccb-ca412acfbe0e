import React from "react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import SEO from "@/components/SEO";
import { FAQSchema } from "@/utils/schemaExtensions";
import { useNavigate } from "react-router-dom";
import { Breadcrumbs } from "@/components/ui/Breadcrumbs";
import { useBreadcrumbs, useShouldShowBreadcrumbs } from "@/hooks/useBreadcrumbs";

const FAQ = () => {
  const navigate = useNavigate();
  
  // Function to navigate to home page and scroll to contact form
  const navigateToContactForm = () => {
    navigate('/', { replace: true });
    // Use setTimeout to ensure navigation completes before scrolling
    setTimeout(() => {
      const contactFormElement = document.getElementById('contact-form');
      if (contactFormElement) {
        contactFormElement.scrollIntoView({ behavior: 'smooth' });
      }
    }, 100);
  };
  
  // FAQ items that address common questions about hockey training
  const faqItems = [
    {
      question: "What age groups does ELEV802 | 702HOCKEY train?",
      answer: "We offer training programs for all age groups, from youth players just starting out to adult recreational and professional players. Our specialized programs are tailored to each age group's needs and skill level."
    },
    {
      question: "Do I need prior hockey experience to train at ELEV802 | 702HOCKEY?",
      answer: "No prior experience is required! We welcome players of all skill levels, from complete beginners to professionals. Our coaches will assess your current level and create an appropriate training plan."
    },
    {
      question: "How often should I train to see improvements?",
      answer: "For best results, we recommend training at least twice weekly. However, even once-weekly sessions show significant improvement over time. Our coaches will recommend an optimal training schedule based on your goals."
    },
    {
      question: "What equipment do I need to bring to training sessions?",
      answer: "Players should bring full hockey equipment including skates, stick, helmet, gloves, and protective gear. Goalies should bring their complete goalie equipment. For specific training sessions, your coach may provide a more detailed equipment list."
    },
    {
      question: "Do you offer group training or only individual sessions?",
      answer: "We offer both individual and small group training options. Individual sessions provide personalized attention, while small group sessions offer team dynamics and competitive drills at a more accessible price point."
    },
    {
      question: "How do I schedule a training session?",
      answer: "Training sessions can be scheduled through our online booking system on the website, by calling us at (800) 984-5990, or <NAME_EMAIL>. We recommend booking at least 48 hours in advance to secure your preferred time slot."
    },
    {
      question: "What makes ELEV802 | 702HOCKEY different from other training facilities?",
      answer: "ELEV802's 702HOCKEY facility offers year-round access to custom ice, expert coaches with professional playing experience, state-of-the-art training technology, and personalized development plans. Our facility is designed specifically for hockey skill development with features not found at regular ice rinks."
    },
    {
      question: "Do you offer off-ice training?",
      answer: "Yes, we provide comprehensive off-ice training programs that focus on hockey-specific strength, conditioning, and skill development. These sessions complement on-ice training and are crucial for complete player development."
    }
  ];

  return (
    <div className="flex flex-col min-h-screen">
      <SEO
        title="Frequently Asked Questions - ELEV802 | 702HOCKEY - Las Vegas Hockey Training"
        description="Find answers to common questions about hockey training programs, equipment requirements, scheduling, and more at ELEV802's 702HOCKEY facility in Las Vegas."
        keywords="ELEV802, hockey training FAQ, hockey lessons questions, Las Vegas hockey programs, 702HOCKEY information"
        canonical="/faq"
      />
      
      {/* Add structured data for FAQ */}
      <FAQSchema questions={faqItems} />
      
      <Header />
      {useShouldShowBreadcrumbs() && (
        <Breadcrumbs
          items={useBreadcrumbs()}
          showHomeIcon={true}
          separator="chevron"
        />
      )}
      <main className="flex-grow">
        <section className="py-16 bg-elev-background-muted">
          <div className="container mx-auto px-4">
            <h1 className="text-4xl font-bold text-center mb-16 text-elev-navy">Frequently Asked Questions</h1>
            
            <div className="max-w-3xl mx-auto">
              {faqItems.map((item, index) => (
                <div 
                  key={index}
                  className="mb-8 bg-white rounded-lg shadow-md overflow-hidden"
                >
                  <details className="group">
                    <summary className="flex justify-between items-center p-6 cursor-pointer">
                      <h3 className="text-xl font-semibold text-elev-navy">{item.question}</h3>
                      <span className="text-elev-navy transition-transform group-open:rotate-180">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </span>
                    </summary>
                    <div className="px-6 pb-6 text-elev-text-secondary">
                      <p>{item.answer}</p>
                    </div>
                  </details>
                </div>
              ))}
            </div>
            
            <div className="text-center mt-16">
              <h2 className="text-2xl font-bold mb-4 text-elev-navy">Still Have Questions?</h2>
              <p className="mb-8 text-elev-text-secondary">Contact us directly and we'll be happy to help you.</p>
              <button 
                onClick={navigateToContactForm}
                className="inline-flex items-center px-6 py-3 bg-elev-navy text-white rounded-lg hover:bg-blue-800 transition"
              >
                Contact Us
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default FAQ;
