// Service Worker for 702Hockey - handles caching and offline support

// Set this to false if you don't want to use precaching
const ENABLE_PRECACHING = true;

// Name of the cache to use for precaching
const PRECACHE = 'precache-v2'; // Incremented version

// List of URLs to precache
const PRECACHE_URLS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.ico',
  // JavaScript entry points
  '/assets/index-t8dVq62k.js',
  // CSS files
  '/assets/index-B6ZHwyGF.css',
  // Images
  '/images/icon-192x192.png',
  '/images/icon-512x512.png',
  // Add other assets you want to precache here
];

// Assets that should be cached with a cache-first strategy
const CACHE_FIRST_PATTERNS = [
  /\.(?:js|css)$/,  // All JS and CSS files
  /\.(?:png|jpg|jpeg|svg|gif|webp)$/, // All image files
  /\.(?:woff|woff2|ttf|otf)$/ // All font files
];

// Assets that should be cached with a network-first strategy
const NETWORK_FIRST_PATTERNS = [
  /\/api\//,  // API calls
  /\/data\//   // Data files
];

// External domains that might cause CORS issues
const PROBLEMATIC_DOMAINS = [
  'cdn.gpteng.co',
  'doubleclick.net'
];

// Helper to check if a URL is from a problematic domain
const isProblematicUrl = (url) => {
  try {
    const urlObj = new URL(url);
    return PROBLEMATIC_DOMAINS.some(domain => urlObj.hostname.includes(domain));
  } catch (e) {
    return false;
  }
};

// Install event - cache all static assets with error handling
self.addEventListener('install', (event) => {
  if (ENABLE_PRECACHING) {
    event.waitUntil(
      caches.open(PRECACHE)
        .then((cache) => {
          // Use Promise.allSettled instead of Promise.all to handle individual failures
          return Promise.allSettled(
            PRECACHE_URLS.map(url => 
              cache.add(url).catch(error => {
                console.error(`Failed to cache ${url}:`, error);
                // Continue despite the error
                return Promise.resolve();
              })
            )
          );
        })
        .then(() => self.skipWaiting())
        .catch(error => {
          console.error('Precaching failed:', error);
          // Skip waiting even if precaching fails
          return self.skipWaiting();
        })
    );
  } else {
    self.skipWaiting();
  }
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  const currentCaches = [PRECACHE, 'api-cache', 'runtime-cache'];
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (!currentCaches.includes(cacheName)) {
              return caches.delete(cacheName);
            }
            return null;
          }).filter(Boolean)
        );
      })
      .then(() => self.clients.claim())
      .catch(error => {
        console.error('Cache cleanup failed:', error);
        // Claim clients even if cleanup fails
        return self.clients.claim();
      })
  );
});

// Fetch event - cache-first strategy for static assets, network-first for API calls
self.addEventListener('fetch', (event) => {
  // Skip non-GET requests
  if (event.request.method !== 'GET') {
    return;
  }

  // Skip browser extensions and problematic domains
  const url = event.request.url;
  if (
    url.startsWith('chrome-extension://') || 
    url.startsWith('moz-extension://') ||
    isProblematicUrl(url)
  ) {
    return; // Let the browser handle these requests normally
  }

  // Check if the request matches any of our network-first patterns
  const isNetworkFirstRequest = NETWORK_FIRST_PATTERNS.some(pattern => pattern.test(url));
  
  // Handle network-first requests (API calls, data files)
  if (isNetworkFirstRequest) {
    event.respondWith(
      fetch(event.request)
        .then((response) => {
          if (!response || response.status !== 200) {
            throw new Error('Bad response status');
          }
          // Clone the response for potential caching
          const responseToCache = response.clone();
          caches.open('api-cache')
            .then((cache) => cache.put(event.request, responseToCache))
            .catch(err => console.error('API cache error:', err));
          return response;
        })
        .catch((error) => {
          console.log(`Network request failed for ${url}:`, error);
          // If network fails, try to get from cache
          return caches.match(event.request)
            .then(cachedResponse => {
              return cachedResponse || Promise.reject('No cached response available');
            });
        })
    );
    return;
  }
  
  // Check if the request matches any of our cache-first patterns
  const isCacheFirstRequest = CACHE_FIRST_PATTERNS.some(pattern => pattern.test(url));

  // For cache-first requests (JS, CSS, images, fonts), use cache-first strategy
  if (isCacheFirstRequest) {
    event.respondWith(
      caches.match(event.request)
        .then((cachedResponse) => {
          if (cachedResponse) {
            return cachedResponse;
          }
          
          // If not in cache, fetch from network
          return fetch(event.request)
            .then((response) => {
              // Don't cache non-successful responses or opaque responses
              if (!response || response.status !== 200 || response.type === 'opaque') {
                return response;
              }
              
              // Clone the response for caching
              const responseToCache = response.clone();
              caches.open('runtime-cache')
                .then((cache) => cache.put(event.request, responseToCache))
                .catch(err => console.error('Runtime cache error:', err));
              
              return response;
            })
            .catch((error) => {
              console.error(`Network request failed for ${url}:`, error);
              // For JS/CSS failures, return a minimal response to prevent breaking the page
              if (url.endsWith('.js')) {
                return new Response('console.log("JS asset failed to load");', 
                  { status: 200, headers: { 'Content-Type': 'application/javascript' } });
              } else if (url.endsWith('.css')) {
                return new Response('/* CSS asset failed to load */', 
                  { status: 200, headers: { 'Content-Type': 'text/css' } });
              }
              return new Response('Asset unavailable', { status: 408, headers: { 'Content-Type': 'text/plain' } });
            });
        })
    );
    return;
  }
  
  // For all other requests, use network-first with cache fallback
  event.respondWith(
    fetch(event.request)
      .then((response) => {
        // Don't cache non-successful responses or opaque responses
        if (!response || response.status !== 200 || response.type === 'opaque') {
          return response;
        }
        
        // Clone the response for caching
        const responseToCache = response.clone();
        caches.open('runtime-cache')
          .then((cache) => cache.put(event.request, responseToCache))
          .catch(err => console.error('Runtime cache error:', err));
        
        return response;
      })
      .catch((error) => {
        console.error(`Network request failed for ${url}:`, error);
        // Try to get from cache
        return caches.match(event.request)
          .then(cachedResponse => {
            if (cachedResponse) {
              return cachedResponse;
            }
            // Return a custom offline page for navigation requests
            if (event.request.mode === 'navigate') {
              return caches.match('/offline.html');
            }
            return new Response('Network error', { status: 408, headers: { 'Content-Type': 'text/plain' } });
          });
      })
  );
});

// Background sync for failed API requests
self.addEventListener('sync', (event) => {
  if (event.tag === 'sync-api-requests') {
    // Implement background sync logic here
    console.log('Background sync triggered:', event.tag);
  }
});

// Push notifications
self.addEventListener('push', (event) => {
  if (!(self.Notification && self.Notification.permission === 'granted')) {
    return;
  }

  try {
    const data = event.data ? event.data.json() : {};
    const title = data?.title || 'New Notification';
    const options = {
      body: data?.body || 'You have a new notification',
      icon: data?.icon || '/icons/icon-192x192.png',
      badge: '/icons/icon-96x96.png',
      data: data?.data || {},
    };

    event.waitUntil(self.registration.showNotification(title, options));
  } catch (error) {
    console.error('Push notification error:', error);
  }
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  try {
    const urlToOpen = new URL(event.notification.data?.url || '/', self.location.origin).href;

    event.waitUntil(
      clients.matchAll({ type: 'window' })
        .then((windowClients) => {
          // Check if there's already a window/tab open with the target URL
          const existingWindow = windowClients.find(
            (client) => client.url === urlToOpen && 'focus' in client
          );

          if (existingWindow) {
            // Focus the existing window
            return existingWindow.focus();
          }
          
          // Open a new window/tab if none exists
          if (clients.openWindow) {
            return clients.openWindow(urlToOpen);
          }
          
          return null;
        })
        .catch(error => {
          console.error('Notification click handling error:', error);
        })
    );
  } catch (error) {
    console.error('Notification URL processing error:', error);
  }
});
