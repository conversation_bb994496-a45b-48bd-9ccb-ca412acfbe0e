// Comprehensive audit report generation system

import {
  AuditReport,
  AnalyzerResult,
  AuditSummary,
  Issue,
  Severity,
  Category,
  IssueType,
  Recommendation,
  RemediationPlan,
  ImpactAssessment
} from '../types';
import { auditLogger } from './logger';
import { PrioritizationEngine } from './prioritization-engine';

export interface ReportMetrics {
  issueDistribution: Record<IssueType, number>;
  severityDistribution: Record<Severity, number>;
  categoryDistribution: Record<Category, number>;
  impactAnalysis: {
    totalImpactScore: number;
    averageImpactScore: number;
    highImpactIssues: number;
  };
  fileAnalysis: {
    mostProblematicFiles: Array<{
      filePath: string;
      issueCount: number;
      severityScore: number;
    }>;
    fileTypeBreakdown: Record<string, number>;
  };
}

export interface CategorizedIssues {
  critical: Issue[];
  high: Issue[];
  medium: Issue[];
  low: Issue[];
  byCategory: Record<Category, Issue[]>;
  byType: Record<IssueType, Issue[]>;
}

export class ReportGenerator {
  private prioritizationEngine: PrioritizationEngine;

  constructor() {
    this.prioritizationEngine = new PrioritizationEngine();
  }

  /**
   * Generate comprehensive audit report with detailed analysis
   */
  generateComprehensiveReport(results: AnalyzerResult[]): AuditReport {
    auditLogger.info('Generating comprehensive audit report', { 
      analyzerCount: results.length 
    });

    const startTime = Date.now();
    
    try {
      // Combine all issues from analyzers
      const allIssues = this.combineAnalyzerResults(results);
      
      // Generate detailed summary with metrics
      const summary = this.generateDetailedSummary(results, allIssues);
      
      // Categorize and assess issues
      const categorizedIssues = this.categorizeIssues(allIssues);
      
      // Generate impact assessment
      const impactAssessment = this.generateImpactAssessment(allIssues);
      
      // Create prioritized recommendations
      const recommendations = this.generatePrioritizedRecommendations(
        categorizedIssues, 
        impactAssessment
      );
      
      // Build remediation plan
      const remediationPlan = this.generateDetailedRemediationPlan(
        categorizedIssues,
        recommendations
      );

      const report: AuditReport = {
        summary: {
          ...summary,
          executionTime: Date.now() - startTime
        },
        results,
        recommendations,
        remediationPlan,
        generatedAt: new Date()
      };

      auditLogger.info('Comprehensive audit report generated', {
        totalIssues: allIssues.length,
        recommendationCount: recommendations.length,
        executionTime: Date.now() - startTime
      });

      return report;

    } catch (error) {
      auditLogger.error('Failed to generate comprehensive report', error as Error);
      throw error;
    }
  }

  /**
   * Combine and deduplicate issues from all analyzers
   */
  private combineAnalyzerResults(results: AnalyzerResult[]): Issue[] {
    const allIssues: Issue[] = [];
    const seenIssues = new Set<string>();

    for (const result of results) {
      for (const issue of result.issues) {
        // Create unique identifier for deduplication
        const issueKey = `${issue.location.filePath}:${issue.location.lineNumber}:${issue.type}`;
        
        if (!seenIssues.has(issueKey)) {
          seenIssues.add(issueKey);
          allIssues.push(issue);
        }
      }
    }

    auditLogger.info('Combined analyzer results', {
      totalIssues: allIssues.length,
      uniqueIssues: seenIssues.size
    });

    return allIssues;
  }

  /**
   * Generate detailed summary with comprehensive metrics
   */
  private generateDetailedSummary(
    results: AnalyzerResult[], 
    allIssues: Issue[]
  ): AuditSummary {
    const severityBreakdown: Record<Severity, number> = {
      [Severity.LOW]: 0,
      [Severity.MEDIUM]: 0,
      [Severity.HIGH]: 0,
      [Severity.CRITICAL]: 0
    };

    const categoryBreakdown: Record<Category, number> = {
      [Category.VISUAL]: 0,
      [Category.CONTENT]: 0,
      [Category.COMPONENT]: 0,
      [Category.ACCESSIBILITY]: 0
    };

    // Count issues by severity and category
    allIssues.forEach(issue => {
      severityBreakdown[issue.severity]++;
      categoryBreakdown[issue.category]++;
    });

    // Calculate total files analyzed
    const uniqueFiles = new Set(allIssues.map(issue => issue.location.filePath));
    const totalFiles = uniqueFiles.size;

    // Calculate total execution time
    const totalExecutionTime = results.reduce(
      (sum, result) => sum + result.executionTime, 
      0
    );

    return {
      totalFiles,
      totalIssues: allIssues.length,
      severityBreakdown,
      categoryBreakdown,
      executionTime: totalExecutionTime,
      timestamp: new Date()
    };
  }

  /**
   * Categorize issues by severity, category, and type
   */
  private categorizeIssues(issues: Issue[]): CategorizedIssues {
    const categorized: CategorizedIssues = {
      critical: [],
      high: [],
      medium: [],
      low: [],
      byCategory: {
        [Category.VISUAL]: [],
        [Category.CONTENT]: [],
        [Category.COMPONENT]: [],
        [Category.ACCESSIBILITY]: []
      },
      byType: {
        [IssueType.COLOR_INCONSISTENCY]: [],
        [IssueType.SPACING_INCONSISTENCY]: [],
        [IssueType.TYPOGRAPHY_INCONSISTENCY]: [],
        [IssueType.COMPONENT_INCONSISTENCY]: [],
        [IssueType.CONTENT_INCONSISTENCY]: []
      }
    };

    issues.forEach(issue => {
      // Categorize by severity
      switch (issue.severity) {
        case Severity.CRITICAL:
          categorized.critical.push(issue);
          break;
        case Severity.HIGH:
          categorized.high.push(issue);
          break;
        case Severity.MEDIUM:
          categorized.medium.push(issue);
          break;
        case Severity.LOW:
          categorized.low.push(issue);
          break;
      }

      // Categorize by category
      categorized.byCategory[issue.category].push(issue);

      // Categorize by type
      categorized.byType[issue.type].push(issue);
    });

    return categorized;
  }

  /**
   * Generate comprehensive impact assessment
   */
  private generateImpactAssessment(issues: Issue[]): ReportMetrics {
    // Calculate impact scores
    const impactScores = issues.map(issue => 
      issue.impact.userExperience + 
      issue.impact.maintenanceEffort + 
      issue.impact.implementationComplexity
    );

    const totalImpactScore = impactScores.reduce((sum, score) => sum + score, 0);
    const averageImpactScore = issues.length > 0 ? totalImpactScore / issues.length : 0;
    const highImpactIssues = issues.filter(issue => 
      (issue.impact.userExperience + 
       issue.impact.maintenanceEffort + 
       issue.impact.implementationComplexity) > 15
    ).length;

    // Analyze file distribution
    const fileIssueMap = new Map<string, Issue[]>();
    issues.forEach(issue => {
      const filePath = issue.location.filePath;
      if (!fileIssueMap.has(filePath)) {
        fileIssueMap.set(filePath, []);
      }
      fileIssueMap.get(filePath)!.push(issue);
    });

    // Calculate severity scores for files
    const mostProblematicFiles = Array.from(fileIssueMap.entries())
      .map(([filePath, fileIssues]) => ({
        filePath,
        issueCount: fileIssues.length,
        severityScore: this.calculateFileSeverityScore(fileIssues)
      }))
      .sort((a, b) => b.severityScore - a.severityScore)
      .slice(0, 10); // Top 10 most problematic files

    // File type breakdown
    const fileTypeBreakdown: Record<string, number> = {};
    Array.from(fileIssueMap.keys()).forEach(filePath => {
      const extension = filePath.split('.').pop() || 'unknown';
      fileTypeBreakdown[extension] = (fileTypeBreakdown[extension] || 0) + 1;
    });

    // Issue distribution
    const issueDistribution: Record<IssueType, number> = {
      [IssueType.COLOR_INCONSISTENCY]: 0,
      [IssueType.SPACING_INCONSISTENCY]: 0,
      [IssueType.TYPOGRAPHY_INCONSISTENCY]: 0,
      [IssueType.COMPONENT_INCONSISTENCY]: 0,
      [IssueType.CONTENT_INCONSISTENCY]: 0
    };

    const severityDistribution: Record<Severity, number> = {
      [Severity.LOW]: 0,
      [Severity.MEDIUM]: 0,
      [Severity.HIGH]: 0,
      [Severity.CRITICAL]: 0
    };

    const categoryDistribution: Record<Category, number> = {
      [Category.VISUAL]: 0,
      [Category.CONTENT]: 0,
      [Category.COMPONENT]: 0,
      [Category.ACCESSIBILITY]: 0
    };

    issues.forEach(issue => {
      issueDistribution[issue.type]++;
      severityDistribution[issue.severity]++;
      categoryDistribution[issue.category]++;
    });

    return {
      issueDistribution,
      severityDistribution,
      categoryDistribution,
      impactAnalysis: {
        totalImpactScore,
        averageImpactScore,
        highImpactIssues
      },
      fileAnalysis: {
        mostProblematicFiles,
        fileTypeBreakdown
      }
    };
  }

  /**
   * Calculate severity score for a file based on its issues
   */
  private calculateFileSeverityScore(issues: Issue[]): number {
    const severityWeights = {
      [Severity.CRITICAL]: 10,
      [Severity.HIGH]: 7,
      [Severity.MEDIUM]: 4,
      [Severity.LOW]: 1
    };

    return issues.reduce((score, issue) => 
      score + severityWeights[issue.severity], 0
    );
  }

  /**
   * Generate prioritized recommendations based on impact and categorization
   */
  private generatePrioritizedRecommendations(
    categorizedIssues: CategorizedIssues,
    metrics: ReportMetrics
  ): Recommendation[] {
    const recommendations: Recommendation[] = [];
    let recommendationId = 1;

    // Generate recommendations for each issue type
    Object.entries(categorizedIssues.byType).forEach(([type, issues]) => {
      if (issues.length === 0) return;

      const typeKey = type as IssueType;
      const recommendation = this.createRecommendation(
        recommendationId++,
        typeKey,
        issues,
        metrics
      );
      
      recommendations.push(recommendation);
    });

    // Add high-level strategic recommendations
    if (metrics.impactAnalysis.highImpactIssues > 10) {
      recommendations.push({
        id: `rec_${recommendationId++}`,
        title: 'Establish Design System Governance',
        description: `With ${metrics.impactAnalysis.highImpactIssues} high-impact issues identified, implementing design system governance and automated checking is critical.`,
        priority: 10,
        estimatedEffort: 15,
        relatedIssues: categorizedIssues.critical.concat(categorizedIssues.high).map(i => i.id)
      });
    }

    // Sort by priority (highest first)
    return recommendations.sort((a, b) => b.priority - a.priority);
  }

  /**
   * Create detailed recommendation for specific issue type
   */
  private createRecommendation(
    id: number,
    type: IssueType,
    issues: Issue[],
    metrics: ReportMetrics
  ): Recommendation {
    const severityScore = this.calculateFileSeverityScore(issues);
    const priority = Math.min(10, Math.max(1, Math.floor(severityScore / issues.length)));
    
    const recommendationMap = {
      [IssueType.COLOR_INCONSISTENCY]: {
        title: 'Implement Unified Color System',
        description: `Found ${issues.length} color inconsistencies. Establish design tokens and color palette to ensure consistent brand representation.`,
        baseEffort: 8
      },
      [IssueType.SPACING_INCONSISTENCY]: {
        title: 'Standardize Spacing and Layout',
        description: `Identified ${issues.length} spacing inconsistencies. Implement spacing tokens and grid system for consistent layouts.`,
        baseEffort: 6
      },
      [IssueType.TYPOGRAPHY_INCONSISTENCY]: {
        title: 'Unify Typography System',
        description: `Detected ${issues.length} typography inconsistencies. Create type scale and hierarchy for consistent text presentation.`,
        baseEffort: 7
      },
      [IssueType.COMPONENT_INCONSISTENCY]: {
        title: 'Standardize Component Patterns',
        description: `Found ${issues.length} component inconsistencies. Establish component library and usage guidelines.`,
        baseEffort: 12
      },
      [IssueType.CONTENT_INCONSISTENCY]: {
        title: 'Align Content Standards',
        description: `Identified ${issues.length} content inconsistencies. Develop content style guide and tone guidelines.`,
        baseEffort: 5
      }
    };

    const config = recommendationMap[type];
    const effortMultiplier = Math.max(1, Math.floor(issues.length / 10));
    
    return {
      id: `rec_${id}`,
      title: config.title,
      description: config.description,
      priority,
      estimatedEffort: config.baseEffort * effortMultiplier,
      relatedIssues: issues.map(issue => issue.id)
    };
  }

  /**
   * Generate detailed remediation plan with advanced prioritization and dependencies
   */
  private generateDetailedRemediationPlan(
    categorizedIssues: CategorizedIssues,
    recommendations: Recommendation[]
  ): RemediationPlan {
    // Combine all issues for prioritization analysis
    const allIssues = [
      ...categorizedIssues.critical,
      ...categorizedIssues.high,
      ...categorizedIssues.medium,
      ...categorizedIssues.low
    ];

    // Use prioritization engine for sophisticated planning
    const { plan, metrics } = this.prioritizationEngine.createPrioritizedPlan(
      allIssues,
      recommendations
    );

    auditLogger.info('Advanced remediation plan generated', {
      taskCount: plan.tasks.length,
      totalEffort: plan.estimatedEffort,
      quickWins: metrics.quickWins.length,
      highImpactTasks: metrics.highImpactTasks.length
    });

    return plan;
  }

  private getTaskTypeFromRecommendation(title: string): string {
    if (title.includes('Color')) return 'color_standardization';
    if (title.includes('Spacing')) return 'spacing_standardization';
    if (title.includes('Typography')) return 'typography_standardization';
    if (title.includes('Component')) return 'component_standardization';
    if (title.includes('Content')) return 'content_standardization';
    return 'general_standardization';
  }

  private getTaskDependencies(title: string): string[] {
    const dependencyMap: Record<string, string[]> = {
      'Implement Unified Color System': ['design_tokens_setup'],
      'Standardize Spacing and Layout': ['design_tokens_setup'],
      'Unify Typography System': ['design_tokens_setup'],
      'Standardize Component Patterns': ['design_tokens_setup', 'component_library_setup'],
      'Align Content Standards': ['style_guide_creation'],
      'Establish Design System Governance': []
    };

    return dependencyMap[title] || [];
  }

  private isAutoFixable(relatedIssueIds: string[], categorizedIssues: CategorizedIssues): boolean {
    const allIssues = [
      ...categorizedIssues.critical,
      ...categorizedIssues.high,
      ...categorizedIssues.medium,
      ...categorizedIssues.low
    ];

    const relatedIssues = allIssues.filter(issue => relatedIssueIds.includes(issue.id));
    return relatedIssues.some(issue => issue.autoFixable);
  }
}