#!/usr/bin/env node

/**
 * Sitemap Generator Script
 * 
 * This script generates a comprehensive XML sitemap for 702Hockey website.
 * It includes all pages with proper lastmod, changefreq, and priority attributes.
 * 
 * Run with: node scripts/generate-sitemap.js
 */

const fs = require('fs');
const path = require('path');
const prettier = require('prettier');

// Base URL of the website
const WEBSITE_URL = 'https://702hockey.com';
// Today's date in YYYY-MM-DD format
const TODAY = new Date().toISOString().split('T')[0];

// Pages configuration with their relative importance and update frequency
const PAGES = [
  { path: '/', changefreq: 'weekly', priority: 1.0 },
  { path: '/scheduling', changefreq: 'weekly', priority: 0.9 },
  { path: '/goalie-training', changefreq: 'monthly', priority: 0.8 },
  { path: '/hockey-training', changefreq: 'monthly', priority: 0.8 },
  { path: '/player-skills-training', changefreq: 'monthly', priority: 0.8 },
  { path: '/pop-up-clinics', changefreq: 'monthly', priority: 0.7 },
  { path: '/coaches', changefreq: 'monthly', priority: 0.6 },
  { path: '/faq', changefreq: 'monthly', priority: 0.7 },
  // Add any new pages here
];

// Generate the XML sitemap
function generateSitemap() {
  const sitemap = `
    <?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
        ${PAGES.map(page => `
            <url>
                <loc>${WEBSITE_URL}${page.path}</loc>
                <lastmod>${TODAY}</lastmod>
                <changefreq>${page.changefreq}</changefreq>
                <priority>${page.priority}</priority>
            </url>
        `).join('')}
    </urlset>
  `;

  // Format the XML
  const formattedSitemap = prettier.format(sitemap, { parser: 'html' });

  // Write to file
  fs.writeFileSync(
    path.resolve(__dirname, '../public/sitemap.xml'),
    formattedSitemap
  );

  console.log('✅ Sitemap generated successfully!');
}

// Execute the generator
generateSitemap();
