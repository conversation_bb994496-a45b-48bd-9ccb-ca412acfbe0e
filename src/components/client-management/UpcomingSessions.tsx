import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Calendar, Clock, MapPin, User, Phone } from 'lucide-react';

/**
 * UpcomingSessions component displays upcoming training sessions
 * Shows session details, client information, and action buttons
 */
interface Session {
  id: string;
  clientName: string;
  clientPhone?: string;
  sessionType: string;
  date: string;
  time: string;
  duration: number; // in minutes
  location: string;
  status: 'confirmed' | 'pending' | 'cancelled';
  notes?: string;
}

interface UpcomingSessionsProps {
  sessions?: Session[];
  onContactClient?: (clientPhone: string) => void;
  onReschedule?: (sessionId: string) => void;
  onCancel?: (sessionId: string) => void;
}

const UpcomingSessions: React.FC<UpcomingSessionsProps> = ({
  sessions = [],
  onContactClient,
  onReschedule,
  onCancel
}) => {
  // Mock data for demonstration
  const mockSessions: Session[] = [
    {
      id: '1',
      clientName: '<PERSON>',
      clientPhone: '******-0123',
      sessionType: 'Individual Skills Training',
      date: '2024-01-20',
      time: '10:00',
      duration: 60,
      location: 'Rink A',
      status: 'confirmed',
      notes: 'Focus on shooting technique'
    },
    {
      id: '2',
      clientName: 'Sarah Johnson',
      clientPhone: '******-0456',
      sessionType: 'Goalie Training',
      date: '2024-01-20',
      time: '14:00',
      duration: 90,
      location: 'Rink B',
      status: 'pending',
      notes: 'First session - assessment'
    },
    {
      id: '3',
      clientName: 'Mike Wilson',
      clientPhone: '******-0789',
      sessionType: 'Power Skating',
      date: '2024-01-21',
      time: '09:00',
      duration: 45,
      location: 'Rink A',
      status: 'confirmed'
    }
  ];

  const displaySessions = sessions.length > 0 ? sessions : mockSessions;

  /**
   * Get badge variant based on session status
   */
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'default';
      case 'pending':
        return 'secondary';
      case 'cancelled':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  /**
   * Format date for display
   */
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  /**
   * Handle contact client
   */
  const handleContactClient = (phone: string) => {
    onContactClient?.(phone);
  };

  /**
   * Handle reschedule session
   */
  const handleReschedule = (sessionId: string) => {
    onReschedule?.(sessionId);
  };

  /**
   * Handle cancel session
   */
  const handleCancel = (sessionId: string) => {
    onCancel?.(sessionId);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Upcoming Sessions
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {displaySessions.length === 0 ? (
            <p className="text-gray-500 text-center py-8">
              No upcoming sessions scheduled.
            </p>
          ) : (
            displaySessions.map((session) => (
              <div
                key={session.id}
                className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
              >
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  {/* Session Info */}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="font-semibold text-gray-900">
                        {session.clientName}
                      </h4>
                      <Badge variant={getStatusBadgeVariant(session.status)}>
                        {session.status.charAt(0).toUpperCase() + session.status.slice(1)}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <User className="h-4 w-4" />
                        {session.sessionType}
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        {formatDate(session.date)}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {session.time} ({session.duration} min)
                      </div>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-4 w-4" />
                        {session.location}
                      </div>
                    </div>
                    
                    {session.notes && (
                      <p className="text-sm text-gray-500 mt-2 italic">
                        {session.notes}
                      </p>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex flex-col sm:flex-row gap-2">
                    {session.clientPhone && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleContactClient(session.clientPhone!)}
                        className="flex items-center gap-1"
                      >
                        <Phone className="h-4 w-4" />
                        Contact
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleReschedule(session.id)}
                    >
                      Reschedule
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleCancel(session.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default UpcomingSessions;