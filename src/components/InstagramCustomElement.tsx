import { StrictMode } from 'react';
import { createRoot, Root } from 'react-dom/client';

/**
 * Instagram post component for custom element
 */
export const InstagramPost = ({ postUrl, captions }: { postUrl?: string; captions?: boolean }) => {
  return (
    <StrictMode>
      <div
        dangerouslySetInnerHTML={{
          __html: `<blockquote 
            class="instagram-media" 
            data-instgrm-permalink="${postUrl || 'https://www.instagram.com/p/C78_gA7yZ6J/'}" 
            data-instgrm-version="14"
            ${captions ? '' : 'data-instgrm-captioned'}
          ></blockquote>`
        }}
      />
    </StrictMode>
  );
};

// Declare global instgrm to prevent TypeScript errors
declare global {
  interface Window {
    instgrm?: {
      Embeds: {
        process: (node?: HTMLElement | null) => void;
      };
    };
  }
}
