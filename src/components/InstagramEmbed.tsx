import React from 'react';
import type { FC } from 'react';

/**
 * LocalVideoEmbed component displaying Elev802 Summer Camp Recap video
 * Styled to match Instagram embed appearance
 */
const LazyInstagramEmbed: FC = () => {
  // Local video file path for Elev802 Summer Camp Recap
  // Using import to properly reference the video asset
  const videoSrc = new URL('../assets/Elev802 Summer Camp Recap 🔥  Thank you to all who joined in on the fun. Hope everyone had a gre.mp4', import.meta.url).href;
  
  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden" style={{ maxWidth: '328px' }}>
        {/* Video Header */}
        <div className="p-3 border-b border-gray-100">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-bold">E</span>
            </div>
            <div>
              <p className="font-semibold text-sm text-gray-900">elev802_vegas</p>
              <p className="text-xs text-gray-500">Summer Camp Recap</p>
            </div>
          </div>
        </div>
        
        {/* Video Player */}
        <div className="relative">
          <video 
            className="w-full h-auto"
            controls
            preload="metadata"
            style={{ aspectRatio: '9/16', maxHeight: '400px' }}
          >
            <source src={videoSrc} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        </div>
        
        {/* Video Caption */}
        <div className="p-3">
          <p className="text-sm text-gray-800">
            <span className="font-semibold">elev802_vegas</span> Summer Camp Recap 🔥 Thank you to all who joined in on the fun. Hope everyone had a great time!
          </p>
          <p className="text-xs text-gray-500 mt-1">#Elev802 #SummerCamp #Hockey #Vegas</p>
        </div>
      </div>
    </div>
  );
};

export default LazyInstagramEmbed;