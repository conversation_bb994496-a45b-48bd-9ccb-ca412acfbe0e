/**
 * Component Documentation Generator
 * Creates comprehensive documentation for React components including props, variants, and usage guidelines
 */

import { promises as fs } from 'fs';
import * as path from 'path';
import {
  ComponentDocumentation,
  ComponentSpec,
  ComponentCategory,
  PropSpec,
  ComponentVariant,
  ComponentState,
  ComponentExample,
  UsageGuidelines,
  AccessibilitySpec,
  ComponentTokenUsage,
  DocumentationMetadata,
  NavigationStructure
} from './types';

export class ComponentDocumentationGenerator {
  private outputDir: string;
  private components: Map<string, ComponentSpec> = new Map();

  constructor(outputDir: string = './docs/components') {
    this.outputDir = outputDir;
  }

  /**
   * Generate comprehensive component documentation
   */
  async generateDocumentation(
    componentSpecs: ComponentSpec[],
    options: DocumentationOptions = {}
  ): Promise<ComponentDocumentation> {
    const metadata = this.createMetadata(options);
    const navigation = this.createNavigation(componentSpecs);

    // Store components for cross-referencing
    componentSpecs.forEach(spec => {
      this.components.set(spec.name, spec);
    });

    const documentation: ComponentDocumentation = {
      components: componentSpecs,
      metadata,
      navigation
    };

    // Generate documentation files
    await this.generateMarkdownDocs(documentation);
    await this.generateJSONDocs(documentation);
    await this.generateIndexFiles(documentation);

    return documentation;
  }

  /**
   * Create component specification from component analysis
   */
  createComponentSpec(
    componentName: string,
    componentPath: string,
    analysisResult: ComponentAnalysis
  ): ComponentSpec {
    return {
      name: componentName,
      description: analysisResult.description || `${componentName} component`,
      category: this.categorizeComponent(componentName, analysisResult),
      props: this.extractPropSpecs(analysisResult.props),
      variants: this.createVariants(analysisResult.variants),
      states: this.createStates(analysisResult.states),
      examples: this.createExamples(componentName, analysisResult.examples),
      usage: this.createUsageGuidelines(componentName, analysisResult),
      accessibility: this.createAccessibilitySpec(analysisResult.accessibility),
      designTokens: this.extractTokenUsage(analysisResult.tokens)
    };
  }

  /**
   * Generate Markdown documentation files
   */
  private async generateMarkdownDocs(documentation: ComponentDocumentation): Promise<void> {
    await fs.mkdir(this.outputDir, { recursive: true });

    // Generate individual component docs
    for (const component of documentation.components) {
      const markdown = this.generateComponentMarkdown(component);
      const filePath = path.join(this.outputDir, `${component.name.toLowerCase()}.md`);
      await fs.writeFile(filePath, markdown, 'utf-8');
    }

    // Generate overview documentation
    const overview = this.generateOverviewMarkdown(documentation);
    await fs.writeFile(path.join(this.outputDir, 'README.md'), overview, 'utf-8');
  }

  /**
   * Generate JSON documentation for programmatic access
   */
  private async generateJSONDocs(documentation: ComponentDocumentation): Promise<void> {
    const jsonPath = path.join(this.outputDir, 'components.json');
    await fs.writeFile(jsonPath, JSON.stringify(documentation, null, 2), 'utf-8');

    // Generate individual component JSON files
    for (const component of documentation.components) {
      const componentJsonPath = path.join(this.outputDir, 'json', `${component.name.toLowerCase()}.json`);
      await fs.mkdir(path.dirname(componentJsonPath), { recursive: true });
      await fs.writeFile(componentJsonPath, JSON.stringify(component, null, 2), 'utf-8');
    }
  }

  /**
   * Generate index files for navigation
   */
  private async generateIndexFiles(documentation: ComponentDocumentation): Promise<void> {
    // Generate category index files
    const categoriesDir = path.join(this.outputDir, 'categories');
    await fs.mkdir(categoriesDir, { recursive: true });

    const componentsByCategory = this.groupComponentsByCategory(documentation.components);

    for (const [category, components] of componentsByCategory.entries()) {
      const categoryMarkdown = this.generateCategoryMarkdown(category, components);
      const categoryPath = path.join(categoriesDir, `${category}.md`);
      await fs.writeFile(categoryPath, categoryMarkdown, 'utf-8');
    }

    // Generate navigation index
    const navIndex = this.generateNavigationIndex(documentation.navigation);
    await fs.writeFile(path.join(this.outputDir, 'navigation.json'), navIndex, 'utf-8');
  }

  /**
   * Generate Markdown for individual component
   */
  private generateComponentMarkdown(component: ComponentSpec): string {
    return `# ${component.name}

${component.description}

## Category
${component.category}

## Props

| Name | Type | Required | Default | Description |
|------|------|----------|---------|-------------|
${component.props.map(prop => 
  `| ${prop.name} | \`${prop.type}\` | ${prop.required ? '✓' : ''} | ${prop.defaultValue || ''} | ${prop.description} |`
).join('\n')}

## Variants

${component.variants.map(variant => `
### ${variant.name}

${variant.description}

**Props:** \`${JSON.stringify(variant.props)}\`

**Design Tokens:** ${variant.designTokens.join(', ')}

\`\`\`tsx
${variant.code}
\`\`\`
`).join('\n')}

## States

${component.states.map(state => `
### ${state.name}

${state.description}

**Trigger:** ${state.trigger}

**Visual Changes:**
${state.visualChanges.map(change => `- ${change}`).join('\n')}

**Accessibility:**
${state.accessibility.map(a11y => `- ${a11y}`).join('\n')}
`).join('\n')}

## Examples

${component.examples.map(example => `
### ${example.title}

${example.description}

\`\`\`tsx
${example.code}
\`\`\`
${example.variants ? `
**Variants:**
${example.variants.map(variant => `
- **${variant.name}:** ${variant.description}
  \`${JSON.stringify(variant.props)}\`
`).join('')}` : ''}
`).join('\n')}

## Usage Guidelines

### When to use
${component.usage.when.map(item => `- ${item}`).join('\n')}

### When not to use
${component.usage.whenNot.map(item => `- ${item}`).join('\n')}

### Best Practices
${component.usage.bestPractices.map(item => `- ${item}`).join('\n')}

### Common Mistakes
${component.usage.commonMistakes.map(item => `- ${item}`).join('\n')}

### Related Components
${component.usage.relatedComponents.map(item => `- [${item}](./${item.toLowerCase()}.md)`).join('\n')}

## Accessibility

### ARIA Labels
${component.accessibility.ariaLabels.map(aria => `
- **${aria.attribute}** ${aria.required ? '(required)' : '(optional)'}
  - Purpose: ${aria.purpose}
  - Example: \`${aria.example}\`
`).join('')}

### Keyboard Navigation
${component.accessibility.keyboardNavigation.map(kb => `
- **${kb.key}:** ${kb.action} (${kb.context})
`).join('')}

### Screen Reader Support
${component.accessibility.screenReader.map(sr => `
- **${sr.context}:** "${sr.announcement}" (${sr.timing})
`).join('')}

### Color Contrast
${component.accessibility.colorContrast.map(contrast => `
- **${contrast.element}:** ${contrast.ratio} (${contrast.level})
`).join('')}

### Focus Management
${component.accessibility.focusManagement.map(focus => `
- **${focus.element}:** ${focus.behavior}
  - Visual: ${focus.visual}
`).join('')}

## Design Tokens

### Colors
${component.designTokens.colors.map(token => `- \`${token}\``).join('\n')}

### Typography
${component.designTokens.typography.map(token => `- \`${token}\``).join('\n')}

### Spacing
${component.designTokens.spacing.map(token => `- \`${token}\``).join('\n')}

### Layout
${component.designTokens.layout.map(token => `- \`${token}\``).join('\n')}
`;
  }

  /**
   * Generate overview documentation
   */
  private generateOverviewMarkdown(documentation: ComponentDocumentation): string {
    const componentsByCategory = this.groupComponentsByCategory(documentation.components);

    return `# Component Documentation

Generated on ${documentation.metadata.generatedAt}
Design System Version: ${documentation.metadata.designSystemVersion}

## Components by Category

${Array.from(componentsByCategory.entries()).map(([category, components]) => `
### ${category.charAt(0).toUpperCase() + category.slice(1)}

${components.map(component => `- [${component.name}](./${component.name.toLowerCase()}.md) - ${component.description}`).join('\n')}
`).join('')}

## Quick Reference

| Component | Category | Props | Variants | States |
|-----------|----------|-------|----------|--------|
${documentation.components.map(component => 
  `| [${component.name}](./${component.name.toLowerCase()}.md) | ${component.category} | ${component.props.length} | ${component.variants.length} | ${component.states.length} |`
).join('\n')}

## Navigation

${documentation.navigation.sections.map(section => `
### ${section.title}

${section.items.map(item => `- [${item.title}](./${item.slug}.md)`).join('\n')}
`).join('')}
`;
  }

  /**
   * Generate category-specific documentation
   */
  private generateCategoryMarkdown(category: ComponentCategory, components: ComponentSpec[]): string {
    return `# ${category.charAt(0).toUpperCase() + category.slice(1)} Components

## Overview

Components in the ${category} category provide ${this.getCategoryDescription(category)}.

## Components

${components.map(component => `
### [${component.name}](../${component.name.toLowerCase()}.md)

${component.description}

**Props:** ${component.props.length} | **Variants:** ${component.variants.length} | **States:** ${component.states.length}

**Design Tokens Used:**
- Colors: ${component.designTokens.colors.length}
- Typography: ${component.designTokens.typography.length}
- Spacing: ${component.designTokens.spacing.length}
- Layout: ${component.designTokens.layout.length}
`).join('\n')}
`;
  }

  /**
   * Create metadata for documentation
   */
  private createMetadata(options: DocumentationOptions): DocumentationMetadata {
    return {
      version: options.version || '1.0.0',
      generatedAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString(),
      author: options.author || 'Design System Audit Tool',
      designSystemVersion: options.designSystemVersion || '1.0.0'
    };
  }

  /**
   * Create navigation structure
   */
  private createNavigation(components: ComponentSpec[]): NavigationStructure {
    const categories = [...new Set(components.map(c => c.category))];
    const componentsByCategory = this.groupComponentsByCategory(components);

    return {
      sections: categories.map(category => ({
        title: category.charAt(0).toUpperCase() + category.slice(1),
        slug: category,
        items: componentsByCategory.get(category)?.map(component => ({
          title: component.name,
          slug: component.name.toLowerCase(),
          component: component.name,
          category
        })) || []
      })),
      categories
    };
  }

  /**
   * Categorize component based on name and analysis
   */
  private categorizeComponent(name: string, analysis: ComponentAnalysis): ComponentCategory {
    const lowerName = name.toLowerCase();
    
    if (lowerName.includes('button') || lowerName.includes('input') || lowerName.includes('form')) {
      return 'input';
    }
    if (lowerName.includes('nav') || lowerName.includes('menu') || lowerName.includes('breadcrumb')) {
      return 'navigation';
    }
    if (lowerName.includes('card') || lowerName.includes('table') || lowerName.includes('list')) {
      return 'display';
    }
    if (lowerName.includes('modal') || lowerName.includes('dialog') || lowerName.includes('tooltip')) {
      return 'overlay';
    }
    if (lowerName.includes('alert') || lowerName.includes('toast') || lowerName.includes('notification')) {
      return 'feedback';
    }
    if (lowerName.includes('grid') || lowerName.includes('container') || lowerName.includes('layout')) {
      return 'layout';
    }
    if (lowerName.includes('text') || lowerName.includes('heading') || lowerName.includes('typography')) {
      return 'typography';
    }
    if (lowerName.includes('image') || lowerName.includes('video') || lowerName.includes('media')) {
      return 'media';
    }
    
    return 'utility';
  }

  /**
   * Extract prop specifications from analysis
   */
  private extractPropSpecs(props: any[]): PropSpec[] {
    return props.map(prop => ({
      name: prop.name,
      type: prop.type || 'any',
      required: prop.required || false,
      defaultValue: prop.defaultValue,
      description: prop.description || `${prop.name} prop`,
      examples: prop.examples || [],
      validation: prop.validation
    }));
  }

  /**
   * Create component variants
   */
  private createVariants(variants: any[]): ComponentVariant[] {
    return variants.map(variant => ({
      name: variant.name,
      description: variant.description || `${variant.name} variant`,
      props: variant.props || {},
      preview: variant.preview || '',
      code: variant.code || '',
      designTokens: variant.designTokens || []
    }));
  }

  /**
   * Create component states
   */
  private createStates(states: any[]): ComponentState[] {
    return states.map(state => ({
      name: state.name,
      description: state.description || `${state.name} state`,
      trigger: state.trigger || 'user interaction',
      visualChanges: state.visualChanges || [],
      accessibility: state.accessibility || []
    }));
  }

  /**
   * Create component examples
   */
  private createExamples(componentName: string, examples: any[]): ComponentExample[] {
    return examples.map(example => ({
      title: example.title || `${componentName} Example`,
      description: example.description || `Basic usage of ${componentName}`,
      code: example.code || `<${componentName} />`,
      preview: example.preview || '',
      interactive: example.interactive || false,
      variants: example.variants
    }));
  }

  /**
   * Create usage guidelines
   */
  private createUsageGuidelines(componentName: string, analysis: ComponentAnalysis): UsageGuidelines {
    return {
      when: analysis.usage?.when || [`Use ${componentName} when you need...`],
      whenNot: analysis.usage?.whenNot || [`Don't use ${componentName} when...`],
      bestPractices: analysis.usage?.bestPractices || [`Follow design system guidelines`],
      commonMistakes: analysis.usage?.commonMistakes || [`Avoid overriding default styles`],
      relatedComponents: analysis.usage?.relatedComponents || []
    };
  }

  /**
   * Create accessibility specification
   */
  private createAccessibilitySpec(accessibility: any): AccessibilitySpec {
    return {
      ariaLabels: accessibility?.ariaLabels || [],
      keyboardNavigation: accessibility?.keyboardNavigation || [],
      screenReader: accessibility?.screenReader || [],
      colorContrast: accessibility?.colorContrast || [],
      focusManagement: accessibility?.focusManagement || []
    };
  }

  /**
   * Extract design token usage
   */
  private extractTokenUsage(tokens: any): ComponentTokenUsage {
    return {
      colors: tokens?.colors || [],
      typography: tokens?.typography || [],
      spacing: tokens?.spacing || [],
      layout: tokens?.layout || []
    };
  }

  /**
   * Group components by category
   */
  private groupComponentsByCategory(components: ComponentSpec[]): Map<ComponentCategory, ComponentSpec[]> {
    const grouped = new Map<ComponentCategory, ComponentSpec[]>();
    
    components.forEach(component => {
      if (!grouped.has(component.category)) {
        grouped.set(component.category, []);
      }
      grouped.get(component.category)!.push(component);
    });

    return grouped;
  }

  /**
   * Get category description
   */
  private getCategoryDescription(category: ComponentCategory): string {
    const descriptions = {
      layout: 'structural organization and spacing',
      navigation: 'user movement and wayfinding',
      input: 'user data collection and interaction',
      display: 'content presentation and visualization',
      feedback: 'system status and user notifications',
      overlay: 'modal content and contextual information',
      media: 'multimedia content display',
      typography: 'text styling and hierarchy',
      utility: 'helper functions and utilities'
    };
    
    return descriptions[category] || 'component functionality';
  }

  /**
   * Generate navigation index JSON
   */
  private generateNavigationIndex(navigation: NavigationStructure): string {
    return JSON.stringify(navigation, null, 2);
  }
}

// Supporting interfaces
interface DocumentationOptions {
  version?: string;
  author?: string;
  designSystemVersion?: string;
}

interface ComponentAnalysis {
  description?: string;
  props: any[];
  variants: any[];
  states: any[];
  examples: any[];
  usage?: {
    when?: string[];
    whenNot?: string[];
    bestPractices?: string[];
    commonMistakes?: string[];
    relatedComponents?: string[];
  };
  accessibility?: any;
  tokens?: any;
}