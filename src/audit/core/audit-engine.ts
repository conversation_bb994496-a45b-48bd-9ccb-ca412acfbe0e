// Core audit engine implementation

import { 
  AuditEngine, 
  <PERSON><PERSON><PERSON>, 
  AuditResult, 
  AuditReport, 
  AnalyzerResult, 
  AuditSummary,
  Severity,
  Category,
  Recommendation,
  RemediationPlan,
  RemediationTask,
  AuditConfig
} from '../types';
import { FileScanner } from './file-scanner';
import { auditLogger } from './logger';
import { ReportGenerator } from './report-generator';

export class DesignSystemAuditEngine implements AuditEngine {
  private analyzers: Map<string, Analyzer> = new Map();
  private fileScanner: FileScanner;
  private config: AuditConfig;
  private reportGenerator: ReportGenerator;

  constructor(config: Partial<AuditConfig> = {}) {
    this.config = {
      projectPath: process.cwd(),
      includePatterns: [
        '**/*.tsx',
        '**/*.jsx',
        '**/*.ts',
        '**/*.js',
        '**/*.css',
        '**/*.scss',
        '**/*.module.css'
      ],
      excludePatterns: [
        '**/node_modules/**',
        '**/dist/**',
        '**/build/**',
        '**/.git/**',
        '**/coverage/**',
        '**/*.test.*',
        '**/*.spec.*'
      ],
      analyzers: [],
      outputFormat: 'json',
      logLevel: 'info',
      ...config
    };

    this.fileScanner = new FileScanner({
      includePatterns: this.config.includePatterns,
      excludePatterns: this.config.excludePatterns
    });

    this.reportGenerator = new ReportGenerator();

    auditLogger.info('Audit engine initialized', { 
      projectPath: this.config.projectPath,
      analyzers: this.config.analyzers 
    });
  }

  registerAnalyzer(analyzer: Analyzer): void {
    if (this.analyzers.has(analyzer.name)) {
      auditLogger.warn('Analyzer already registered, overwriting', { 
        analyzerName: analyzer.name 
      });
    }

    this.analyzers.set(analyzer.name, analyzer);
    auditLogger.info('Analyzer registered', { analyzerName: analyzer.name });
  }

  async scanProject(projectPath: string = this.config.projectPath): Promise<AuditResult> {
    const startTime = Date.now();
    auditLogger.info('Starting project audit', { projectPath });

    try {
      // Step 1: Scan files
      const files = await this.fileScanner.scanDirectory(projectPath);
      auditLogger.info('File scanning completed', { fileCount: files.length });

      // Step 2: Run analyzers
      const analyzerResults: AnalyzerResult[] = [];
      
      for (const [name, analyzer] of this.analyzers) {
        try {
          auditLogger.info('Running analyzer', { analyzerName: name });
          const result = await analyzer.analyze(files);
          analyzerResults.push(result);
          auditLogger.info('Analyzer completed', { 
            analyzerName: name, 
            issuesFound: result.issues.length 
          });
        } catch (error) {
          auditLogger.error('Analyzer failed', error as Error, { analyzerName: name });
          // Continue with other analyzers even if one fails
        }
      }

      // Step 3: Generate comprehensive report using new report generator
      const report = this.reportGenerator.generateComprehensiveReport(analyzerResults);
      
      // Step 4: Create audit result
      const executionTime = Date.now() - startTime;
      const auditResult: AuditResult = {
        summary: {
          ...report.summary,
          totalFiles: files.length,
          executionTime,
          timestamp: new Date()
        },
        issues: report.results.flatMap(result => result.issues),
        recommendations: report.recommendations,
        remediationPlan: report.remediationPlan
      };

      auditLogger.info('Project audit completed', { 
        totalIssues: auditResult.issues.length,
        executionTime 
      });

      return auditResult;

    } catch (error) {
      auditLogger.error('Project audit failed', error as Error, { projectPath });
      throw error;
    }
  }

  generateReport(results: AnalyzerResult[]): AuditReport {
    auditLogger.info('Delegating to comprehensive report generator', { analyzerCount: results.length });
    return this.reportGenerator.generateComprehensiveReport(results);
  }

  private generateSummary(results: AnalyzerResult[]): AuditSummary {
    const allIssues = results.flatMap(result => result.issues);
    
    const severityBreakdown: Record<Severity, number> = {
      [Severity.LOW]: 0,
      [Severity.MEDIUM]: 0,
      [Severity.HIGH]: 0,
      [Severity.CRITICAL]: 0
    };

    const categoryBreakdown: Record<Category, number> = {
      [Category.VISUAL]: 0,
      [Category.CONTENT]: 0,
      [Category.COMPONENT]: 0,
      [Category.ACCESSIBILITY]: 0
    };

    allIssues.forEach(issue => {
      severityBreakdown[issue.severity]++;
      categoryBreakdown[issue.category]++;
    });

    const totalExecutionTime = results.reduce((sum, result) => sum + result.executionTime, 0);

    return {
      totalFiles: 0, // Will be set by the calling method
      totalIssues: allIssues.length,
      severityBreakdown,
      categoryBreakdown,
      executionTime: totalExecutionTime,
      timestamp: new Date()
    };
  }

  private generateRecommendations(results: AnalyzerResult[]): Recommendation[] {
    const recommendations: Recommendation[] = [];
    const allIssues = results.flatMap(result => result.issues);

    // Group issues by type and severity to generate recommendations
    const issueGroups = this.groupIssuesByTypeAndSeverity(allIssues);

    let recommendationId = 1;
    for (const [groupKey, issues] of issueGroups) {
      const [type, severity] = groupKey.split('_');
      
      const recommendation: Recommendation = {
        id: `rec_${recommendationId++}`,
        title: this.getRecommendationTitle(type, severity),
        description: this.getRecommendationDescription(type, issues.length),
        priority: this.calculatePriority(severity, issues.length),
        estimatedEffort: this.estimateEffort(type, issues.length),
        relatedIssues: issues.map(issue => issue.id)
      };

      recommendations.push(recommendation);
    }

    return recommendations.sort((a, b) => b.priority - a.priority);
  }

  private generateRemediationPlan(results: AnalyzerResult[]): RemediationPlan {
    const tasks: RemediationTask[] = [];
    const allIssues = results.flatMap(result => result.issues);

    // Create tasks based on issue types and complexity
    const issuesByType = this.groupIssuesByType(allIssues);
    
    let taskId = 1;
    for (const [type, issues] of issuesByType) {
      const task: RemediationTask = {
        id: `task_${taskId++}`,
        description: this.getTaskDescription(type, issues.length),
        type,
        effort: this.estimateTaskEffort(type, issues.length),
        dependencies: this.getTaskDependencies(type),
        autoFixable: issues.some(issue => issue.autoFixable)
      };

      tasks.push(task);
    }

    const totalEffort = tasks.reduce((sum, task) => sum + task.effort, 0);
    const averagePriority = tasks.length > 0 ? 
      tasks.reduce((sum, task) => sum + (task.effort > 5 ? 8 : 5), 0) / tasks.length : 0;

    return {
      tasks,
      dependencies: tasks.map(task => ({
        taskId: task.id,
        dependsOn: task.dependencies
      })),
      estimatedEffort: totalEffort,
      priority: Math.round(averagePriority)
    };
  }

  private groupIssuesByTypeAndSeverity(issues: any[]) {
    const groups = new Map();
    issues.forEach(issue => {
      const key = `${issue.type}_${issue.severity}`;
      if (!groups.has(key)) {
        groups.set(key, []);
      }
      groups.get(key).push(issue);
    });
    return groups;
  }

  private groupIssuesByType(issues: any[]) {
    const groups = new Map();
    issues.forEach(issue => {
      if (!groups.has(issue.type)) {
        groups.set(issue.type, []);
      }
      groups.get(issue.type).push(issue);
    });
    return groups;
  }

  private getRecommendationTitle(type: string, severity: string): string {
    const titles: Record<string, string> = {
      'color_inconsistency': 'Standardize Color Usage',
      'spacing_inconsistency': 'Implement Consistent Spacing',
      'typography_inconsistency': 'Unify Typography System',
      'component_inconsistency': 'Standardize Component Patterns',
      'content_inconsistency': 'Align Content Standards'
    };
    return titles[type] || 'Address Design Inconsistencies';
  }

  private getRecommendationDescription(type: string, count: number): string {
    return `Found ${count} instances of ${type.replace('_', ' ')} that should be addressed to improve design consistency.`;
  }

  private calculatePriority(severity: string, count: number): number {
    const severityWeights = { critical: 10, high: 8, medium: 5, low: 2 };
    const baseWeight = severityWeights[severity as keyof typeof severityWeights] || 1;
    return Math.min(10, baseWeight + Math.floor(count / 5));
  }

  private estimateEffort(type: string, count: number): number {
    const baseEfforts: Record<string, number> = {
      'color_inconsistency': 2,
      'spacing_inconsistency': 3,
      'typography_inconsistency': 4,
      'component_inconsistency': 6,
      'content_inconsistency': 3
    };
    const baseEffort = baseEfforts[type] || 3;
    return Math.max(1, baseEffort + Math.floor(count / 10));
  }

  private getTaskDescription(type: string, count: number): string {
    const descriptions: Record<string, string> = {
      'color_inconsistency': `Fix ${count} color inconsistencies by implementing design tokens`,
      'spacing_inconsistency': `Resolve ${count} spacing issues using standardized spacing system`,
      'typography_inconsistency': `Address ${count} typography inconsistencies with unified type scale`,
      'component_inconsistency': `Standardize ${count} component implementation issues`,
      'content_inconsistency': `Align ${count} content inconsistencies with style guide`
    };
    return descriptions[type] || `Address ${count} design inconsistencies`;
  }

  private estimateTaskEffort(type: string, count: number): number {
    return this.estimateEffort(type, count);
  }

  private getTaskDependencies(type: string): string[] {
    const dependencies: Record<string, string[]> = {
      'color_inconsistency': ['design_tokens'],
      'spacing_inconsistency': ['design_tokens'],
      'typography_inconsistency': ['design_tokens'],
      'component_inconsistency': ['design_tokens', 'component_library'],
      'content_inconsistency': ['style_guide']
    };
    return dependencies[type] || [];
  }

  getRegisteredAnalyzers(): string[] {
    return Array.from(this.analyzers.keys());
  }

  getConfig(): AuditConfig {
    return { ...this.config };
  }
}