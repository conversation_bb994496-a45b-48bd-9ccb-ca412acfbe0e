/**
 * Design Token Validator
 * Validates design token usage and provides compliance checking
 */

import { DesignTokenSystem } from './types.js';

export interface TokenValidationResult {
  isValid: boolean;
  violations: TokenViolation[];
  compliance: ComplianceReport;
}

export interface TokenViolation {
  type: 'color' | 'typography' | 'spacing';
  severity: 'error' | 'warning' | 'info';
  message: string;
  location: {
    file: string;
    line: number;
    column: number;
  };
  suggestion: string;
  autoFixable: boolean;
}

export interface ComplianceReport {
  colorCompliance: number;
  typographyCompliance: number;
  spacingCompliance: number;
  overallCompliance: number;
  totalIssues: number;
  criticalIssues: number;
}

export class DesignTokenValidator {
  private tokens: DesignTokenSystem;

  constructor(tokens: DesignTokenSystem) {
    this.tokens = tokens;
  }

  /**
   * Validates color usage against design tokens
   */
  validateColorUsage(cssContent: string, filePath: string): TokenViolation[] {
    const violations: TokenViolation[] = [];
    const lines = cssContent.split('\n');

    // Regex patterns for hardcoded colors
    const hexColorPattern = /#([0-9a-f]{3}|[0-9a-f]{6}|[0-9a-f]{8})\b/gi;
    const rgbColorPattern = /rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)/gi;
    const rgbaColorPattern = /rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\)/gi;

    lines.forEach((line, lineIndex) => {
      // Check for hardcoded hex colors
      let match;
      while ((match = hexColorPattern.exec(line)) !== null) {
        const color = match[0];
        if (!this.isTokenizedColor(color)) {
          violations.push({
            type: 'color',
            severity: 'warning',
            message: `Hardcoded color value "${color}" should use design token`,
            location: {
              file: filePath,
              line: lineIndex + 1,
              column: match.index + 1
            },
            suggestion: this.suggestColorToken(color),
            autoFixable: true
          });
        }
      }

      // Check for RGB/RGBA colors
      [rgbColorPattern, rgbaColorPattern].forEach(pattern => {
        while ((match = pattern.exec(line)) !== null) {
          const color = match[0];
          violations.push({
            type: 'color',
            severity: 'warning',
            message: `RGB/RGBA color "${color}" should use design token`,
            location: {
              file: filePath,
              line: lineIndex + 1,
              column: match.index + 1
            },
            suggestion: 'Use CSS custom property like var(--color-primary-500)',
            autoFixable: false
          });
        }
      });
    });

    return violations;
  }

  /**
   * Validates typography usage against design tokens
   */
  validateTypographyUsage(cssContent: string, filePath: string): TokenViolation[] {
    const violations: TokenViolation[] = [];
    const lines = cssContent.split('\n');

    // Patterns for hardcoded typography values
    const fontSizePattern = /font-size:\s*(\d+(?:\.\d+)?(?:px|rem|em))/gi;
    const fontWeightPattern = /font-weight:\s*(\d+)/gi;
    const lineHeightPattern = /line-height:\s*(\d+(?:\.\d+)?)/gi;

    lines.forEach((line, lineIndex) => {
      // Check for hardcoded font sizes
      let match;
      while ((match = fontSizePattern.exec(line)) !== null) {
        const fontSize = match[1];
        if (!this.isTokenizedFontSize(fontSize)) {
          violations.push({
            type: 'typography',
            severity: 'warning',
            message: `Hardcoded font-size "${fontSize}" should use design token`,
            location: {
              file: filePath,
              line: lineIndex + 1,
              column: match.index + 1
            },
            suggestion: this.suggestFontSizeToken(fontSize),
            autoFixable: true
          });
        }
      }

      // Check for hardcoded font weights
      while ((match = fontWeightPattern.exec(line)) !== null) {
        const fontWeight = parseInt(match[1]);
        if (!this.isTokenizedFontWeight(fontWeight)) {
          violations.push({
            type: 'typography',
            severity: 'info',
            message: `Font weight "${fontWeight}" could use design token`,
            location: {
              file: filePath,
              line: lineIndex + 1,
              column: match.index + 1
            },
            suggestion: this.suggestFontWeightToken(fontWeight),
            autoFixable: true
          });
        }
      }
    });

    return violations;
  }

  /**
   * Validates spacing usage against design tokens
   */
  validateSpacingUsage(cssContent: string, filePath: string): TokenViolation[] {
    const violations: TokenViolation[] = [];
    const lines = cssContent.split('\n');

    // Patterns for spacing properties
    const spacingPattern = /(margin|padding|gap|top|right|bottom|left):\s*(\d+(?:\.\d+)?(?:px|rem|em))/gi;

    lines.forEach((line, lineIndex) => {
      let match;
      while ((match = spacingPattern.exec(line)) !== null) {
        const property = match[1];
        const value = match[2];
        
        if (!this.isTokenizedSpacing(value)) {
          violations.push({
            type: 'spacing',
            severity: 'warning',
            message: `Hardcoded ${property} value "${value}" should use design token`,
            location: {
              file: filePath,
              line: lineIndex + 1,
              column: match.index + 1
            },
            suggestion: this.suggestSpacingToken(value),
            autoFixable: true
          });
        }
      }
    });

    return violations;
  }

  /**
   * Generates comprehensive validation report
   */
  generateComplianceReport(violations: TokenViolation[], totalElements: number): ComplianceReport {
    const colorViolations = violations.filter(v => v.type === 'color');
    const typographyViolations = violations.filter(v => v.type === 'typography');
    const spacingViolations = violations.filter(v => v.type === 'spacing');
    const criticalIssues = violations.filter(v => v.severity === 'error').length;

    const colorCompliance = Math.max(0, 100 - (colorViolations.length / totalElements) * 100);
    const typographyCompliance = Math.max(0, 100 - (typographyViolations.length / totalElements) * 100);
    const spacingCompliance = Math.max(0, 100 - (spacingViolations.length / totalElements) * 100);
    const overallCompliance = (colorCompliance + typographyCompliance + spacingCompliance) / 3;

    return {
      colorCompliance: Math.round(colorCompliance),
      typographyCompliance: Math.round(typographyCompliance),
      spacingCompliance: Math.round(spacingCompliance),
      overallCompliance: Math.round(overallCompliance),
      totalIssues: violations.length,
      criticalIssues
    };
  }

  private isTokenizedColor(color: string): boolean {
    // Check if color matches any token values
    const allColors = [
      ...Object.values(this.tokens.colors.primary),
      ...Object.values(this.tokens.colors.secondary),
      ...Object.values(this.tokens.colors.neutral),
      ...Object.values(this.tokens.colors.surface)
    ];
    
    return allColors.includes(color.toLowerCase());
  }

  private isTokenizedFontSize(fontSize: string): boolean {
    return Object.values(this.tokens.typography.fontSizes).includes(fontSize);
  }

  private isTokenizedFontWeight(fontWeight: number): boolean {
    return Object.values(this.tokens.typography.fontWeights).includes(fontWeight);
  }

  private isTokenizedSpacing(spacing: string): boolean {
    return Object.values(this.tokens.spacing.scale).includes(spacing);
  }

  private suggestColorToken(color: string): string {
    // Simple color matching logic - in practice, this would be more sophisticated
    const colorLower = color.toLowerCase();
    
    if (colorLower.includes('blue') || colorLower === '#3b82f6') {
      return 'var(--color-primary-500)';
    }
    if (colorLower.includes('gray') || colorLower.includes('grey')) {
      return 'var(--color-neutral-500)';
    }
    
    return 'Use appropriate color token from design system';
  }

  private suggestFontSizeToken(fontSize: string): string {
    const sizeMap: Record<string, string> = {
      '12px': 'var(--font-size-xs)',
      '14px': 'var(--font-size-sm)',
      '16px': 'var(--font-size-base)',
      '18px': 'var(--font-size-lg)',
      '20px': 'var(--font-size-xl)',
      '24px': 'var(--font-size-2xl)',
      '0.75rem': 'var(--font-size-xs)',
      '0.875rem': 'var(--font-size-sm)',
      '1rem': 'var(--font-size-base)',
      '1.125rem': 'var(--font-size-lg)',
      '1.25rem': 'var(--font-size-xl)',
      '1.5rem': 'var(--font-size-2xl)'
    };

    return sizeMap[fontSize] || 'Use appropriate font-size token';
  }

  private suggestFontWeightToken(fontWeight: number): string {
    const weightMap: Record<number, string> = {
      100: 'var(--font-weight-thin)',
      200: 'var(--font-weight-extralight)',
      300: 'var(--font-weight-light)',
      400: 'var(--font-weight-normal)',
      500: 'var(--font-weight-medium)',
      600: 'var(--font-weight-semibold)',
      700: 'var(--font-weight-bold)',
      800: 'var(--font-weight-extrabold)',
      900: 'var(--font-weight-black)'
    };

    return weightMap[fontWeight] || 'Use appropriate font-weight token';
  }

  private suggestSpacingToken(spacing: string): string {
    const spacingMap: Record<string, string> = {
      '4px': 'var(--spacing-1)',
      '8px': 'var(--spacing-2)',
      '12px': 'var(--spacing-3)',
      '16px': 'var(--spacing-4)',
      '20px': 'var(--spacing-5)',
      '24px': 'var(--spacing-6)',
      '32px': 'var(--spacing-8)',
      '0.25rem': 'var(--spacing-1)',
      '0.5rem': 'var(--spacing-2)',
      '0.75rem': 'var(--spacing-3)',
      '1rem': 'var(--spacing-4)',
      '1.25rem': 'var(--spacing-5)',
      '1.5rem': 'var(--spacing-6)',
      '2rem': 'var(--spacing-8)'
    };

    return spacingMap[spacing] || 'Use appropriate spacing token';
  }
}