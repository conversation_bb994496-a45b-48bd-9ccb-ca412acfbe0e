export interface MonitoringConfig {
  enabled: boolean;
  checkInterval: number; // in milliseconds
  thresholds: {
    errorCount: number;
    warningCount: number;
    criticalSeverityLimit: number;
  };
  notifications: {
    email?: string[];
    webhook?: string;
    slack?: {
      webhookUrl: string;
      channel: string;
    };
  };
  cicd: {
    failOnErrors: boolean;
    failOnWarnings: boolean;
    generateReports: boolean;
    reportPath: string;
  };
}

export interface MonitoringResult {
  timestamp: Date;
  newIssues: Issue[];
  resolvedIssues: Issue[];
  totalIssues: number;
  severityBreakdown: Record<Severity, number>;
  complianceScore: number;
  trend: 'improving' | 'degrading' | 'stable';
}

export interface Alert {
  id: string;
  type: 'threshold_exceeded' | 'new_critical_issue' | 'compliance_degraded';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  metadata: Record<string, any>;
}

export interface Issue {
  id: string;
  type: string;
  severity: Severity;
  category: string;
  description: string;
  location: {
    filePath: string;
    lineNumber: number;
    columnNumber: number;
  };
  hash: string; // for tracking issue persistence
}

export type Severity = 'low' | 'medium' | 'high' | 'critical';

export interface ComplianceMetrics {
  totalFiles: number;
  compliantFiles: number;
  compliancePercentage: number;
  issuesByCategory: Record<string, number>;
  issuesBySeverity: Record<Severity, number>;
  trendData: {
    date: Date;
    complianceScore: number;
    issueCount: number;
  }[];
}