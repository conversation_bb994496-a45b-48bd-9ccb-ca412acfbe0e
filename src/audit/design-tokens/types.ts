/**
 * Design Token System Types
 * Defines the structure for comprehensive design tokens including colors, typography, and spacing
 */

export interface DesignTokenSystem {
  colors: ColorTokens;
  typography: TypographyTokens;
  spacing: SpacingTokens;
  layout: LayoutTokens;
  metadata: TokenMetadata;
}

export interface TokenMetadata {
  version: string;
  generatedAt: string;
  source: string;
}

// Color Token System
export interface ColorTokens {
  primary: ColorScale;
  secondary: ColorScale;
  semantic: SemanticColors;
  neutral: ColorScale;
  surface: SurfaceColors;
}

export interface ColorScale {
  50: string;
  100: string;
  200: string;
  300: string;
  400: string;
  500: string;
  600: string;
  700: string;
  800: string;
  900: string;
  950: string;
}

export interface SemanticColors {
  success: ColorVariant;
  warning: ColorVariant;
  error: ColorVariant;
  info: ColorVariant;
}

export interface ColorVariant {
  light: string;
  default: string;
  dark: string;
  contrast: string;
}

export interface SurfaceColors {
  background: string;
  foreground: string;
  card: string;
  cardForeground: string;
  popover: string;
  popoverForeground: string;
  muted: string;
  mutedForeground: string;
  accent: string;
  accentForeground: string;
  border: string;
  input: string;
  ring: string;
}

// Typography Token System
export interface TypographyTokens {
  fontFamilies: FontFamilies;
  fontSizes: FontSizes;
  fontWeights: FontWeights;
  lineHeights: LineHeights;
  letterSpacing: LetterSpacing;
  typeScale: TypeScale;
}

export interface FontFamilies {
  sans: string[];
  serif: string[];
  mono: string[];
  display: string[];
}

export interface FontSizes {
  xs: string;
  sm: string;
  base: string;
  lg: string;
  xl: string;
  '2xl': string;
  '3xl': string;
  '4xl': string;
  '5xl': string;
  '6xl': string;
  '7xl': string;
  '8xl': string;
  '9xl': string;
}

export interface FontWeights {
  thin: number;
  extralight: number;
  light: number;
  normal: number;
  medium: number;
  semibold: number;
  bold: number;
  extrabold: number;
  black: number;
}

export interface LineHeights {
  none: number;
  tight: number;
  snug: number;
  normal: number;
  relaxed: number;
  loose: number;
}

export interface LetterSpacing {
  tighter: string;
  tight: string;
  normal: string;
  wide: string;
  wider: string;
  widest: string;
}

export interface TypeScale {
  h1: TypographyStyle;
  h2: TypographyStyle;
  h3: TypographyStyle;
  h4: TypographyStyle;
  h5: TypographyStyle;
  h6: TypographyStyle;
  body: TypographyStyle;
  bodyLarge: TypographyStyle;
  bodySmall: TypographyStyle;
  caption: TypographyStyle;
  overline: TypographyStyle;
}

export interface TypographyStyle {
  fontSize: string;
  fontWeight: number;
  lineHeight: number;
  letterSpacing: string;
  fontFamily: string[];
}

// Spacing Token System
export interface SpacingTokens {
  scale: SpacingScale;
  semantic: SemanticSpacing;
  layout: LayoutSpacing;
}

export interface SpacingScale {
  0: string;
  px: string;
  0.5: string;
  1: string;
  1.5: string;
  2: string;
  2.5: string;
  3: string;
  3.5: string;
  4: string;
  5: string;
  6: string;
  7: string;
  8: string;
  9: string;
  10: string;
  11: string;
  12: string;
  14: string;
  16: string;
  20: string;
  24: string;
  28: string;
  32: string;
  36: string;
  40: string;
  44: string;
  48: string;
  52: string;
  56: string;
  60: string;
  64: string;
  72: string;
  80: string;
  96: string;
}

export interface SemanticSpacing {
  component: {
    padding: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
    };
    margin: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
    };
    gap: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
    };
  };
  layout: {
    section: string;
    container: string;
    content: string;
  };
}

export interface LayoutSpacing {
  containerMaxWidth: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
  };
  containerPadding: {
    mobile: string;
    tablet: string;
    desktop: string;
  };
}

// Layout and Grid System
export interface LayoutTokens {
  grid: GridSystem;
  breakpoints: Breakpoints;
  containers: ContainerSystem;
  patterns: LayoutPatterns;
  validation: LayoutValidation;
}

export interface GridSystem {
  columns: {
    default: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  gutters: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  margins: {
    mobile: string;
    tablet: string;
    desktop: string;
    wide: string;
  };
  baseline: {
    unit: string;
    scale: number;
  };
}

export interface Breakpoints {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  ranges: {
    mobile: string;
    tablet: string;
    desktop: string;
    wide: string;
  };
}

export interface ContainerSystem {
  maxWidths: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
    full: string;
  };
  padding: {
    mobile: string;
    tablet: string;
    desktop: string;
  };
  centering: {
    auto: string;
    flex: FlexCentering;
    grid: GridCentering;
  };
}

export interface FlexCentering {
  horizontal: string;
  vertical: string;
  both: string;
}

export interface GridCentering {
  horizontal: string;
  vertical: string;
  both: string;
}

export interface LayoutPatterns {
  stack: StackPattern;
  cluster: ClusterPattern;
  sidebar: SidebarPattern;
  switcher: SwitcherPattern;
  cover: CoverPattern;
  grid: GridPattern;
}

export interface StackPattern {
  gap: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  alignment: {
    start: string;
    center: string;
    end: string;
    stretch: string;
  };
}

export interface ClusterPattern {
  gap: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  justify: {
    start: string;
    center: string;
    end: string;
    between: string;
    around: string;
    evenly: string;
  };
  align: {
    start: string;
    center: string;
    end: string;
    baseline: string;
  };
}

export interface SidebarPattern {
  sidebarWidth: {
    narrow: string;
    default: string;
    wide: string;
  };
  gap: string;
  breakpoint: string;
  contentMinWidth: string;
}

export interface SwitcherPattern {
  threshold: string;
  gap: string;
  limit: number;
}

export interface CoverPattern {
  minHeight: {
    viewport: string;
    container: string;
    content: string;
  };
  padding: {
    top: string;
    bottom: string;
    sides: string;
  };
}

export interface GridPattern {
  minItemWidth: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
  };
  gap: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  autoFit: boolean;
  autoFill: boolean;
}

export interface LayoutValidation {
  rules: ValidationRule[];
  patterns: PatternValidation[];
  accessibility: AccessibilityValidation;
}

export interface ValidationRule {
  name: string;
  description: string;
  selector: string;
  property: string;
  expectedValues: string[];
  severity: 'error' | 'warning' | 'info';
}

export interface PatternValidation {
  pattern: string;
  requiredProperties: string[];
  forbiddenProperties: string[];
  childConstraints: ChildConstraint[];
}

export interface ChildConstraint {
  selector: string;
  maxCount?: number;
  minCount?: number;
  requiredProperties?: string[];
}

export interface AccessibilityValidation {
  focusManagement: FocusValidation;
  semanticStructure: SemanticValidation;
  responsiveDesign: ResponsiveValidation;
}

export interface FocusValidation {
  tabOrder: boolean;
  focusVisible: boolean;
  skipLinks: boolean;
}

export interface SemanticValidation {
  headingHierarchy: boolean;
  landmarkRoles: boolean;
  listStructure: boolean;
}

export interface ResponsiveValidation {
  minTouchTarget: string;
  maxLineLength: string;
  scalableText: boolean;
}