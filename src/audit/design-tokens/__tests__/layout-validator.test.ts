/**
 * Layout Pattern Validator Tests
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { LayoutPatternValidator } from '../layout-validator.js';
import { DesignTokenGenerator } from '../generator.js';

describe('LayoutPatternValidator', () => {
  let validator: LayoutPatternValidator;
  let layoutTokens: any;

  beforeEach(() => {
    const generator = new DesignTokenGenerator();
    const tokens = generator.generateTokenSystem();
    layoutTokens = tokens.layout;
    validator = new LayoutPatternValidator(layoutTokens);
  });

  describe('validateLayout', () => {
    it('should validate layout without violations for compliant CSS', () => {
      const compliantCSS = `
        .container {
          max-width: 1280px;
          margin: 0 auto;
        }
        
        @media (min-width: 768px) {
          .responsive {
            display: grid;
            gap: 1rem;
          }
        }
        
        .stack {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }
      `;

      const result = validator.validateLayout(compliantCSS, 'test.css');
      
      expect(result.isValid).toBe(true);
      expect(result.violations.filter(v => v.severity === 'error')).toHaveLength(0);
    });

    it('should detect breakpoint violations', () => {
      const nonCompliantCSS = `
        @media (min-width: 900px) {
          .custom-breakpoint {
            display: block;
          }
        }
      `;

      const result = validator.validateLayout(nonCompliantCSS, 'test.css');
      
      const breakpointViolations = result.violations.filter(v => v.type === 'breakpoint');
      expect(breakpointViolations).toHaveLength(1);
      expect(breakpointViolations[0].message).toContain('Non-standard breakpoint');
    });

    it('should detect container width violations', () => {
      const nonCompliantCSS = `
        .custom-container {
          max-width: 1100px;
        }
      `;

      const result = validator.validateLayout(nonCompliantCSS, 'test.css');
      
      const containerViolations = result.violations.filter(v => v.type === 'container');
      expect(containerViolations).toHaveLength(1);
      expect(containerViolations[0].message).toContain('could use standard container token');
    });

    it('should detect grid gap violations', () => {
      const nonCompliantCSS = `
        .grid {
          display: grid;
          gap: 18px;
        }
      `;

      const result = validator.validateLayout(nonCompliantCSS, 'test.css');
      
      const gridViolations = result.violations.filter(v => v.type === 'grid');
      expect(gridViolations).toHaveLength(1);
      expect(gridViolations[0].message).toContain('should use standardized spacing token');
    });

    it('should detect accessibility violations for small touch targets', () => {
      const nonCompliantCSS = `
        .small-button {
          width: 30px;
          height: 30px;
        }
      `;

      const result = validator.validateLayout(nonCompliantCSS, 'test.css');
      
      const accessibilityViolations = result.violations.filter(v => v.type === 'accessibility');
      expect(accessibilityViolations.length).toBeGreaterThan(0);
      expect(accessibilityViolations[0].message).toContain('below minimum 44px');
    });

    it('should detect excessive line length violations', () => {
      const nonCompliantCSS = `
        .long-text {
          max-width: 100ch;
        }
      `;

      const result = validator.validateLayout(nonCompliantCSS, 'test.css');
      
      const accessibilityViolations = result.violations.filter(v => v.type === 'accessibility');
      expect(accessibilityViolations).toHaveLength(1);
      expect(accessibilityViolations[0].message).toContain('exceeds recommended 75ch');
    });
  });

  describe('pattern analysis', () => {
    it('should analyze stack pattern usage', () => {
      const cssWithStack = `
        .stack {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }
      `;

      const result = validator.validateLayout(cssWithStack, 'test.css');
      
      const stackPattern = result.patterns.find(p => p.pattern === 'stack');
      expect(stackPattern).toBeDefined();
      expect(stackPattern?.usage).toBe(1);
      expect(stackPattern?.compliance).toBe(100);
    });

    it('should detect pattern violations', () => {
      const cssWithIncompleteStack = `
        .stack {
          display: block;
        }
      `;

      const result = validator.validateLayout(cssWithIncompleteStack, 'test.css');
      
      const patternViolations = result.violations.filter(v => v.type === 'pattern');
      expect(patternViolations.length).toBeGreaterThan(0);
      expect(patternViolations[0].message).toContain('missing required properties');
    });
  });

  describe('compliance reporting', () => {
    it('should generate comprehensive compliance report', () => {
      const mixedCSS = `
        .container {
          max-width: 1100px;
        }
        
        @media (min-width: 900px) {
          .responsive {
            display: grid;
            gap: 18px;
          }
        }
        
        .small-target {
          width: 30px;
          height: 30px;
        }
      `;

      const result = validator.validateLayout(mixedCSS, 'test.css');
      
      expect(result.compliance).toBeDefined();
      expect(result.compliance.overallCompliance).toBeGreaterThanOrEqual(0);
      expect(result.compliance.overallCompliance).toBeLessThanOrEqual(100);
      expect(result.compliance.totalIssues).toBeGreaterThan(0);
      
      expect(result.compliance).toHaveProperty('breakpointCompliance');
      expect(result.compliance).toHaveProperty('containerCompliance');
      expect(result.compliance).toHaveProperty('gridCompliance');
      expect(result.compliance).toHaveProperty('patternCompliance');
      expect(result.compliance).toHaveProperty('accessibilityCompliance');
    });

    it('should provide actionable suggestions', () => {
      const nonCompliantCSS = `
        @media (min-width: 900px) {
          .test { display: block; }
        }
      `;

      const result = validator.validateLayout(nonCompliantCSS, 'test.css');
      
      expect(result.violations).toHaveLength(1);
      expect(result.violations[0].suggestion).toContain('breakpoint');
      expect(result.violations[0].autoFixable).toBe(true);
    });
  });

  describe('helper methods', () => {
    it('should correctly identify standard breakpoints', () => {
      const standardBreakpoints = ['640px', '768px', '1024px', '1280px', '1536px'];
      const nonStandardBreakpoints = ['900px', '1100px', '500px'];

      // Test through validation since helper methods are private
      standardBreakpoints.forEach(bp => {
        const css = `@media (min-width: ${bp}) { .test { display: block; } }`;
        const result = validator.validateLayout(css, 'test.css');
        const breakpointViolations = result.violations.filter(v => v.type === 'breakpoint');
        expect(breakpointViolations).toHaveLength(0);
      });

      nonStandardBreakpoints.forEach(bp => {
        const css = `@media (min-width: ${bp}) { .test { display: block; } }`;
        const result = validator.validateLayout(css, 'test.css');
        const breakpointViolations = result.violations.filter(v => v.type === 'breakpoint');
        expect(breakpointViolations.length).toBeGreaterThan(0);
      });
    });

    it('should provide appropriate suggestions for different values', () => {
      const testCases = [
        { css: '@media (min-width: 500px) { .test { display: block; } }', expectedSuggestion: 'sm breakpoint' },
        { css: '@media (min-width: 800px) { .test { display: block; } }', expectedSuggestion: 'lg breakpoint' },
        { css: '@media (min-width: 1100px) { .test { display: block; } }', expectedSuggestion: 'xl breakpoint' }
      ];

      testCases.forEach(({ css, expectedSuggestion }) => {
        const result = validator.validateLayout(css, 'test.css');
        const violation = result.violations.find(v => v.type === 'breakpoint');
        expect(violation?.suggestion).toContain(expectedSuggestion);
      });
    });
  });
});