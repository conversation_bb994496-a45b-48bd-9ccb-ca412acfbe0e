import * as React from "react";

interface UseRevealOnScrollOptions extends IntersectionObserverInit {
  delay?: number; // Optional delay in milliseconds
}

export function useRevealOnScroll<T extends HTMLElement>(
  options?: UseRevealOnScrollOptions
) {
  const [isVisible, setIsVisible] = React.useState(false);
  const ref = React.useRef<T | null>(null);

  React.useEffect(() => {
    const currentRef = ref.current;
    if (!currentRef) {
      return;
    }

    const { delay, ...observerOptions } = options || {};

    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting) {
        if (delay) {
          setTimeout(() => setIsVisible(true), delay);
        } else {
          setIsVisible(true);
        }
      } else {
        // Optionally, reset visibility when element scrolls out of view
        // setIsVisible(false);
      }
    }, observerOptions);

    observer.observe(currentRef);

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [options]);

  return { ref, isVisible };
}
