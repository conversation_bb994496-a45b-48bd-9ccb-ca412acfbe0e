# 702Hockey - ELEV802 Vegas Hockey Training Facility

## Project Overview

702Hockey is a modern, responsive website for a hockey training facility (ELEV802) in Las Vegas. It showcases the facility's services, coaches, and allows users to schedule training sessions.

### Website URL

[702HOCKEY](https://702hockey.com/)

## Technologies Used

This project is built with:

- **React** (v18) with **TypeScript** - A JavaScript library for building user interfaces
- **Vite** (v6.3+) - Next Generation Frontend Tooling for fast development and optimized builds
- **Tailwind CSS** - A utility-first CSS framework for rapidly building custom designs
- **shadcn-ui** - Re-usable components built with Radix UI and Tailwind CSS
- **React Router** (v6) - For client-side routing and navigation
- **TanStack Query** (v5) - For efficient data fetching and state management
- **React Hook Form** - For flexible and efficient form handling
- **date-fns** - For comprehensive date manipulation

## Website Structure

Single-page application with sections:
1. **Hero Section** - YouTube video background (hockey training footage) with 70vh height
2. **Why Choose Us** - Facility's value propositions
3. **Facility Section** - Training facility features and amenities
4. **Training Programs** - Two main offerings:
   - Individual Training (one-on-one sessions)
   - Small Group Training (team-focused)
5. **Coaches Section** - Staff profiles and credentials
6. **Testimonials** - Yelp reviews with 5-star ratings
7. **Contact Form** - Inquiry form with service selection options

## Design Elements

- **Color Scheme**: Navy blue and white
- **Modern UI** with animations (reveal effects on scroll)
- **Responsive design** with mobile menu
- **Interactive elements** with smooth scrolling
- **YouTube video background** in hero section (muted, looping)

## Project Structure

```
src/
├── assets/            # Images and static resources
├── components/        # Reusable UI components
│   ├── admin/         # Admin dashboard components
│   ├── client-management/ # Client management components
│   ├── scheduling/    # Calendar and booking components
│   └── ui/            # shadcn UI components
├── data/              # Static data (coaches, testimonials)
├── hooks/             # Custom React hooks
├── lib/               # Utility libraries
├── pages/             # Page components
│   └── admin/         # Admin pages
├── types/             # TypeScript type definitions
└── utils/             # Helper functions
```

## Getting Started

### Development Setup

```sh
# Clone the repository
git clone https://github.com/nicholasg-dev/702Hockey.git

# Navigate to the project directory
cd 702Hockey

# Install dependencies
npm install

# Start the development server
npm run dev
```

The application will be available at `http://localhost:8080`

### Build for Production

```sh
# Create an optimized production build
npm run build

# Preview the production build locally
npm run preview
```

## Key Features

- **Wix Scheduling Integration**: Online booking for various training programs via embedded Wix calendar
- **Client Management**: Tools for managing client information and documents
- **Admin Dashboard**: Administrative interface for managing schedules and clients
- **Responsive Design**: Optimized for all device sizes
- **Performance Optimized**: Fast loading times and smooth animations

## Recent Optimizations

As part of our ongoing maintenance efforts, we've recently:

1. **Removed Unused Dependencies**:
   - Cleaned up package.json to remove unnecessary packages
   - Reduced bundle size and improved load times

2. **Streamlined UI Components**:
   - Removed 15 unused UI components (1,500+ lines of code)
   - Simplified the codebase for better maintainability

3. **Code Organization**:
   - Eliminated duplicate implementations
   - Standardized import patterns

## Deployment

This project is deployed on Netlify with the following configuration:

### Netlify Configuration

To ensure successful builds:

1. Go to your Netlify site settings
2. Navigate to "Build & deploy" > "Environment variables"
3. Add a variable with key `NPM_FLAGS` and value `--legacy-peer-deps`
4. Save and trigger a new build

## Contributing

1. Create a feature branch from `main`
2. Make your changes
3. Test thoroughly
4. Submit a pull request with a clear description of your changes

## Maintenance Recommendations

- Run `npm run lint` regularly to catch potential issues
- Keep dependencies updated with `npm update`
- Review and remove unused code or assets periodically
- Test across multiple browsers and devices before deployment
