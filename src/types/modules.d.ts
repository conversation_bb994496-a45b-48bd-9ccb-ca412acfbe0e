// This file contains type declarations for modules that don't have their own type definitions



// This helps with dynamic imports
declare module '*.module.css' {
  const classes: { [key: string]: string };
  export default classes;
}

// Custom element declarations for JSX
declare namespace JSX {
  interface IntrinsicElements {
    'instagram-post': {
      'post-url'?: string;
      captions?: string;
      className?: string;
      [key: string]: string | number | boolean | undefined;
    };
  }
}
