import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AlertCircle, Wifi, RefreshCw, Home, AlertTriangle } from "lucide-react";

interface ErrorStateProps {
  type?: 'network' | 'not-found' | 'server' | 'generic';
  title?: string;
  message?: string;
  onRetry?: () => void;
  onGoHome?: () => void;
  className?: string;
  showActions?: boolean;
}

/**
 * Responsive error state component for different error scenarios
 * Adapts layout and content based on screen size and error type
 */
export const ErrorState = ({
  type = 'generic',
  title,
  message,
  onRetry,
  onGoHome,
  className,
  showActions = true
}: ErrorStateProps) => {
  const errorConfig = {
    network: {
      icon: Wifi,
      defaultTitle: 'Connection Problem',
      defaultMessage: 'Please check your internet connection and try again.',
      iconColor: 'text-orange-500',
      bgColor: 'bg-orange-100'
    },
    'not-found': {
      icon: AlertTriangle,
      defaultTitle: 'Page Not Found',
      defaultMessage: 'The page you\'re looking for doesn\'t exist or has been moved.',
      iconColor: 'text-yellow-500',
      bgColor: 'bg-yellow-100'
    },
    server: {
      icon: AlertCircle,
      defaultTitle: 'Server Error',
      defaultMessage: 'Something went wrong on our end. Please try again later.',
      iconColor: 'text-red-500',
      bgColor: 'bg-red-100'
    },
    generic: {
      icon: AlertCircle,
      defaultTitle: 'Something went wrong',
      defaultMessage: 'An unexpected error occurred. Please try again.',
      iconColor: 'text-red-500',
      bgColor: 'bg-red-100'
    }
  };

  const config = errorConfig[type];
  const Icon = config.icon;

  return (
    <div className={cn(
      "flex flex-col items-center justify-center text-center",
      "px-4 sm:px-6 lg:px-8 py-8 sm:py-12 lg:py-16",
      "min-h-[300px] sm:min-h-[400px]",
      className
    )}>
      {/* Error Icon - Responsive sizing */}
      <div className={cn(
        "rounded-full p-3 sm:p-4 mb-4 sm:mb-6",
        config.bgColor
      )}>
        <Icon className={cn(
          "h-8 w-8 sm:h-12 sm:w-12 lg:h-16 lg:w-16",
          config.iconColor
        )} />
      </div>

      {/* Error Content - Responsive typography */}
      <div className="max-w-md space-y-2 sm:space-y-3 mb-6 sm:mb-8">
        <h2 className="text-lg sm:text-xl lg:text-2xl font-bold text-elev-navy">
          {title || config.defaultTitle}
        </h2>
        <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
          {message || config.defaultMessage}
        </p>
      </div>

      {/* Action Buttons - Responsive layout */}
      {showActions && (
        <div className="space-y-3 sm:space-y-0 sm:space-x-3 sm:flex">
          {onRetry && (
            <Button 
              onClick={onRetry}
              className="w-full sm:w-auto bg-elev-navy hover:bg-elev-navy/90 text-white px-6 py-2.5"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          )}
          {onGoHome && (
            <Button 
              onClick={onGoHome}
              variant="outline"
              className="w-full sm:w-auto border-elev-navy text-elev-navy hover:bg-elev-navy hover:text-white px-6 py-2.5"
            >
              <Home className="h-4 w-4 mr-2" />
              Go Home
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

/**
 * Responsive network error component
 */
export const NetworkError = (props: Omit<ErrorStateProps, 'type'>) => (
  <ErrorState type="network" {...props} />
);

/**
 * Responsive 404 error component
 */
export const NotFoundError = (props: Omit<ErrorStateProps, 'type'>) => (
  <ErrorState type="not-found" {...props} />
);

/**
 * Responsive server error component
 */
export const ServerError = (props: Omit<ErrorStateProps, 'type'>) => (
  <ErrorState type="server" {...props} />
);