// Component standardization system for enforcing consistent component patterns and variants

import { 
  Analy<PERSON>, 
  <PERSON>lyzerResult, 
  FileInfo, 
  Issue, 
  IssueType, 
  Severity, 
  Category, 
  FileLocation, 
  ImpactAssessment 
} from '../types';
import { auditLogger } from '../core/logger';

interface ComponentStandard {
  name: string;
  requiredProps: string[];
  optionalProps: string[];
  variants: ComponentVariant[];
  stylingApproach: 'css-modules' | 'styled-components' | 'tailwind' | 'inline-styles' | 'css-in-js';
  stateHandling: 'hooks' | 'class-state' | 'props-only';
  interactionPatterns: InteractionPattern[];
}

interface ComponentVariant {
  name: string;
  props: Record<string, string[]>; // prop name -> allowed values
  defaultProps: Record<string, string>;
}

interface InteractionPattern {
  type: 'click' | 'hover' | 'focus' | 'keyboard' | 'touch';
  handler: string;
  accessibility: AccessibilityRequirement[];
}

interface AccessibilityRequirement {
  attribute: string;
  value: string;
  required: boolean;
}

interface ComponentImplementation {
  name: string;
  filePath: string;
  props: string[];
  variants: string[];
  stylingApproach: string;
  stateHandling: string;
  interactionPatterns: string[];
  accessibilityAttributes: Record<string, string>;
  location: FileLocation;
}

interface StandardizationRule {
  id: string;
  name: string;
  description: string;
  check: (implementation: ComponentImplementation, standard: ComponentStandard) => boolean;
  fix?: (implementation: ComponentImplementation, standard: ComponentStandard) => string;
  severity: Severity;
}

export class ComponentStandardizationSystem implements Analyzer {
  name = 'component-standardization-system';

  // Default component standards
  private componentStandards: Map<string, ComponentStandard> = new Map();
  
  // Standardization rules
  private standardizationRules: StandardizationRule[] = [
    {
      id: 'required-props',
      name: 'Required Props Validation',
      description: 'Ensures all required props are present in component implementations',
      check: (impl, standard) => {
        return standard.requiredProps.every(prop => impl.props.includes(prop));
      },
      severity: Severity.HIGH
    },
    {
      id: 'styling-consistency',
      name: 'Styling Approach Consistency',
      description: 'Ensures consistent styling approach across component variants',
      check: (impl, standard) => {
        return impl.stylingApproach === standard.stylingApproach;
      },
      severity: Severity.HIGH
    },
    {
      id: 'state-handling-consistency',
      name: 'State Handling Consistency',
      description: 'Ensures consistent state handling patterns',
      check: (impl, standard) => {
        return impl.stateHandling === standard.stateHandling;
      },
      severity: Severity.MEDIUM
    },
    {
      id: 'interaction-patterns',
      name: 'Interaction Pattern Validation',
      description: 'Validates that required interaction patterns are implemented',
      check: (impl, standard) => {
        const requiredPatterns = standard.interactionPatterns.map(p => p.type);
        return requiredPatterns.every(pattern => 
          impl.interactionPatterns.some(ip => ip.includes(pattern))
        );
      },
      severity: Severity.MEDIUM
    },
    {
      id: 'accessibility-compliance',
      name: 'Accessibility Compliance',
      description: 'Ensures required accessibility attributes are present',
      check: (impl, standard) => {
        const requiredA11yAttrs = standard.interactionPatterns
          .flatMap(p => p.accessibility)
          .filter(a => a.required);
        
        return requiredA11yAttrs.every(attr => 
          impl.accessibilityAttributes[attr.attribute] === attr.value
        );
      },
      severity: Severity.HIGH
    }
  ];

  constructor() {
    this.initializeDefaultStandards();
  }

  async analyze(files: FileInfo[]): Promise<AnalyzerResult> {
    const startTime = Date.now();
    auditLogger.info('Starting component standardization analysis', { fileCount: files.length });

    const issues: Issue[] = [];
    const componentImplementations = new Map<string, ComponentImplementation[]>();

    // Filter React component files
    const componentFiles = files.filter(file => 
      ['.tsx', '.jsx'].includes(file.extension) &&
      !file.path.includes('test') &&
      !file.path.includes('spec') &&
      !file.path.includes('stories')
    );

    // Extract component implementations
    for (const file of componentFiles) {
      try {
        const implementations = await this.extractComponentImplementations(file);
        implementations.forEach(impl => {
          if (!componentImplementations.has(impl.name)) {
            componentImplementations.set(impl.name, []);
          }
          componentImplementations.get(impl.name)!.push(impl);
        });
      } catch (error) {
        auditLogger.error('Failed to extract component implementations', error as Error, { 
          filePath: file.path 
        });
      }
    }

    // Validate implementations against standards
    componentImplementations.forEach((implementations, componentName) => {
      const standard = this.componentStandards.get(componentName);
      if (standard) {
        implementations.forEach(impl => {
          const validationIssues = this.validateImplementation(impl, standard);
          issues.push(...validationIssues);
        });
      } else {
        // Create issues for components without standards
        const standardizationIssues = this.createStandardizationSuggestions(implementations);
        issues.push(...standardizationIssues);
      }
    });

    const executionTime = Math.max(1, Date.now() - startTime);
    
    const result: AnalyzerResult = {
      analyzerName: this.name,
      issues,
      summary: {
        totalIssues: issues.length,
        severityBreakdown: this.calculateSeverityBreakdown(issues),
        categoryBreakdown: this.calculateCategoryBreakdown(issues)
      },
      executionTime
    };

    auditLogger.info('Component standardization analysis completed', { 
      issuesFound: issues.length,
      executionTime 
    });

    return result;
  }

  private initializeDefaultStandards(): void {
    // Button component standard
    this.componentStandards.set('Button', {
      name: 'Button',
      requiredProps: ['children'],
      optionalProps: ['variant', 'size', 'disabled', 'loading', 'onClick'],
      variants: [
        {
          name: 'primary',
          props: { variant: ['primary'], size: ['small', 'medium', 'large'] },
          defaultProps: { variant: 'primary', size: 'medium' }
        },
        {
          name: 'secondary',
          props: { variant: ['secondary'], size: ['small', 'medium', 'large'] },
          defaultProps: { variant: 'secondary', size: 'medium' }
        }
      ],
      stylingApproach: 'css-modules',
      stateHandling: 'props-only',
      interactionPatterns: [
        {
          type: 'click',
          handler: 'onClick',
          accessibility: [
            { attribute: 'role', value: 'button', required: true },
            { attribute: 'aria-disabled', value: 'true', required: false }
          ]
        },
        {
          type: 'keyboard',
          handler: 'onKeyDown',
          accessibility: [
            { attribute: 'tabIndex', value: '0', required: true }
          ]
        }
      ]
    });

    // Input component standard
    this.componentStandards.set('Input', {
      name: 'Input',
      requiredProps: ['value', 'onChange'],
      optionalProps: ['placeholder', 'disabled', 'error', 'label', 'type'],
      variants: [
        {
          name: 'text',
          props: { type: ['text', 'email', 'password', 'number'] },
          defaultProps: { type: 'text' }
        }
      ],
      stylingApproach: 'css-modules',
      stateHandling: 'props-only',
      interactionPatterns: [
        {
          type: 'focus',
          handler: 'onFocus',
          accessibility: [
            { attribute: 'aria-label', value: '', required: true },
            { attribute: 'aria-invalid', value: 'false', required: false }
          ]
        }
      ]
    });

    // Card component standard
    this.componentStandards.set('Card', {
      name: 'Card',
      requiredProps: ['children'],
      optionalProps: ['title', 'variant', 'elevation', 'padding'],
      variants: [
        {
          name: 'default',
          props: { elevation: ['none', 'low', 'medium', 'high'] },
          defaultProps: { elevation: 'low' }
        }
      ],
      stylingApproach: 'css-modules',
      stateHandling: 'props-only',
      interactionPatterns: []
    });
  }

  private async extractComponentImplementations(file: FileInfo): Promise<ComponentImplementation[]> {
    const implementations: ComponentImplementation[] = [];
    const lines = file.content.split('\n');

    // Find component definitions
    const componentPattern = /^(?:export\s+)?(?:const|function)\s+([A-Z][a-zA-Z0-9]*)\s*[=:]/;
    
    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex];
      const match = line.match(componentPattern);
      
      if (match) {
        const componentName = match[1];
        const componentCode = this.extractComponentCode(lines, lineIndex);
        
        const implementation: ComponentImplementation = {
          name: componentName,
          filePath: file.path,
          props: this.extractProps(componentCode),
          variants: this.extractVariants(componentCode),
          stylingApproach: this.determineStylingApproach(componentCode),
          stateHandling: this.determineStateHandling(componentCode),
          interactionPatterns: this.extractInteractionPatterns(componentCode),
          accessibilityAttributes: this.extractAccessibilityAttributes(componentCode),
          location: {
            filePath: file.path,
            lineNumber: lineIndex + 1,
            columnNumber: match.index || 0,
            context: line.trim()
          }
        };

        implementations.push(implementation);
      }
    }

    return implementations;
  }

  private extractComponentCode(lines: string[], startIndex: number): string {
    let braceCount = 0;
    let endIndex = startIndex;
    let foundOpenBrace = false;

    for (let i = startIndex; i < lines.length; i++) {
      const line = lines[i];
      
      for (const char of line) {
        if (char === '{') {
          braceCount++;
          foundOpenBrace = true;
        } else if (char === '}') {
          braceCount--;
          if (foundOpenBrace && braceCount === 0) {
            endIndex = i;
            break;
          }
        }
      }
      
      if (foundOpenBrace && braceCount === 0) {
        break;
      }
      
      if (i - startIndex > 100) {
        endIndex = i;
        break;
      }
    }

    return lines.slice(startIndex, endIndex + 1).join('\n');
  }

  private extractProps(componentCode: string): string[] {
    const props: string[] = [];
    
    // Extract props from function parameters
    const functionParamMatch = componentCode.match(/\(([^)]*)\)/);
    if (functionParamMatch) {
      const params = functionParamMatch[1];
      
      // Handle destructured props
      const destructuredMatch = params.match(/\{\s*([^}]+)\s*\}/);
      if (destructuredMatch) {
        const propsList = destructuredMatch[1]
          .split(',')
          .map(prop => prop.trim().split(':')[0].trim())
          .filter(prop => prop && !prop.includes('...'));
        props.push(...propsList);
      }
    }

    return [...new Set(props)];
  }

  private extractVariants(componentCode: string): string[] {
    const variants: string[] = [];
    
    // Look for variant prop usage
    const variantMatches = componentCode.match(/variant\s*===?\s*['"`]([^'"`]+)['"`]/g);
    if (variantMatches) {
      variantMatches.forEach(match => {
        const variantMatch = match.match(/['"`]([^'"`]+)['"`]/);
        if (variantMatch) {
          variants.push(variantMatch[1]);
        }
      });
    }

    return [...new Set(variants)];
  }

  private determineStylingApproach(componentCode: string): string {
    if (componentCode.includes('import styles from') && componentCode.includes('.module.css')) {
      return 'css-modules';
    }
    if (componentCode.includes('styled.') || componentCode.includes('styled(')) {
      return 'styled-components';
    }
    if (componentCode.includes('className=') && componentCode.match(/bg-|text-|p-|m-|flex|grid/)) {
      return 'tailwind';
    }
    if (componentCode.includes('style={{')) {
      return 'inline-styles';
    }
    if (componentCode.includes('css`') || componentCode.includes('makeStyles')) {
      return 'css-in-js';
    }
    
    return 'unknown';
  }

  private determineStateHandling(componentCode: string): string {
    if (componentCode.includes('useState') || componentCode.includes('useEffect')) {
      return 'hooks';
    }
    if (componentCode.includes('this.state') || componentCode.includes('this.setState')) {
      return 'class-state';
    }
    return 'props-only';
  }

  private extractInteractionPatterns(componentCode: string): string[] {
    const patterns: string[] = [];
    
    if (componentCode.includes('onClick')) patterns.push('click');
    if (componentCode.includes('onHover') || componentCode.includes('onMouseEnter')) patterns.push('hover');
    if (componentCode.includes('onFocus')) patterns.push('focus');
    if (componentCode.includes('onKeyDown') || componentCode.includes('onKeyPress')) patterns.push('keyboard');
    if (componentCode.includes('onTouchStart') || componentCode.includes('onTouchEnd')) patterns.push('touch');

    return patterns;
  }

  private extractAccessibilityAttributes(componentCode: string): Record<string, string> {
    const attributes: Record<string, string> = {};
    
    // Extract aria-* attributes
    const ariaMatches = componentCode.match(/aria-[\w-]+\s*=\s*['"`]([^'"`]+)['"`]/g);
    if (ariaMatches) {
      ariaMatches.forEach(match => {
        const attrMatch = match.match(/(aria-[\w-]+)\s*=\s*['"`]([^'"`]+)['"`]/);
        if (attrMatch) {
          attributes[attrMatch[1]] = attrMatch[2];
        }
      });
    }

    // Extract role attribute
    const roleMatch = componentCode.match(/role\s*=\s*['"`]([^'"`]+)['"`]/);
    if (roleMatch) {
      attributes['role'] = roleMatch[1];
    }

    // Extract tabIndex
    const tabIndexMatch = componentCode.match(/tabIndex\s*=\s*['"`]?([^'"`\s]+)['"`]?/);
    if (tabIndexMatch) {
      attributes['tabIndex'] = tabIndexMatch[1];
    }

    return attributes;
  }

  private validateImplementation(
    implementation: ComponentImplementation, 
    standard: ComponentStandard
  ): Issue[] {
    const issues: Issue[] = [];

    this.standardizationRules.forEach(rule => {
      if (!rule.check(implementation, standard)) {
        const issue: Issue = {
          id: `${rule.id}_${implementation.name}_${implementation.filePath}`,
          type: IssueType.COMPONENT_INCONSISTENCY,
          severity: rule.severity,
          category: Category.COMPONENT,
          description: `${implementation.name}: ${rule.name}`,
          location: implementation.location,
          suggestion: this.generateStandardizationSuggestion(rule, implementation, standard),
          autoFixable: !!rule.fix,
          impact: this.calculateStandardizationImpact(rule.severity)
        };
        issues.push(issue);
      }
    });

    return issues;
  }

  private createStandardizationSuggestions(implementations: ComponentImplementation[]): Issue[] {
    const issues: Issue[] = [];
    
    if (implementations.length === 0) return issues;

    const componentName = implementations[0].name;
    const firstImpl = implementations[0];

    // Suggest creating a standard for this component
    const issue: Issue = {
      id: `missing_standard_${componentName}`,
      type: IssueType.COMPONENT_INCONSISTENCY,
      severity: Severity.MEDIUM,
      category: Category.COMPONENT,
      description: `Component '${componentName}' lacks standardization guidelines`,
      location: firstImpl.location,
      suggestion: `Create standardization guidelines for '${componentName}' component including required props, styling approach, and interaction patterns`,
      autoFixable: false,
      impact: {
        userExperience: 4,
        maintenanceEffort: 7,
        implementationComplexity: 5
      }
    };

    issues.push(issue);

    return issues;
  }

  private generateStandardizationSuggestion(
    rule: StandardizationRule, 
    implementation: ComponentImplementation, 
    standard: ComponentStandard
  ): string {
    switch (rule.id) {
      case 'required-props':
        const missingProps = standard.requiredProps.filter(prop => !implementation.props.includes(prop));
        return `Add required props: ${missingProps.join(', ')}`;
      
      case 'styling-consistency':
        return `Change styling approach from '${implementation.stylingApproach}' to '${standard.stylingApproach}'`;
      
      case 'state-handling-consistency':
        return `Change state handling from '${implementation.stateHandling}' to '${standard.stateHandling}'`;
      
      case 'interaction-patterns':
        const missingPatterns = standard.interactionPatterns
          .filter(p => !implementation.interactionPatterns.includes(p.type))
          .map(p => p.type);
        return `Add missing interaction patterns: ${missingPatterns.join(', ')}`;
      
      case 'accessibility-compliance':
        const requiredAttrs = standard.interactionPatterns
          .flatMap(p => p.accessibility)
          .filter(a => a.required && !implementation.accessibilityAttributes[a.attribute]);
        return `Add required accessibility attributes: ${requiredAttrs.map(a => a.attribute).join(', ')}`;
      
      default:
        return `Follow ${standard.name} component standards`;
    }
  }

  private calculateStandardizationImpact(severity: Severity): ImpactAssessment {
    const impactMap = {
      [Severity.LOW]: { userExperience: 2, maintenanceEffort: 3, implementationComplexity: 2 },
      [Severity.MEDIUM]: { userExperience: 4, maintenanceEffort: 6, implementationComplexity: 4 },
      [Severity.HIGH]: { userExperience: 7, maintenanceEffort: 8, implementationComplexity: 6 },
      [Severity.CRITICAL]: { userExperience: 9, maintenanceEffort: 10, implementationComplexity: 8 }
    };

    return impactMap[severity];
  }

  getSeverity(issue: Omit<Issue, 'severity'>): Severity {
    if (issue.description.includes('Required Props Validation') || 
        issue.description.includes('Accessibility Compliance')) {
      return Severity.HIGH;
    }
    if (issue.description.includes('Styling Approach Consistency') || 
        issue.description.includes('Interaction Pattern Validation') ||
        issue.description.includes('lacks standardization')) {
      return Severity.MEDIUM;
    }
    
    return Severity.LOW;
  }

  private calculateSeverityBreakdown(issues: Issue[]): Record<Severity, number> {
    const breakdown: Record<Severity, number> = {
      [Severity.LOW]: 0,
      [Severity.MEDIUM]: 0,
      [Severity.HIGH]: 0,
      [Severity.CRITICAL]: 0
    };

    issues.forEach(issue => {
      breakdown[issue.severity]++;
    });

    return breakdown;
  }

  private calculateCategoryBreakdown(issues: Issue[]): Record<Category, number> {
    const breakdown: Record<Category, number> = {
      [Category.VISUAL]: 0,
      [Category.CONTENT]: 0,
      [Category.COMPONENT]: 0,
      [Category.ACCESSIBILITY]: 0
    };

    issues.forEach(issue => {
      breakdown[issue.category]++;
    });

    return breakdown;
  }

  // Public methods for external use
  public addComponentStandard(standard: ComponentStandard): void {
    this.componentStandards.set(standard.name, standard);
  }

  public getComponentStandard(componentName: string): ComponentStandard | undefined {
    return this.componentStandards.get(componentName);
  }

  public addStandardizationRule(rule: StandardizationRule): void {
    this.standardizationRules.push(rule);
  }

  public generateStandardFromImplementations(
    componentName: string, 
    implementations: ComponentImplementation[]
  ): ComponentStandard {
    // Analyze implementations to suggest a standard
    const allProps = [...new Set(implementations.flatMap(impl => impl.props))];
    const commonProps = allProps.filter(prop => 
      implementations.every(impl => impl.props.includes(prop))
    );
    const optionalProps = allProps.filter(prop => !commonProps.includes(prop));

    const mostCommonStyling = this.getMostCommon(implementations.map(impl => impl.stylingApproach));
    const mostCommonStateHandling = this.getMostCommon(implementations.map(impl => impl.stateHandling));

    return {
      name: componentName,
      requiredProps: commonProps,
      optionalProps,
      variants: [],
      stylingApproach: mostCommonStyling as ComponentStandard['stylingApproach'],
      stateHandling: mostCommonStateHandling as ComponentStandard['stateHandling'],
      interactionPatterns: []
    };
  }

  private getMostCommon<T>(items: T[]): T {
    const frequency = new Map<T, number>();
    items.forEach(item => {
      frequency.set(item, (frequency.get(item) || 0) + 1);
    });

    let mostCommon = items[0];
    let maxCount = 0;
    frequency.forEach((count, item) => {
      if (count > maxCount) {
        maxCount = count;
        mostCommon = item;
      }
    });

    return mostCommon;
  }
}