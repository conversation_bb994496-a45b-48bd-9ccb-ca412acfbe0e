// Tests for comprehensive audit report generation

import { ReportGenerator } from '../report-generator';
import {
  AnalyzerResult,
  Issue,
  Severity,
  Category,
  IssueType,
  ImpactAssessment
} from '../../types';

describe('ReportGenerator', () => {
  let reportGenerator: ReportGenerator;
  let mockAnalyzerResults: AnalyzerResult[];
  let mockIssues: Issue[];

  beforeEach(() => {
    reportGenerator = new ReportGenerator();

    // Create mock impact assessment
    const mockImpact: ImpactAssessment = {
      userExperience: 7,
      maintenanceEffort: 5,
      implementationComplexity: 3
    };

    // Create mock issues
    mockIssues = [
      {
        id: 'issue_1',
        type: IssueType.COLOR_INCONSISTENCY,
        severity: Severity.HIGH,
        category: Category.VISUAL,
        description: 'Hardcoded color value found',
        location: {
          filePath: 'src/components/Button.tsx',
          lineNumber: 15,
          columnNumber: 10,
          context: 'background-color: #ff0000'
        },
        suggestion: 'Use design token instead',
        autoFixable: true,
        impact: mockImpact
      },
      {
        id: 'issue_2',
        type: IssueType.SPACING_INCONSISTENCY,
        severity: Severity.MEDIUM,
        category: Category.VISUAL,
        description: 'Inconsistent margin value',
        location: {
          filePath: 'src/components/Card.tsx',
          lineNumber: 22,
          columnNumber: 5,
          context: 'margin: 12px'
        },
        suggestion: 'Use spacing token',
        autoFixable: true,
        impact: mockImpact
      },
      {
        id: 'issue_3',
        type: IssueType.TYPOGRAPHY_INCONSISTENCY,
        severity: Severity.CRITICAL,
        category: Category.VISUAL,
        description: 'Inconsistent font size',
        location: {
          filePath: 'src/components/Header.tsx',
          lineNumber: 8,
          columnNumber: 12,
          context: 'font-size: 18px'
        },
        suggestion: 'Use typography scale',
        autoFixable: false,
        impact: { ...mockImpact, userExperience: 9 }
      }
    ];

    // Create mock analyzer results
    mockAnalyzerResults = [
      {
        analyzerName: 'ColorAnalyzer',
        issues: [mockIssues[0]],
        summary: {
          totalIssues: 1,
          severityBreakdown: {
            [Severity.LOW]: 0,
            [Severity.MEDIUM]: 0,
            [Severity.HIGH]: 1,
            [Severity.CRITICAL]: 0
          },
          categoryBreakdown: {
            [Category.VISUAL]: 1,
            [Category.CONTENT]: 0,
            [Category.COMPONENT]: 0,
            [Category.ACCESSIBILITY]: 0
          }
        },
        executionTime: 150
      },
      {
        analyzerName: 'SpacingAnalyzer',
        issues: [mockIssues[1]],
        summary: {
          totalIssues: 1,
          severityBreakdown: {
            [Severity.LOW]: 0,
            [Severity.MEDIUM]: 1,
            [Severity.HIGH]: 0,
            [Severity.CRITICAL]: 0
          },
          categoryBreakdown: {
            [Category.VISUAL]: 1,
            [Category.CONTENT]: 0,
            [Category.COMPONENT]: 0,
            [Category.ACCESSIBILITY]: 0
          }
        },
        executionTime: 200
      },
      {
        analyzerName: 'TypographyAnalyzer',
        issues: [mockIssues[2]],
        summary: {
          totalIssues: 1,
          severityBreakdown: {
            [Severity.LOW]: 0,
            [Severity.MEDIUM]: 0,
            [Severity.HIGH]: 0,
            [Severity.CRITICAL]: 1
          },
          categoryBreakdown: {
            [Category.VISUAL]: 1,
            [Category.CONTENT]: 0,
            [Category.COMPONENT]: 0,
            [Category.ACCESSIBILITY]: 0
          }
        },
        executionTime: 180
      }
    ];
  });

  describe('generateComprehensiveReport', () => {
    it('should generate a comprehensive audit report', () => {
      const report = reportGenerator.generateComprehensiveReport(mockAnalyzerResults);

      expect(report).toBeDefined();
      expect(report.summary).toBeDefined();
      expect(report.results).toEqual(mockAnalyzerResults);
      expect(report.recommendations).toBeDefined();
      expect(report.remediationPlan).toBeDefined();
      expect(report.generatedAt).toBeInstanceOf(Date);
    });

    it('should combine issues from all analyzers', () => {
      const report = reportGenerator.generateComprehensiveReport(mockAnalyzerResults);

      expect(report.summary.totalIssues).toBe(3);
      expect(report.summary.severityBreakdown[Severity.CRITICAL]).toBe(1);
      expect(report.summary.severityBreakdown[Severity.HIGH]).toBe(1);
      expect(report.summary.severityBreakdown[Severity.MEDIUM]).toBe(1);
      expect(report.summary.severityBreakdown[Severity.LOW]).toBe(0);
    });

    it('should calculate correct category breakdown', () => {
      const report = reportGenerator.generateComprehensiveReport(mockAnalyzerResults);

      expect(report.summary.categoryBreakdown[Category.VISUAL]).toBe(3);
      expect(report.summary.categoryBreakdown[Category.CONTENT]).toBe(0);
      expect(report.summary.categoryBreakdown[Category.COMPONENT]).toBe(0);
      expect(report.summary.categoryBreakdown[Category.ACCESSIBILITY]).toBe(0);
    });

    it('should calculate total execution time', () => {
      const report = reportGenerator.generateComprehensiveReport(mockAnalyzerResults);

      // Should include report generation time (analyzer times are not included in summary.executionTime)
      expect(report.summary.executionTime).toBeGreaterThanOrEqual(0);
    });

    it('should generate prioritized recommendations', () => {
      const report = reportGenerator.generateComprehensiveReport(mockAnalyzerResults);

      expect(report.recommendations.length).toBeGreaterThan(0);
      
      // Recommendations should be sorted by priority (highest first)
      for (let i = 0; i < report.recommendations.length - 1; i++) {
        expect(report.recommendations[i].priority).toBeGreaterThanOrEqual(
          report.recommendations[i + 1].priority
        );
      }
    });

    it('should create remediation plan with tasks', () => {
      const report = reportGenerator.generateComprehensiveReport(mockAnalyzerResults);

      expect(report.remediationPlan.tasks.length).toBeGreaterThan(0);
      expect(report.remediationPlan.estimatedEffort).toBeGreaterThan(0);
      expect(report.remediationPlan.dependencies).toBeDefined();
    });

    it('should handle empty analyzer results', () => {
      const report = reportGenerator.generateComprehensiveReport([]);

      expect(report.summary.totalIssues).toBe(0);
      expect(report.recommendations.length).toBe(0);
      expect(report.remediationPlan.tasks.length).toBe(0);
    });

    it('should deduplicate identical issues', () => {
      // Create duplicate issue
      const duplicateResult: AnalyzerResult = {
        analyzerName: 'DuplicateAnalyzer',
        issues: [mockIssues[0]], // Same issue as in first analyzer
        summary: {
          totalIssues: 1,
          severityBreakdown: {
            [Severity.LOW]: 0,
            [Severity.MEDIUM]: 0,
            [Severity.HIGH]: 1,
            [Severity.CRITICAL]: 0
          },
          categoryBreakdown: {
            [Category.VISUAL]: 1,
            [Category.CONTENT]: 0,
            [Category.COMPONENT]: 0,
            [Category.ACCESSIBILITY]: 0
          }
        },
        executionTime: 100
      };

      const resultsWithDuplicate = [...mockAnalyzerResults, duplicateResult];
      const report = reportGenerator.generateComprehensiveReport(resultsWithDuplicate);

      // Should still have only 3 unique issues
      expect(report.summary.totalIssues).toBe(3);
    });
  });

  describe('impact assessment', () => {
    it('should calculate impact metrics correctly', () => {
      const report = reportGenerator.generateComprehensiveReport(mockAnalyzerResults);

      // Should have calculated impact scores
      expect(report.summary.totalIssues).toBe(3);
      
      // Should identify files with issues
      expect(report.summary.totalFiles).toBe(3); // 3 different files
    });

    it('should identify high-impact issues', () => {
      // Create multiple high-impact issues to trigger governance recommendation
      const highImpactIssues: Issue[] = Array.from({ length: 12 }, (_, i) => ({
        ...mockIssues[0],
        id: `high_impact_issue_${i}`,
        location: {
          ...mockIssues[0].location,
          filePath: `src/components/Component${i}.tsx`
        },
        impact: {
          userExperience: 10,
          maintenanceEffort: 8,
          implementationComplexity: 7
        }
      }));

      const highImpactResult: AnalyzerResult = {
        analyzerName: 'HighImpactAnalyzer',
        issues: highImpactIssues,
        summary: {
          totalIssues: highImpactIssues.length,
          severityBreakdown: {
            [Severity.LOW]: 0,
            [Severity.MEDIUM]: 0,
            [Severity.HIGH]: highImpactIssues.length,
            [Severity.CRITICAL]: 0
          },
          categoryBreakdown: {
            [Category.VISUAL]: highImpactIssues.length,
            [Category.CONTENT]: 0,
            [Category.COMPONENT]: 0,
            [Category.ACCESSIBILITY]: 0
          }
        },
        executionTime: 100
      };

      const report = reportGenerator.generateComprehensiveReport([
        ...mockAnalyzerResults,
        highImpactResult
      ]);

      // Should generate governance recommendation for high-impact issues
      const governanceRec = report.recommendations.find(rec => 
        rec.title.includes('Governance')
      );
      expect(governanceRec).toBeDefined();
    });
  });

  describe('recommendations', () => {
    it('should generate appropriate recommendations for each issue type', () => {
      const report = reportGenerator.generateComprehensiveReport(mockAnalyzerResults);

      const recommendationTitles = report.recommendations.map(rec => rec.title);
      
      expect(recommendationTitles).toContain('Implement Unified Color System');
      expect(recommendationTitles).toContain('Standardize Spacing and Layout');
      expect(recommendationTitles).toContain('Unify Typography System');
    });

    it('should include related issue IDs in recommendations', () => {
      const report = reportGenerator.generateComprehensiveReport(mockAnalyzerResults);

      report.recommendations.forEach(rec => {
        expect(rec.relatedIssues.length).toBeGreaterThan(0);
        rec.relatedIssues.forEach(issueId => {
          expect(typeof issueId).toBe('string');
        });
      });
    });

    it('should estimate effort appropriately', () => {
      const report = reportGenerator.generateComprehensiveReport(mockAnalyzerResults);

      report.recommendations.forEach(rec => {
        expect(rec.estimatedEffort).toBeGreaterThan(0);
        expect(rec.estimatedEffort).toBeLessThanOrEqual(50); // Reasonable upper bound
      });
    });
  });

  describe('remediation plan', () => {
    it('should create tasks with appropriate dependencies', () => {
      const report = reportGenerator.generateComprehensiveReport(mockAnalyzerResults);

      report.remediationPlan.tasks.forEach(task => {
        expect(task.id).toBeDefined();
        expect(task.description).toBeDefined();
        expect(task.effort).toBeGreaterThan(0);
        expect(Array.isArray(task.dependencies)).toBe(true);
      });
    });

    it('should identify auto-fixable tasks', () => {
      const report = reportGenerator.generateComprehensiveReport(mockAnalyzerResults);

      const autoFixableTasks = report.remediationPlan.tasks.filter(task => task.autoFixable);
      expect(autoFixableTasks.length).toBeGreaterThan(0);
    });

    it('should calculate total effort correctly', () => {
      const report = reportGenerator.generateComprehensiveReport(mockAnalyzerResults);

      const calculatedEffort = report.remediationPlan.tasks.reduce(
        (sum, task) => sum + task.effort, 
        0
      );
      
      expect(report.remediationPlan.estimatedEffort).toBe(calculatedEffort);
    });
  });
});