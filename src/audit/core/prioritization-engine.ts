// Advanced prioritization and planning system for design system remediation

import {
  Issue,
  Severity,
  Category,
  IssueType,
  Recommendation,
  RemediationTask,
  RemediationPlan,
  ImpactAssessment
} from '../types';
import { auditLogger } from './logger';

export interface PriorityScore {
  overall: number;
  userImpact: number;
  businessValue: number;
  technicalComplexity: number;
  implementationEffort: number;
  riskLevel: number;
}

export interface TaskDependency {
  taskId: string;
  dependsOn: string[];
  dependencyType: 'blocking' | 'preferred' | 'optional';
  reason: string;
}

export interface EffortEstimate {
  hours: number;
  complexity: 'low' | 'medium' | 'high' | 'very_high';
  confidence: number; // 0-1 scale
  factors: string[];
}

export interface PrioritizedTask extends RemediationTask {
  priorityScore: PriorityScore;
  effortEstimate: EffortEstimate;
  dependencies: TaskDependency[];
  suggestedOrder: number;
  phase: 'foundation' | 'implementation' | 'refinement' | 'maintenance';
}

export interface PrioritizationMetrics {
  totalIssues: number;
  criticalIssues: number;
  autoFixableIssues: number;
  estimatedTotalEffort: number;
  recommendedPhases: number;
  quickWins: PrioritizedTask[];
  highImpactTasks: PrioritizedTask[];
}

export class PrioritizationEngine {
  private readonly SEVERITY_WEIGHTS = {
    [Severity.CRITICAL]: 10,
    [Severity.HIGH]: 7,
    [Severity.MEDIUM]: 4,
    [Severity.LOW]: 2
  };

  private readonly CATEGORY_WEIGHTS = {
    [Category.VISUAL]: 8,
    [Category.COMPONENT]: 9,
    [Category.CONTENT]: 5,
    [Category.ACCESSIBILITY]: 10
  };

  private readonly TYPE_COMPLEXITY = {
    [IssueType.COLOR_INCONSISTENCY]: 3,
    [IssueType.SPACING_INCONSISTENCY]: 4,
    [IssueType.TYPOGRAPHY_INCONSISTENCY]: 5,
    [IssueType.COMPONENT_INCONSISTENCY]: 8,
    [IssueType.CONTENT_INCONSISTENCY]: 3
  };

  /**
   * Create comprehensive prioritization and planning system
   */
  createPrioritizedPlan(
    issues: Issue[],
    recommendations: Recommendation[]
  ): { plan: RemediationPlan; metrics: PrioritizationMetrics } {
    auditLogger.info('Creating prioritized remediation plan', {
      issueCount: issues.length,
      recommendationCount: recommendations.length
    });

    try {
      // Step 1: Analyze and score all issues
      const scoredIssues = this.scoreIssues(issues);
      
      // Step 2: Create prioritized tasks from recommendations
      const prioritizedTasks = this.createPrioritizedTasks(recommendations, scoredIssues);
      
      // Step 3: Analyze dependencies and create dependency graph
      const tasksWithDependencies = this.analyzeDependencies(prioritizedTasks);
      
      // Step 4: Calculate effort estimates
      const tasksWithEffort = this.calculateEffortEstimates(tasksWithDependencies, scoredIssues);
      
      // Step 5: Determine optimal execution order
      const orderedTasks = this.determineExecutionOrder(tasksWithEffort);
      
      // Step 6: Organize into phases
      const phasedTasks = this.organizeIntoPhases(orderedTasks);
      
      // Step 7: Generate metrics and insights
      const metrics = this.generatePrioritizationMetrics(phasedTasks, issues);
      
      // Step 8: Create final remediation plan
      const plan = this.createRemediationPlan(phasedTasks, metrics);

      auditLogger.info('Prioritized remediation plan created', {
        taskCount: phasedTasks.length,
        totalEffort: metrics.estimatedTotalEffort,
        phases: metrics.recommendedPhases
      });

      return { plan, metrics };

    } catch (error) {
      auditLogger.error('Failed to create prioritized plan', error as Error);
      throw error;
    }
  }

  /**
   * Score issues based on multiple factors
   */
  private scoreIssues(issues: Issue[]): Array<Issue & { priorityScore: PriorityScore }> {
    return issues.map(issue => {
      const priorityScore = this.calculatePriorityScore(issue);
      return { ...issue, priorityScore };
    });
  }

  /**
   * Calculate comprehensive priority score for an issue
   */
  private calculatePriorityScore(issue: Issue): PriorityScore {
    // User impact score (0-10)
    const userImpact = this.calculateUserImpactScore(issue);
    
    // Business value score (0-10)
    const businessValue = this.calculateBusinessValueScore(issue);
    
    // Technical complexity score (0-10, lower is better)
    const technicalComplexity = this.calculateTechnicalComplexityScore(issue);
    
    // Implementation effort score (0-10, lower is better)
    const implementationEffort = this.calculateImplementationEffortScore(issue);
    
    // Risk level score (0-10)
    const riskLevel = this.calculateRiskLevelScore(issue);
    
    // Overall score calculation (weighted average)
    const overall = (
      userImpact * 0.25 +
      businessValue * 0.20 +
      (10 - technicalComplexity) * 0.20 +
      (10 - implementationEffort) * 0.25 +
      riskLevel * 0.10
    );

    return {
      overall,
      userImpact,
      businessValue,
      technicalComplexity,
      implementationEffort,
      riskLevel
    };
  }

  private calculateUserImpactScore(issue: Issue): number {
    const severityScore = this.SEVERITY_WEIGHTS[issue.severity];
    const categoryScore = this.CATEGORY_WEIGHTS[issue.category];
    const impactScore = issue.impact.userExperience;
    
    return Math.min(10, (severityScore + categoryScore + impactScore) / 3);
  }

  private calculateBusinessValueScore(issue: Issue): number {
    // Higher severity and user-facing issues have higher business value
    const severityMultiplier = this.SEVERITY_WEIGHTS[issue.severity] / 10;
    const categoryMultiplier = issue.category === Category.VISUAL ? 1.2 : 1.0;
    const baseScore = issue.impact.userExperience * severityMultiplier * categoryMultiplier;
    
    return Math.min(10, baseScore);
  }

  private calculateTechnicalComplexityScore(issue: Issue): number {
    const typeComplexity = this.TYPE_COMPLEXITY[issue.type];
    const implementationComplexity = issue.impact.implementationComplexity;
    
    return Math.min(10, (typeComplexity + implementationComplexity) / 2);
  }

  private calculateImplementationEffortScore(issue: Issue): number {
    const maintenanceEffort = issue.impact.maintenanceEffort;
    const implementationComplexity = issue.impact.implementationComplexity;
    const autoFixBonus = issue.autoFixable ? -2 : 0;
    
    return Math.max(1, Math.min(10, (maintenanceEffort + implementationComplexity) / 2 + autoFixBonus));
  }

  private calculateRiskLevelScore(issue: Issue): number {
    // Higher severity issues pose higher risk if not addressed
    const severityRisk = this.SEVERITY_WEIGHTS[issue.severity];
    const categoryRisk = issue.category === Category.ACCESSIBILITY ? 2 : 0;
    
    return Math.min(10, severityRisk + categoryRisk);
  }

  /**
   * Create prioritized tasks from recommendations
   */
  private createPrioritizedTasks(
    recommendations: Recommendation[],
    scoredIssues: Array<Issue & { priorityScore: PriorityScore }>
  ): PrioritizedTask[] {
    return recommendations.map((rec, index) => {
      // Find related issues for this recommendation
      const relatedIssues = scoredIssues.filter(issue => 
        rec.relatedIssues.includes(issue.id)
      );

      // Calculate aggregate priority score
      const avgPriorityScore = this.calculateAggregateScore(relatedIssues);

      return {
        id: `task_${index + 1}`,
        description: rec.title,
        type: this.getTaskTypeFromTitle(rec.title),
        effort: rec.estimatedEffort,
        dependencies: [], // Will be populated in next step
        autoFixable: relatedIssues.some(issue => issue.autoFixable),
        priorityScore: avgPriorityScore,
        effortEstimate: {
          hours: rec.estimatedEffort * 8, // Convert days to hours
          complexity: this.determineComplexity(rec.estimatedEffort),
          confidence: this.calculateConfidence(relatedIssues),
          factors: this.identifyEffortFactors(relatedIssues)
        },
        suggestedOrder: 0, // Will be set during ordering
        phase: 'implementation' // Will be refined during phase organization
      };
    });
  }

  private calculateAggregateScore(issues: Array<Issue & { priorityScore: PriorityScore }>): PriorityScore {
    if (issues.length === 0) {
      return {
        overall: 5,
        userImpact: 5,
        businessValue: 5,
        technicalComplexity: 5,
        implementationEffort: 5,
        riskLevel: 5
      };
    }

    const sum = issues.reduce((acc, issue) => ({
      overall: acc.overall + issue.priorityScore.overall,
      userImpact: acc.userImpact + issue.priorityScore.userImpact,
      businessValue: acc.businessValue + issue.priorityScore.businessValue,
      technicalComplexity: acc.technicalComplexity + issue.priorityScore.technicalComplexity,
      implementationEffort: acc.implementationEffort + issue.priorityScore.implementationEffort,
      riskLevel: acc.riskLevel + issue.priorityScore.riskLevel
    }), {
      overall: 0,
      userImpact: 0,
      businessValue: 0,
      technicalComplexity: 0,
      implementationEffort: 0,
      riskLevel: 0
    });

    const count = issues.length;
    return {
      overall: sum.overall / count,
      userImpact: sum.userImpact / count,
      businessValue: sum.businessValue / count,
      technicalComplexity: sum.technicalComplexity / count,
      implementationEffort: sum.implementationEffort / count,
      riskLevel: sum.riskLevel / count
    };
  }

  /**
   * Analyze task dependencies and create dependency graph
   */
  private analyzeDependencies(tasks: PrioritizedTask[]): PrioritizedTask[] {
    const dependencyMap = this.createDependencyMap();
    
    return tasks.map(task => {
      const dependencies = this.identifyTaskDependencies(task, tasks, dependencyMap);
      return { ...task, dependencies };
    });
  }

  private createDependencyMap(): Record<string, Array<{ dependency: string; type: TaskDependency['dependencyType']; reason: string }>> {
    return {
      'color_standardization': [
        { dependency: 'design_tokens_setup', type: 'blocking', reason: 'Requires design token system' }
      ],
      'spacing_standardization': [
        { dependency: 'design_tokens_setup', type: 'blocking', reason: 'Requires spacing token system' }
      ],
      'typography_standardization': [
        { dependency: 'design_tokens_setup', type: 'blocking', reason: 'Requires typography token system' }
      ],
      'component_standardization': [
        { dependency: 'design_tokens_setup', type: 'blocking', reason: 'Components need design tokens' },
        { dependency: 'color_standardization', type: 'preferred', reason: 'Benefits from color consistency' },
        { dependency: 'spacing_standardization', type: 'preferred', reason: 'Benefits from spacing consistency' }
      ],
      'content_standardization': [
        { dependency: 'style_guide_creation', type: 'blocking', reason: 'Requires content style guide' }
      ]
    };
  }

  private identifyTaskDependencies(
    task: PrioritizedTask,
    allTasks: PrioritizedTask[],
    dependencyMap: Record<string, Array<{ dependency: string; type: TaskDependency['dependencyType']; reason: string }>>
  ): TaskDependency[] {
    const taskType = this.getTaskTypeFromTitle(task.description);
    const dependencies = dependencyMap[taskType] || [];
    
    return dependencies.map(dep => ({
      taskId: task.id,
      dependsOn: this.findTasksByType(allTasks, dep.dependency),
      dependencyType: dep.type,
      reason: dep.reason
    }));
  }

  private findTasksByType(tasks: PrioritizedTask[], type: string): string[] {
    return tasks
      .filter(task => this.getTaskTypeFromTitle(task.description).includes(type.replace('_setup', '').replace('_creation', '')))
      .map(task => task.id);
  }

  /**
   * Calculate detailed effort estimates
   */
  private calculateEffortEstimates(
    tasks: PrioritizedTask[],
    scoredIssues: Array<Issue & { priorityScore: PriorityScore }>
  ): PrioritizedTask[] {
    return tasks.map(task => {
      const relatedIssues = scoredIssues.filter(issue => 
        task.description.toLowerCase().includes(issue.type.replace('_inconsistency', '').replace('_', ' '))
      );

      const effortEstimate = this.calculateDetailedEffort(task, relatedIssues);
      
      return { ...task, effortEstimate };
    });
  }

  private calculateDetailedEffort(
    task: PrioritizedTask,
    relatedIssues: Array<Issue & { priorityScore: PriorityScore }>
  ): EffortEstimate {
    const baseHours = task.effort * 8; // Convert days to hours
    const complexityMultiplier = this.getComplexityMultiplier(task.priorityScore.technicalComplexity);
    const issueCountMultiplier = Math.max(1, Math.log10(relatedIssues.length + 1));
    
    const adjustedHours = Math.round(baseHours * complexityMultiplier * issueCountMultiplier);
    
    return {
      hours: adjustedHours,
      complexity: this.determineComplexity(adjustedHours / 8),
      confidence: this.calculateConfidence(relatedIssues),
      factors: this.identifyEffortFactors(relatedIssues)
    };
  }

  private getComplexityMultiplier(technicalComplexity: number): number {
    if (technicalComplexity <= 3) return 0.8;
    if (technicalComplexity <= 6) return 1.0;
    if (technicalComplexity <= 8) return 1.3;
    return 1.6;
  }

  private determineComplexity(effortDays: number): EffortEstimate['complexity'] {
    if (effortDays <= 2) return 'low';
    if (effortDays <= 5) return 'medium';
    if (effortDays <= 10) return 'high';
    return 'very_high';
  }

  private calculateConfidence(issues: Array<Issue & { priorityScore: PriorityScore }>): number {
    if (issues.length === 0) return 0.5;
    
    const autoFixableRatio = issues.filter(i => i.autoFixable).length / issues.length;
    const avgComplexity = issues.reduce((sum, i) => sum + i.priorityScore.technicalComplexity, 0) / issues.length;
    
    // Higher confidence for more auto-fixable issues and lower complexity
    return Math.min(1, 0.3 + (autoFixableRatio * 0.4) + ((10 - avgComplexity) / 10 * 0.3));
  }

  private identifyEffortFactors(issues: Array<Issue & { priorityScore: PriorityScore }>): string[] {
    const factors: string[] = [];
    
    if (issues.length > 20) factors.push('High issue volume');
    if (issues.some(i => i.priorityScore.technicalComplexity > 7)) factors.push('Complex technical requirements');
    if (issues.filter(i => i.autoFixable).length / issues.length > 0.7) factors.push('Many auto-fixable issues');
    if (issues.some(i => i.severity === Severity.CRITICAL)) factors.push('Critical issues present');
    
    return factors;
  }

  /**
   * Determine optimal execution order using topological sorting
   */
  private determineExecutionOrder(tasks: PrioritizedTask[]): PrioritizedTask[] {
    // Create adjacency list for dependency graph
    const graph = new Map<string, string[]>();
    const inDegree = new Map<string, number>();
    
    // Initialize graph
    tasks.forEach(task => {
      graph.set(task.id, []);
      inDegree.set(task.id, 0);
    });
    
    // Build dependency graph
    tasks.forEach(task => {
      task.dependencies.forEach(dep => {
        dep.dependsOn.forEach(depId => {
          if (graph.has(depId)) {
            graph.get(depId)!.push(task.id);
            inDegree.set(task.id, (inDegree.get(task.id) || 0) + 1);
          }
        });
      });
    });
    
    // Topological sort with priority consideration
    const result: PrioritizedTask[] = [];
    const queue: PrioritizedTask[] = [];
    
    // Start with tasks that have no dependencies
    tasks.forEach(task => {
      if (inDegree.get(task.id) === 0) {
        queue.push(task);
      }
    });
    
    // Sort queue by priority score
    queue.sort((a, b) => b.priorityScore.overall - a.priorityScore.overall);
    
    let order = 1;
    while (queue.length > 0) {
      const current = queue.shift()!;
      current.suggestedOrder = order++;
      result.push(current);
      
      // Update dependencies
      graph.get(current.id)!.forEach(dependentId => {
        inDegree.set(dependentId, inDegree.get(dependentId)! - 1);
        
        if (inDegree.get(dependentId) === 0) {
          const dependentTask = tasks.find(t => t.id === dependentId)!;
          queue.push(dependentTask);
          queue.sort((a, b) => b.priorityScore.overall - a.priorityScore.overall);
        }
      });
    }
    
    return result;
  }

  /**
   * Organize tasks into logical phases
   */
  private organizeIntoPhases(tasks: PrioritizedTask[]): PrioritizedTask[] {
    return tasks.map(task => {
      const phase = this.determineTaskPhase(task);
      return { ...task, phase };
    });
  }

  private determineTaskPhase(task: PrioritizedTask): PrioritizedTask['phase'] {
    const taskType = this.getTaskTypeFromTitle(task.description);
    
    // Foundation phase: Design tokens, basic systems
    if (taskType.includes('token') || taskType.includes('foundation')) {
      return 'foundation';
    }
    
    // Implementation phase: Core standardization work
    if (taskType.includes('standardization') || taskType.includes('implementation')) {
      return 'implementation';
    }
    
    // Refinement phase: Advanced features, optimization
    if (taskType.includes('optimization') || taskType.includes('advanced')) {
      return 'refinement';
    }
    
    // Maintenance phase: Monitoring, governance
    if (taskType.includes('monitoring') || taskType.includes('governance')) {
      return 'maintenance';
    }
    
    return 'implementation'; // Default phase
  }

  /**
   * Generate comprehensive prioritization metrics
   */
  private generatePrioritizationMetrics(
    tasks: PrioritizedTask[],
    issues: Issue[]
  ): PrioritizationMetrics {
    const criticalIssues = issues.filter(i => i.severity === Severity.CRITICAL).length;
    const autoFixableIssues = issues.filter(i => i.autoFixable).length;
    const estimatedTotalEffort = tasks.reduce((sum, task) => sum + task.effortEstimate.hours, 0);
    
    // Identify quick wins (high impact, low effort)
    const quickWins = tasks.filter(task => 
      task.priorityScore.overall > 7 && task.effortEstimate.hours < 16
    ).slice(0, 5);
    
    // Identify high impact tasks
    const highImpactTasks = tasks.filter(task => 
      task.priorityScore.userImpact > 8
    ).slice(0, 5);
    
    const phases = new Set(tasks.map(t => t.phase)).size;
    
    return {
      totalIssues: issues.length,
      criticalIssues,
      autoFixableIssues,
      estimatedTotalEffort,
      recommendedPhases: phases,
      quickWins,
      highImpactTasks
    };
  }

  /**
   * Create final remediation plan
   */
  private createRemediationPlan(
    tasks: PrioritizedTask[],
    metrics: PrioritizationMetrics
  ): RemediationPlan {
    const remediationTasks: RemediationTask[] = tasks.map(task => ({
      id: task.id,
      description: task.description,
      type: task.type,
      effort: Math.ceil(task.effortEstimate.hours / 8), // Convert back to days
      dependencies: task.dependencies.flatMap(dep => dep.dependsOn),
      autoFixable: task.autoFixable
    }));

    const dependencies = tasks.flatMap(task => 
      task.dependencies.map(dep => ({
        taskId: task.id,
        dependsOn: dep.dependsOn
      }))
    );

    return {
      tasks: remediationTasks,
      dependencies,
      estimatedEffort: Math.ceil(metrics.estimatedTotalEffort / 8), // Convert to days
      priority: Math.round(
        tasks.reduce((sum, task) => sum + task.priorityScore.overall, 0) / tasks.length
      )
    };
  }

  private getTaskTypeFromTitle(title: string): string {
    if (title.includes('Color')) return 'color_standardization';
    if (title.includes('Spacing')) return 'spacing_standardization';
    if (title.includes('Typography')) return 'typography_standardization';
    if (title.includes('Component')) return 'component_standardization';
    if (title.includes('Content')) return 'content_standardization';
    if (title.includes('Governance')) return 'governance_setup';
    return 'general_standardization';
  }
}