import { useEffect } from "react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import iceTrainingImage from "@/assets/elev802-ice-picture.jpg";
import vegasTrainingImage from "@/assets/elev802-vegas.jpg";
import SEO from "@/components/SEO";
import { Breadcrumbs } from "@/components/ui/Breadcrumbs";
import { useBreadcrumbs, useShouldShowBreadcrumbs } from "@/hooks/useBreadcrumbs";

const HockeyTraining = () => {
  useEffect(() => {
    // Page title now handled by SEO component

    // Implement scroll reveal
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("active");
          }
        });
      },
      { threshold: 0.1 }
    );

    const revealElements = document.querySelectorAll(".reveal");
    revealElements.forEach((el) => observer.observe(el));

    return () => {
      revealElements.forEach((el) => observer.unobserve(el));
    };
  }, []);

  return (
    <div className="flex flex-col min-h-screen">
      <SEO
        title="Hockey Training - ELEV802 | 702HOCKEY - Premier Hockey Training in Las Vegas"
        description="Access expert hockey coaching and individualized training programs at ELEV802's premier hockey training facility in Las Vegas."
        keywords="ELEV802, hockey training, Las Vegas hockey, ice hockey coaching, hockey skills development, 702Hockey"
        canonical="/hockey-training"
        image="/images/on-ice-training.jpg"
      />
      <Header />
      {useShouldShowBreadcrumbs() && (
        <Breadcrumbs
          items={useBreadcrumbs()}
          showHomeIcon={true}
          separator="chevron"
        />
      )}
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="w-full bg-gradient-to-r from-blue-950 to-blue-800 text-white py-32">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-5xl font-bold mb-6">ON ICE TRAINING</h1>
            <p className="text-xl max-w-3xl mx-auto mb-8">
              ELEV802 | 702HOCKEY offers a comprehensive range of on-ice training programs designed to develop players of all ages and skill levels. Our expert coaches use proven techniques to enhance skating, stickhandling, shooting, and game awareness.
            </p>
          </div>
        </section>

        {/* Small Group Skills Section */}
        <section className="py-16 bg-white reveal">
          <div className="container mx-auto px-4">
            <h2 className="text-4xl font-bold text-center mb-12 text-blue-900">SMALL GROUP SKILLS SESSIONS</h2>
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div>
                <p className="text-lg mb-6">
                  Our small group skills sessions offer personalized attention and focused skills development in a collaborative environment. These sessions are designed to improve fundamental hockey skills through progressive drills and real-game scenarios.
                </p>
                <p className="text-lg mb-6">
                  With a maximum of 10 skaters per session, our experienced coaches can provide individualized feedback while fostering a competitive and supportive atmosphere.
                </p>
                <ul className="list-disc pl-6 mb-6 text-lg">
                  <li>Specialized skill development</li>
                  <li>Position-specific training</li>
                  <li>Progressive skill advancement</li>
                  <li>Performance tracking</li>
                </ul>
              </div>
              <div className="rounded-lg overflow-hidden shadow-xl">
                <img 
                  src={iceTrainingImage} 
                  alt="Small Group Skills Session" 
                  className="w-full h-[300px] object-cover"
                />
              </div>
            </div>
          </div>
        </section>



        {/* Team Training Section */}
        <section className="py-16 bg-white reveal">
          <div className="container mx-auto px-4">
            <h2 className="text-4xl font-bold text-center mb-12 text-blue-900">TEAM TRAINING</h2>
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div>
                <p className="text-lg mb-6">
                  Our team training programs are customized to meet the specific needs of your team. We work closely with coaches to develop targeted practices that address team weaknesses, enhance strengths, and improve overall performance.
                </p>
                <p className="text-lg mb-6">
                  From systems implementation to position-specific training, our comprehensive approach ensures that every player understands their role and contributes to the team's success.
                </p>
                <ul className="list-disc pl-6 mb-6 text-lg">
                  <li>Customized practice plans</li>
                  <li>Systems implementation</li>
                  <li>Team-building exercises</li>
                  <li>Special teams training</li>
                  <li>Game situation simulations</li>
                </ul>
              </div>
              <div className="rounded-lg overflow-hidden shadow-xl">
                <img 
                  src={vegasTrainingImage} 
                  alt="Team Training Session" 
                  className="w-full h-[300px] object-cover"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Private Rentals Section */}
        <section className="py-16 bg-elev-background-muted reveal">
          <div className="container mx-auto px-4">
            <h2 className="text-4xl font-bold text-center mb-12 text-blue-900">PRIVATE RENTALS</h2>
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div className="order-2 md:order-1 rounded-lg overflow-hidden shadow-xl">
                <img 
                  src={vegasTrainingImage} 
                  alt="Private Hockey Training" 
                  className="w-full h-[300px] object-cover"
                />
              </div>
              <div className="order-1 md:order-2">
                <p className="text-lg mb-6">
                  Our private rentals offer the ultimate personalized training experience. Whether you're looking for one-on-one coaching or small group sessions with friends or teammates, our private rentals provide the focused attention needed to rapidly improve skills.
                </p>
                <p className="text-lg mb-6">
                  With access to our state-of-the-art facility and equipment, players can work on specific aspects of their game under the guidance of our expert coaches.
                </p>
                <ul className="list-disc pl-6 mb-6 text-lg">
                  <li>Individualized instruction</li>
                  <li>Targeted skill development</li>
                  <li>Flexible scheduling</li>
                  <li>Custom training programs</li>
                  <li>Progress tracking and assessment</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-blue-900 text-white reveal">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-4xl font-bold mb-6">TAKE YOUR GAME TO THE NEXT LEVEL</h2>
            <p className="text-xl max-w-3xl mx-auto mb-8">
              Join 702HOCKEY today and experience the difference our professional coaching and state-of-the-art facility can make in your hockey development.
            </p>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default HockeyTraining;
