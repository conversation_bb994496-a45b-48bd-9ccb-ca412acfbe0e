// Typography usage and sizing analyzer for detecting inconsistent font usage and hierarchy violations

import { 
  <PERSON><PERSON><PERSON>, 
  AnalyzerResult, 
  FileInfo, 
  Issue, 
  IssueType, 
  Severity, 
  Category, 
  FileLocation, 
  ImpactAssessment 
} from '../types';
import { auditLogger } from '../core/logger';

interface TypographyMatch {
  value: string;
  property: string;
  type: 'hardcoded' | 'token' | 'utility';
  unit?: 'px' | 'rem' | 'em' | '%' | 'pt' | 'unitless';
  line: number;
  column: number;
  context: string;
}

interface FontFamilyMatch {
  value: string;
  type: 'hardcoded' | 'token' | 'system';
  line: number;
  column: number;
  context: string;
}

interface TypographyHierarchy {
  level: string;
  fontSize: string;
  lineHeight?: string;
  fontWeight?: string;
  location: FileLocation;
}

export class TypographyAnalyzer implements Analyzer {
  name = 'typography-analyzer';

  // Typography-related CSS properties
  private typographyProperties = [
    'font-size', 'font-family', 'font-weight', 'font-style', 'font-variant',
    'line-height', 'letter-spacing', 'word-spacing', 'text-transform',
    'text-decoration', 'text-align', 'text-indent'
  ];

  // Common font size properties and their hierarchy expectations
  private hierarchyProperties = {
    'h1': { expectedSize: ['2.5rem', '40px', '2.5em'], weight: ['700', 'bold'] },
    'h2': { expectedSize: ['2rem', '32px', '2em'], weight: ['600', 'semibold'] },
    'h3': { expectedSize: ['1.75rem', '28px', '1.75em'], weight: ['600', 'semibold'] },
    'h4': { expectedSize: ['1.5rem', '24px', '1.5em'], weight: ['500', 'medium'] },
    'h5': { expectedSize: ['1.25rem', '20px', '1.25em'], weight: ['500', 'medium'] },
    'h6': { expectedSize: ['1rem', '16px', '1em'], weight: ['500', 'medium'] }
  };

  // Design token patterns for typography
  private typographyTokenPatterns = [
    { pattern: /var\(--font-[\w-]+\)/g, description: 'CSS font custom properties' },
    { pattern: /var\(--text-[\w-]+\)/g, description: 'CSS text custom properties' },
    { pattern: /theme\(['"`]fontSize\.[\w.-]+['"`]\)/g, description: 'Theme fontSize function' },
    { pattern: /theme\(['"`]fontFamily\.[\w.-]+['"`]\)/g, description: 'Theme fontFamily function' },
    { pattern: /fontSize\.[\w.-]+/g, description: 'Font size object references' },
    { pattern: /fontFamily\.[\w.-]+/g, description: 'Font family object references' },
    { pattern: /\$font-[\w-]+/g, description: 'SCSS font variables' },
    { pattern: /\$text-[\w-]+/g, description: 'SCSS text variables' },
    { pattern: /@font-[\w-]+/g, description: 'Less font variables' }
  ];

  // System font stacks (considered acceptable)
  private systemFontStacks = [
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'sans-serif',
    'serif',
    'monospace',
    'cursive',
    'fantasy'
  ];

  // Common font size scale (in rem)
  private commonFontSizes = [
    0.75, 0.875, 1, 1.125, 1.25, 1.375, 1.5, 1.75, 2, 2.25, 2.5, 3, 3.75, 4.5, 6
  ];

  async analyze(files: FileInfo[]): Promise<AnalyzerResult> {
    const startTime = Date.now();
    auditLogger.info('Starting typography analysis', { fileCount: files.length });

    const issues: Issue[] = [];
    const typographyUsageMap = new Map<string, TypographyMatch[]>();
    const fontFamilyUsageMap = new Map<string, FontFamilyMatch[]>();
    const hierarchyMap = new Map<string, TypographyHierarchy[]>();

    // Filter files that might contain typography definitions
    const relevantFiles = files.filter(file => 
      ['.tsx', '.jsx', '.ts', '.js', '.css', '.scss', '.sass', '.less'].includes(file.extension) ||
      file.path.includes('style') ||
      file.path.includes('typography') ||
      file.path.includes('font') ||
      file.path.includes('text') ||
      file.path.includes('component')
    );

    for (const file of relevantFiles) {
      try {
        const fileIssues = await this.analyzeFile(file, typographyUsageMap, fontFamilyUsageMap, hierarchyMap);
        issues.push(...fileIssues);
      } catch (error) {
        auditLogger.error('Failed to analyze file for typography', error as Error, { 
          filePath: file.path 
        });
      }
    }

    // Analyze typography patterns across files
    const patternIssues = this.analyzeTypographyPatterns(typographyUsageMap);
    issues.push(...patternIssues);

    // Analyze font family consistency
    const fontFamilyIssues = this.analyzeFontFamilyPatterns(fontFamilyUsageMap);
    issues.push(...fontFamilyIssues);

    // Analyze typography hierarchy violations
    const hierarchyIssues = this.analyzeTypographyHierarchy(hierarchyMap);
    issues.push(...hierarchyIssues);

    const executionTime = Date.now() - startTime;
    
    const result: AnalyzerResult = {
      analyzerName: this.name,
      issues,
      summary: {
        totalIssues: issues.length,
        severityBreakdown: this.calculateSeverityBreakdown(issues),
        categoryBreakdown: this.calculateCategoryBreakdown(issues)
      },
      executionTime
    };

    auditLogger.info('Typography analysis completed', { 
      issuesFound: issues.length,
      executionTime 
    });

    return result;
  }

  private async analyzeFile(
    file: FileInfo, 
    typographyUsageMap: Map<string, TypographyMatch[]>,
    fontFamilyUsageMap: Map<string, FontFamilyMatch[]>,
    hierarchyMap: Map<string, TypographyHierarchy[]>
  ): Promise<Issue[]> {
    const issues: Issue[] = [];
    const lines = file.content.split('\n');
    
    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex];
      const lineNumber = lineIndex + 1;

      // Skip comments
      if (this.isCommentLine(line)) {
        continue;
      }

      // Find typography values
      const typographyMatches = this.findTypographyValues(line, lineNumber);
      
      // Find font family usage
      const fontFamilyMatches = this.findFontFamilyUsage(line, lineNumber);

      // Find hierarchy elements
      const hierarchyElements = this.findHierarchyElements(line, lineNumber, file.path);

      // Track typography usage for pattern analysis
      typographyMatches.forEach(typography => {
        const key = `${file.path}:${typography.property}:${typography.value}`;
        if (!typographyUsageMap.has(key)) {
          typographyUsageMap.set(key, []);
        }
        typographyUsageMap.get(key)!.push({
          ...typography,
          context: line.trim()
        });
      });

      // Track font family usage
      fontFamilyMatches.forEach(fontFamily => {
        const key = `${file.path}:${fontFamily.value}`;
        if (!fontFamilyUsageMap.has(key)) {
          fontFamilyUsageMap.set(key, []);
        }
        fontFamilyUsageMap.get(key)!.push({
          ...fontFamily,
          context: line.trim()
        });
      });

      // Track hierarchy elements
      hierarchyElements.forEach(hierarchy => {
        const key = `${file.path}:${hierarchy.level}`;
        if (!hierarchyMap.has(key)) {
          hierarchyMap.set(key, []);
        }
        hierarchyMap.get(key)!.push(hierarchy);
      });

      // Create issues for hardcoded typography values
      for (const typography of typographyMatches) {
        if (this.shouldFlagTypographyValue(typography, line)) {
          const issue = this.createTypographyIssue(file, typography, lineNumber, line);
          issues.push(issue);
        }
      }

      // Create issues for inconsistent font families
      for (const fontFamily of fontFamilyMatches) {
        if (this.shouldFlagFontFamily(fontFamily, line)) {
          const issue = this.createFontFamilyIssue(file, fontFamily, lineNumber, line);
          issues.push(issue);
        }
      }

      // Check for typography consistency within the line
      const consistencyIssues = this.checkTypographyConsistency(file, typographyMatches, lineNumber, line);
      issues.push(...consistencyIssues);
    }

    return issues;
  }

  private isCommentLine(line: string): boolean {
    const trimmed = line.trim();
    return trimmed.startsWith('//') || 
           trimmed.startsWith('/*') || 
           trimmed.startsWith('*') ||
           trimmed.startsWith('<!--');
  }

  private findTypographyValues(line: string, lineNumber: number): TypographyMatch[] {
    const matches: TypographyMatch[] = [];

    // Check for CSS property declarations
    this.typographyProperties.forEach(property => {
      const propertyPattern = new RegExp(`${property}\\s*:\\s*([^;]+)`, 'gi');
      let match;
      
      while ((match = propertyPattern.exec(line)) !== null) {
        const value = match[1].trim();
        const typographyMatch = this.parseTypographyValue(value, property, lineNumber, match.index);
        if (typographyMatch) {
          matches.push(typographyMatch);
        }
      }
    });

    // Check for inline styles in React/JSX
    const inlineStylePattern = /style\s*=\s*\{\{([^}]+)\}\}/g;
    let inlineMatch;
    
    while ((inlineMatch = inlineStylePattern.exec(line)) !== null) {
      const styleContent = inlineMatch[1];
      const inlineMatches = this.parseInlineTypographyStyles(styleContent, lineNumber, inlineMatch.index);
      matches.push(...inlineMatches);
    }

    return matches;
  }

  private parseTypographyValue(value: string, property: string, line: number, column: number): TypographyMatch | null {
    // Check if it's a design token
    const isToken = this.typographyTokenPatterns.some(({ pattern }) => {
      const regex = new RegExp(pattern.source, pattern.flags);
      return regex.test(value);
    });
    
    if (isToken) {
      return {
        value,
        property,
        type: 'token',
        unit: this.extractTypographyUnit(value),
        line,
        column,
        context: ''
      };
    }

    // Check for hardcoded values
    if (property === 'font-size') {
      const numericPattern = /^(\d+(?:\.\d+)?)(px|rem|em|%|pt)?$/;
      const match = value.match(numericPattern);
      
      if (match) {
        return {
          value,
          property,
          type: 'hardcoded',
          unit: (match[2] as TypographyMatch['unit']) || 'unitless',
          line,
          column,
          context: ''
        };
      }
    }

    // Check for other typography properties
    if (['font-weight', 'line-height', 'letter-spacing', 'word-spacing'].includes(property)) {
      // These can be hardcoded values we want to track
      return {
        value,
        property,
        type: 'hardcoded',
        unit: this.extractTypographyUnit(value),
        line,
        column,
        context: ''
      };
    }

    return null;
  }

  private parseInlineTypographyStyles(styleContent: string, line: number, baseColumn: number): TypographyMatch[] {
    const matches: TypographyMatch[] = [];
    
    this.typographyProperties.forEach(property => {
      const camelCaseProperty = this.toCamelCase(property);
      const pattern = new RegExp(`${camelCaseProperty}\\s*:\\s*['"\`]?([^,}]+)['"\`]?`, 'gi');
      let match;
      
      while ((match = pattern.exec(styleContent)) !== null) {
        const value = match[1].trim().replace(/['"`]/g, '');
        const typographyMatch = this.parseTypographyValue(value, property, line, baseColumn + match.index);
        if (typographyMatch) {
          typographyMatch.context = styleContent.trim();
          matches.push(typographyMatch);
        }
      }
    });

    return matches;
  }

  private findFontFamilyUsage(line: string, lineNumber: number): FontFamilyMatch[] {
    const matches: FontFamilyMatch[] = [];

    // CSS font-family declarations
    const fontFamilyPattern = /font-family\s*:\s*([^;]+)/gi;
    let match: RegExpExecArray | null;
    
    while ((match = fontFamilyPattern.exec(line)) !== null) {
      const value = match[1].trim();
      const fontFamilyMatch = this.parseFontFamilyValue(value, lineNumber, match.index);
      matches.push(fontFamilyMatch);
    }

    // Inline styles
    const inlineStylePattern = /fontFamily\s*:\s*['"`]([^'"`]+)['"`]/gi;
    while ((match = inlineStylePattern.exec(line)) !== null) {
      const value = match[1].trim();
      const fontFamilyMatch = this.parseFontFamilyValue(value, lineNumber, match.index);
      matches.push(fontFamilyMatch);
    }

    return matches;
  }

  private parseFontFamilyValue(value: string, line: number, column: number): FontFamilyMatch {
    // Check if it's a design token
    const isToken = this.typographyTokenPatterns.some(({ pattern }) => {
      const regex = new RegExp(pattern.source, pattern.flags);
      return regex.test(value);
    });
    
    if (isToken) {
      return {
        value,
        type: 'token',
        line,
        column,
        context: ''
      };
    }

    // Check if it's a system font stack - be more specific about custom fonts
    const isSystemFont = this.systemFontStacks.some(systemFont => 
      value.toLowerCase().includes(systemFont.toLowerCase())
    );

    // If it contains quotes and isn't a system font, it's likely a custom font
    const hasCustomFont = value.includes('"') && !isSystemFont;

    return {
      value,
      type: hasCustomFont ? 'hardcoded' : (isSystemFont ? 'system' : 'hardcoded'),
      line,
      column,
      context: ''
    };
  }

  private findHierarchyElements(line: string, lineNumber: number, filePath: string): TypographyHierarchy[] {
    const hierarchies: TypographyHierarchy[] = [];

    // Look for HTML heading tags
    const headingPattern = /<(h[1-6])[^>]*>/gi;
    let match;
    
    while ((match = headingPattern.exec(line)) !== null) {
      const level = match[1].toLowerCase();
      
      // Try to find associated styles
      const fontSize = this.extractStyleProperty(line, 'font-size') || 
                      this.extractStyleProperty(line, 'fontSize');
      const lineHeight = this.extractStyleProperty(line, 'line-height') || 
                        this.extractStyleProperty(line, 'lineHeight');
      const fontWeight = this.extractStyleProperty(line, 'font-weight') || 
                        this.extractStyleProperty(line, 'fontWeight');

      hierarchies.push({
        level,
        fontSize: fontSize || 'unknown',
        lineHeight,
        fontWeight,
        location: {
          filePath,
          lineNumber,
          columnNumber: match.index,
          context: line.trim()
        }
      });
    }

    return hierarchies;
  }

  private extractStyleProperty(line: string, property: string): string | undefined {
    const pattern = new RegExp(`${property}\\s*[:=]\\s*['"\`]?([^'"\`;,}]+)['"\`]?`, 'i');
    const match = line.match(pattern);
    return match ? match[1].trim() : undefined;
  }

  private shouldFlagTypographyValue(typography: TypographyMatch, line: string): boolean {
    // Don't flag design tokens
    if (typography.type === 'token') {
      return false;
    }

    // Don't flag common utility values
    const commonValues = ['inherit', 'initial', 'unset', 'normal', 'bold', 'italic'];
    if (commonValues.includes(typography.value.toLowerCase())) {
      return false;
    }

    // Flag hardcoded font sizes that aren't in common scale
    if (typography.property === 'font-size' && typography.unit === 'rem') {
      const numValue = parseFloat(typography.value);
      return !this.commonFontSizes.includes(numValue);
    }

    // Flag hardcoded pixel font sizes
    if (typography.property === 'font-size' && typography.unit === 'px') {
      return true; // Always flag pixel font sizes
    }

    // Flag hardcoded line-height values that aren't unitless or common ratios
    if (typography.property === 'line-height' && typography.type === 'hardcoded') {
      const numValue = parseFloat(typography.value);
      const commonLineHeights = [1, 1.2, 1.25, 1.3, 1.4, 1.5, 1.6, 1.75, 2];
      // Flag if it's not a common line height value
      return !commonLineHeights.includes(numValue);
    }

    return typography.type === 'hardcoded';
  }

  private shouldFlagFontFamily(fontFamily: FontFamilyMatch, line: string): boolean {
    // Don't flag design tokens or system fonts
    if (fontFamily.type === 'token' || fontFamily.type === 'system') {
      return false;
    }

    // Flag custom font families that might not be consistently used
    return fontFamily.type === 'hardcoded';
  }

  private createTypographyIssue(
    file: FileInfo, 
    typography: TypographyMatch, 
    lineNumber: number, 
    line: string
  ): Issue {
    const severity = this.getSeverityForTypography(typography, line);
    
    return {
      id: `typography_${file.path}_${lineNumber}_${typography.column}`,
      type: IssueType.TYPOGRAPHY_INCONSISTENCY,
      severity,
      category: Category.VISUAL,
      description: `Hardcoded ${typography.property} value '${typography.value}'. Consider using design tokens for consistency.`,
      location: {
        filePath: file.path,
        lineNumber,
        columnNumber: typography.column,
        context: line.trim()
      },
      suggestion: this.generateTypographySuggestion(typography, line),
      autoFixable: this.isTypographyAutoFixable(typography, line),
      impact: this.calculateTypographyImpact(typography, line)
    };
  }

  private createFontFamilyIssue(
    file: FileInfo, 
    fontFamily: FontFamilyMatch, 
    lineNumber: number, 
    line: string
  ): Issue {
    return {
      id: `font_family_${file.path}_${lineNumber}_${fontFamily.column}`,
      type: IssueType.TYPOGRAPHY_INCONSISTENCY,
      severity: Severity.MEDIUM,
      category: Category.VISUAL,
      description: `Custom font family '${fontFamily.value}' detected. Ensure consistent usage across the application.`,
      location: {
        filePath: file.path,
        lineNumber,
        columnNumber: fontFamily.column,
        context: line.trim()
      },
      suggestion: `Consider creating a font family token for '${fontFamily.value}' to ensure consistency.`,
      autoFixable: false,
      impact: {
        userExperience: 5,
        maintenanceEffort: 6,
        implementationComplexity: 3
      }
    };
  }

  private checkTypographyConsistency(
    file: FileInfo, 
    typographies: TypographyMatch[], 
    lineNumber: number, 
    line: string
  ): Issue[] {
    const issues: Issue[] = [];

    // Only check if we have multiple typography values in the same line
    if (typographies.length < 2) {
      return issues;
    }

    // Check for mixed typography approaches in the same line
    const typographyTypes = new Set(typographies.map(t => t.type));
    
    if (typographyTypes.size > 1 && typographyTypes.has('hardcoded') && typographyTypes.has('token')) {
      const issue: Issue = {
        id: `typography_mixed_${file.path}_${lineNumber}`,
        type: IssueType.TYPOGRAPHY_INCONSISTENCY,
        severity: Severity.MEDIUM,
        category: Category.VISUAL,
        description: 'Mixed typography approaches detected. Consider standardizing on design tokens.',
        location: {
          filePath: file.path,
          lineNumber,
          columnNumber: 0,
          context: line.trim()
        },
        suggestion: 'Use consistent typography tokens throughout the component.',
        autoFixable: false,
        impact: {
          userExperience: 4,
          maintenanceEffort: 7,
          implementationComplexity: 4
        }
      };
      
      issues.push(issue);
    }

    return issues;
  }

  private analyzeTypographyPatterns(typographyUsageMap: Map<string, TypographyMatch[]>): Issue[] {
    const issues: Issue[] = [];
    const valueFrequency = new Map<string, number>();
    
    // Count typography value frequency
    typographyUsageMap.forEach((matches, key) => {
      const value = key.split(':')[2];
      if (matches[0].type === 'hardcoded') {
        valueFrequency.set(value, (valueFrequency.get(value) || 0) + matches.length);
      }
    });

    // Find frequently used hardcoded typography values
    valueFrequency.forEach((count, value) => {
      if (count >= 3) {
        const locations = this.getTypographyLocations(value, typographyUsageMap);
        
        const issue: Issue = {
          id: `typography_frequent_${value.replace(/[^a-zA-Z0-9]/g, '_')}`,
          type: IssueType.TYPOGRAPHY_INCONSISTENCY,
          severity: count >= 5 ? Severity.HIGH : Severity.MEDIUM,
          category: Category.VISUAL,
          description: `Typography value '${value}' is used ${count} times. Consider creating a design token.`,
          location: locations[0],
          suggestion: `Create a typography token for '${value}' and replace all ${count} instances.`,
          autoFixable: true,
          impact: {
            userExperience: 5,
            maintenanceEffort: 8,
            implementationComplexity: 3
          }
        };
        
        issues.push(issue);
      }
    });

    return issues;
  }

  private analyzeFontFamilyPatterns(fontFamilyUsageMap: Map<string, FontFamilyMatch[]>): Issue[] {
    const issues: Issue[] = [];
    const fontFamilyCount = new Map<string, number>();
    
    // Count font family usage
    fontFamilyUsageMap.forEach((matches, key) => {
      const fontFamily = key.split(':')[1];
      if (matches[0].type === 'hardcoded') {
        fontFamilyCount.set(fontFamily, (fontFamilyCount.get(fontFamily) || 0) + matches.length);
      }
    });

    // Check for multiple custom font families
    const customFonts = Array.from(fontFamilyCount.keys()).filter(font => 
      !this.systemFontStacks.some(systemFont => 
        font.toLowerCase().includes(systemFont.toLowerCase())
      ) && font.includes('"')
    );

    if (customFonts.length > 2) {
      const issue: Issue = {
        id: 'font_family_inconsistent',
        type: IssueType.TYPOGRAPHY_INCONSISTENCY,
        severity: Severity.MEDIUM,
        category: Category.VISUAL,
        description: `Multiple custom font families detected (${customFonts.length}). Consider standardizing on fewer font families.`,
        location: {
          filePath: 'multiple files',
          lineNumber: 0,
          columnNumber: 0,
          context: 'Project-wide font family analysis'
        },
        suggestion: 'Standardize on 1-2 primary font families for better consistency and performance.',
        autoFixable: false,
        impact: {
          userExperience: 6,
          maintenanceEffort: 8,
          implementationComplexity: 5
        }
      };
      
      issues.push(issue);
    }

    return issues;
  }

  private analyzeTypographyHierarchy(hierarchyMap: Map<string, TypographyHierarchy[]>): Issue[] {
    const issues: Issue[] = [];
    
    // Check for hierarchy violations
    hierarchyMap.forEach((hierarchies, key) => {
      const level = key.split(':')[1];
      const expectedHierarchy = this.hierarchyProperties[level as keyof typeof this.hierarchyProperties];
      
      if (expectedHierarchy) {
        hierarchies.forEach(hierarchy => {
          // Check font size consistency
          if (hierarchy.fontSize !== 'unknown' && 
              !expectedHierarchy.expectedSize.includes(hierarchy.fontSize) &&
              !this.isTokenValue(hierarchy.fontSize)) {
            
            const issue: Issue = {
              id: `hierarchy_${hierarchy.location.filePath}_${hierarchy.location.lineNumber}`,
              type: IssueType.TYPOGRAPHY_INCONSISTENCY,
              severity: Severity.MEDIUM,
              category: Category.VISUAL,
              description: `${level.toUpperCase()} element has non-standard font size '${hierarchy.fontSize}'. Expected sizes: ${expectedHierarchy.expectedSize.join(', ')}.`,
              location: hierarchy.location,
              suggestion: `Use a standard ${level} font size or create a consistent typography scale.`,
              autoFixable: false,
              impact: {
                userExperience: 6,
                maintenanceEffort: 5,
                implementationComplexity: 3
              }
            };
            
            issues.push(issue);
          }
        });
      }
    });

    return issues;
  }

  private isTokenValue(value: string): boolean {
    return this.typographyTokenPatterns.some(({ pattern }) => {
      const regex = new RegExp(pattern.source, pattern.flags);
      return regex.test(value);
    });
  }

  private getTypographyLocations(value: string, typographyUsageMap: Map<string, TypographyMatch[]>): FileLocation[] {
    const locations: FileLocation[] = [];
    
    typographyUsageMap.forEach((matches, key) => {
      if (key.includes(value)) {
        const filePath = key.split(':')[0];
        matches.forEach(match => {
          locations.push({
            filePath,
            lineNumber: match.line,
            columnNumber: match.column,
            context: match.context
          });
        });
      }
    });

    return locations;
  }

  private getSeverityForTypography(typography: TypographyMatch, line: string): Severity {
    // High severity for font-size inconsistencies
    if (typography.property === 'font-size' && typography.unit === 'px') {
      return Severity.HIGH;
    }

    // Medium severity for other hardcoded typography values
    if (typography.type === 'hardcoded') {
      return Severity.MEDIUM;
    }

    return Severity.LOW;
  }

  private generateTypographySuggestion(typography: TypographyMatch, line: string): string {
    const suggestions = [
      `Replace '${typography.value}' with a typography token like 'var(--font-size-lg)'`,
      `Use theme typography: 'theme("fontSize.lg")'`,
      `Define as SCSS variable: '$font-size-lg: ${typography.value}'`,
      `Use Tailwind typography utility: 'text-lg' instead of hardcoded values`
    ];

    // Return most appropriate suggestion based on context
    if (line.includes('var(')) return suggestions[0];
    if (line.includes('theme(')) return suggestions[1];
    if (line.includes('$')) return suggestions[2];
    if (line.includes('className')) return suggestions[3];
    
    return suggestions[0]; // Default to CSS custom properties
  }

  private isTypographyAutoFixable(typography: TypographyMatch, line: string): boolean {
    // Simple hardcoded typography values in CSS properties are auto-fixable
    return typography.type === 'hardcoded' && 
           this.typographyProperties.some(prop => line.includes(`${prop}:`));
  }

  private calculateTypographyImpact(typography: TypographyMatch, line: string): ImpactAssessment {
    let userExperience = 4;
    let maintenanceEffort = 6;
    let implementationComplexity = 3;

    // Higher impact for font-size (affects readability)
    if (typography.property === 'font-size') {
      userExperience += 2;
      maintenanceEffort += 1;
    }

    // Higher impact for font-family (affects brand consistency)
    if (typography.property === 'font-family') {
      userExperience += 3;
      maintenanceEffort += 2;
    }

    // Higher maintenance effort for pixel values (don't scale)
    if (typography.unit === 'px') {
      maintenanceEffort += 2;
      implementationComplexity += 1;
    }

    return {
      userExperience: Math.min(10, userExperience),
      maintenanceEffort: Math.min(10, maintenanceEffort),
      implementationComplexity: Math.min(10, implementationComplexity)
    };
  }

  private extractTypographyUnit(value: string): TypographyMatch['unit'] {
    if (value.includes('px')) return 'px';
    if (value.includes('rem')) return 'rem';
    if (value.includes('em')) return 'em';
    if (value.includes('%')) return '%';
    if (value.includes('pt')) return 'pt';
    return 'unitless';
  }

  private toCamelCase(str: string): string {
    return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
  }

  getSeverity(issue: Omit<Issue, 'severity'>): Severity {
    if (issue.type === IssueType.TYPOGRAPHY_INCONSISTENCY) {
      if (issue.description.includes('frequently used')) {
        return Severity.HIGH;
      }
      if (issue.description.includes('font-size') && issue.description.includes('px')) {
        return Severity.HIGH;
      }
      if (issue.description.includes('hardcoded')) {
        return Severity.MEDIUM;
      }
      if (issue.description.includes('Mixed')) {
        return Severity.MEDIUM;
      }
    }
    
    return Severity.LOW;
  }

  private calculateSeverityBreakdown(issues: Issue[]): Record<Severity, number> {
    const breakdown: Record<Severity, number> = {
      [Severity.LOW]: 0,
      [Severity.MEDIUM]: 0,
      [Severity.HIGH]: 0,
      [Severity.CRITICAL]: 0
    };

    issues.forEach(issue => {
      breakdown[issue.severity]++;
    });

    return breakdown;
  }

  private calculateCategoryBreakdown(issues: Issue[]): Record<Category, number> {
    const breakdown: Record<Category, number> = {
      [Category.VISUAL]: 0,
      [Category.CONTENT]: 0,
      [Category.COMPONENT]: 0,
      [Category.ACCESSIBILITY]: 0
    };

    issues.forEach(issue => {
      breakdown[issue.category]++;
    });

    return breakdown;
  }
}