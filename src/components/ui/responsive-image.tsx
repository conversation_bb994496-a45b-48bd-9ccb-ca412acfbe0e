import { cn } from '@/lib/utils';
import React from 'react';

interface ImageSource {
  src: string;
  width: number;
  media?: string; // Media query for art direction
}

interface ResponsiveImageProps {
  src: string;
  alt: string;
  sources?: ImageSource[];
  className?: string;
  sizes?: string;
  loading?: 'eager' | 'lazy';
  width?: number | string;
  height?: number | string;
  aspectRatio?: 'square' | 'video' | 'portrait' | 'landscape' | 'auto';
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
  priority?: boolean; // For above-the-fold images
  placeholder?: string; // Base64 or low-quality placeholder
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * Enhanced Responsive Image Component
 * 
 * Renders an optimized image with responsive sizing, lazy loading, and proper aspect ratios
 * Supports multiple image sources for different screen sizes and art direction
 */
export function ResponsiveImage({
  src,
  alt,
  sources = [],
  className,
  sizes = '100vw',
  loading = 'lazy',
  width,
  height,
  aspectRatio = 'auto',
  objectFit = 'cover',
  priority = false,
  placeholder,
  onLoad,
  onError,
  ...props
}: ResponsiveImageProps) {
  // Generate srcSet string from sources array
  const srcSet = sources.length > 0
    ? sources.map(source => `${source.src} ${source.width}w`).join(', ')
    : undefined;

  // Determine aspect ratio classes
  const getAspectRatioClass = () => {
    switch (aspectRatio) {
      case 'square':
        return 'aspect-square';
      case 'video':
        return 'aspect-video';
      case 'portrait':
        return 'aspect-[3/4]';
      case 'landscape':
        return 'aspect-[4/3]';
      default:
        return '';
    }
  };

  // Determine object-fit classes
  const getObjectFitClass = () => {
    switch (objectFit) {
      case 'cover':
        return 'object-cover';
      case 'contain':
        return 'object-contain';
      case 'fill':
        return 'object-fill';
      case 'none':
        return 'object-none';
      case 'scale-down':
        return 'object-scale-down';
      default:
        return 'object-cover';
    }
  };

  // Use eager loading for priority images
  const imageLoading = priority ? 'eager' : loading;

  return (
    <div className={cn(
      'relative overflow-hidden',
      getAspectRatioClass(),
      className
    )}>
      {/* Placeholder/Loading state */}
      {placeholder && (
        <img
          src={placeholder}
          alt=""
          className={cn(
            'absolute inset-0 w-full h-full blur-sm scale-110 transition-opacity duration-300',
            getObjectFitClass()
          )}
          aria-hidden="true"
        />
      )}
      
      {/* Main image */}
      <img
        src={src}
        alt={alt}
        srcSet={srcSet}
        sizes={sizes}
        loading={imageLoading}
        className={cn(
          'w-full h-full transition-opacity duration-300',
          getObjectFitClass(),
          placeholder && 'absolute inset-0'
        )}
        width={width}
        height={height}
        onLoad={onLoad}
        onError={onError}
        {...props}
      />
    </div>
  );
}

export default ResponsiveImage;
