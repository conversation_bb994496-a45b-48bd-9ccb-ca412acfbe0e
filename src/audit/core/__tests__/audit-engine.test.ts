// Basic tests for audit engine functionality

import { DesignSystemAuditEngine } from '../audit-engine';
import { <PERSON><PERSON><PERSON>, AnalyzerR<PERSON>ult, FileInfo, Severity, IssueType, Category } from '../../types';

// Mock analyzer for testing
class MockAnalyzer implements Analyzer {
  name = 'mock-analyzer';

  async analyze(files: FileInfo[]): Promise<AnalyzerResult> {
    return {
      analyzerName: this.name,
      issues: [
        {
          id: 'test-issue-1',
          type: IssueType.COLOR_INCONSISTENCY,
          severity: Severity.MEDIUM,
          category: Category.VISUAL,
          description: 'Test color inconsistency',
          location: {
            filePath: 'test.tsx',
            lineNumber: 1,
            columnNumber: 1,
            context: 'test context'
          },
          suggestion: 'Use design tokens',
          autoFixable: true,
          impact: {
            userExperience: 5,
            maintenanceEffort: 3,
            implementationComplexity: 2
          }
        }
      ],
      summary: {
        totalIssues: 1,
        severityBreakdown: {
          [Severity.LOW]: 0,
          [Severity.MEDIUM]: 1,
          [Severity.HIGH]: 0,
          [Severity.CRITICAL]: 0
        },
        categoryBreakdown: {
          [Category.VISUAL]: 1,
          [Category.CONTENT]: 0,
          [Category.COMPONENT]: 0,
          [Category.ACCESSIBILITY]: 0
        }
      },
      executionTime: 100
    };
  }

  getSeverity(): Severity {
    return Severity.MEDIUM;
  }
}

describe('DesignSystemAuditEngine', () => {
  let auditEngine: DesignSystemAuditEngine;
  let mockAnalyzer: MockAnalyzer;

  beforeEach(() => {
    auditEngine = new DesignSystemAuditEngine();
    mockAnalyzer = new MockAnalyzer();
  });

  test('should register analyzer successfully', () => {
    auditEngine.registerAnalyzer(mockAnalyzer);
    const registeredAnalyzers = auditEngine.getRegisteredAnalyzers();
    expect(registeredAnalyzers).toContain('mock-analyzer');
  });

  test('should generate report from analyzer results', () => {
    const mockResults: AnalyzerResult[] = [
      {
        analyzerName: 'test-analyzer',
        issues: [],
        summary: {
          totalIssues: 0,
          severityBreakdown: {
            [Severity.LOW]: 0,
            [Severity.MEDIUM]: 0,
            [Severity.HIGH]: 0,
            [Severity.CRITICAL]: 0
          },
          categoryBreakdown: {
            [Category.VISUAL]: 0,
            [Category.CONTENT]: 0,
            [Category.COMPONENT]: 0,
            [Category.ACCESSIBILITY]: 0
          }
        },
        executionTime: 50
      }
    ];

    const report = auditEngine.generateReport(mockResults);
    
    expect(report).toBeDefined();
    expect(report.results).toEqual(mockResults);
    expect(report.summary.totalIssues).toBe(0);
    expect(report.generatedAt).toBeInstanceOf(Date);
  });

  test('should get configuration', () => {
    const config = auditEngine.getConfig();
    expect(config).toBeDefined();
    expect(config.includePatterns).toContain('**/*.tsx');
    expect(config.excludePatterns).toContain('**/node_modules/**');
  });
});

// Export for potential use in other tests
export { MockAnalyzer };