/**
 * Style Guidelines Generator
 * Creates comprehensive content and style guidelines including tone, voice, terminology, and messaging patterns
 */

import { promises as fs } from 'fs';
import * as path from 'path';
import {
  StyleGuidelines,
  GuidelineSection,
  StyleRule,
  ToneGuidelines,
  TerminologyStandards,
  MessagingPatterns,
  GuidelineExample,
  TonePrinciple,
  ToneContext,
  ToneExample,
  VoiceCharacteristics,
  TermEntry,
  TermCategory,
  MessagePattern,
  MessageContext,
  MessageTemplate
} from './types';

export class StyleGuidelineGenerator {
  private outputDir: string;

  constructor(outputDir: string = './docs/guidelines') {
    this.outputDir = outputDir;
  }

  /**
   * Generate comprehensive style guidelines
   */
  async generateGuidelines(
    options: StyleGuidelineOptions = {}
  ): Promise<StyleGuidelines> {
    const sections = this.createGuidelineSections();
    const toneAndVoice = this.createToneGuidelines(options.brandPersonality);
    const terminology = this.createTerminologyStandards(options.customTerms);
    const messaging = this.createMessagingPatterns(options.messageContexts);
    const examples = this.createGuidelineExamples();

    const guidelines: StyleGuidelines = {
      sections,
      toneAndVoice,
      terminology,
      messaging,
      examples
    };

    // Generate documentation files
    await this.generateMarkdownDocs(guidelines);
    await this.generateJSONDocs(guidelines);
    await this.generateValidationRules(guidelines);
    await this.generateLintingConfig(guidelines);

    return guidelines;
  }  /*
*
   * Create guideline sections
   */
  private createGuidelineSections(): GuidelineSection[] {
    return [
      this.createWritingStyleSection(),
      this.createToneSection(),
      this.createTerminologySection(),
      this.createMessagingSection(),
      this.createAccessibilitySection(),
      this.createLocalizationSection()
    ];
  }

  /**
   * Create writing style section
   */
  private createWritingStyleSection(): GuidelineSection {
    return {
      title: 'Writing Style',
      slug: 'writing-style',
      description: 'Guidelines for consistent writing style across all content',
      rules: [
        {
          title: 'Use active voice',
          description: 'Write in active voice to create clear, direct communication',
          do: [
            'The system processes your request',
            'You can update your settings',
            'We recommend using this feature'
          ],
          dont: [
            'Your request is processed by the system',
            'Settings can be updated by you',
            'This feature is recommended'
          ],
          examples: [
            {
              correct: 'Click Save to store your changes',
              incorrect: 'Your changes will be stored when Save is clicked',
              explanation: 'Active voice makes the action clear and direct'
            }
          ]
        },
        {
          title: 'Write concisely',
          description: 'Use clear, concise language that gets to the point quickly',
          do: [
            'Enter your email address',
            'File uploaded successfully',
            'Choose a payment method'
          ],
          dont: [
            'Please enter your email address in the field below',
            'Your file has been uploaded successfully to our servers',
            'You need to choose which payment method you would like to use'
          ],
          examples: [
            {
              correct: 'Password must be 8+ characters',
              incorrect: 'Your password must contain at least 8 characters in length',
              explanation: 'Concise language reduces cognitive load'
            }
          ]
        }
      ],
      examples: [
        {
          title: 'Button Text',
          description: 'Clear, action-oriented button text',
          good: {
            text: 'Save changes',
            context: 'Form submission button'
          },
          bad: {
            text: 'Submit',
            context: 'Generic button text'
          },
          explanation: 'Specific action words help users understand what will happen',
          category: 'writing-style'
        }
      ]
    };
  } 
 /**
   * Create tone section
   */
  private createToneSection(): GuidelineSection {
    return {
      title: 'Tone and Voice',
      slug: 'tone-voice',
      description: 'Guidelines for maintaining consistent tone and voice across all communications',
      rules: [
        {
          title: 'Be helpful and supportive',
          description: 'Always aim to help users accomplish their goals',
          do: [
            'Here\'s how to fix this issue',
            'Let\'s get this sorted out',
            'You\'re almost done!'
          ],
          dont: [
            'You did something wrong',
            'This is broken',
            'Error occurred'
          ],
          examples: [
            {
              correct: 'We couldn\'t find that file. Try checking the file name or location.',
              incorrect: 'File not found. Error 404.',
              explanation: 'Helpful tone provides guidance instead of just stating the problem'
            }
          ]
        }
      ],
      examples: [
        {
          title: 'Error Messages',
          description: 'Helpful error communication',
          good: {
            text: 'Oops! We couldn\'t save your changes. Please check your internet connection and try again.',
            context: 'Network error message'
          },
          bad: {
            text: 'Save failed. Network error.',
            context: 'Generic error message'
          },
          explanation: 'Helpful tone acknowledges the issue and provides next steps',
          category: 'tone-voice'
        }
      ]
    };
  }

  /**
   * Create terminology section
   */
  private createTerminologySection(): GuidelineSection {
    return {
      title: 'Terminology',
      slug: 'terminology',
      description: 'Consistent terminology and word choices across the application',
      rules: [
        {
          title: 'Use consistent terms',
          description: 'Always use the same term for the same concept throughout the application',
          do: [
            'Use "sign in" consistently (not "log in" or "login")',
            'Use "account" consistently (not "profile" or "user")',
            'Use "settings" consistently (not "preferences" or "options")'
          ],
          dont: [
            'Mix "sign in", "log in", and "login"',
            'Switch between "account" and "profile"',
            'Use "settings" and "preferences" interchangeably'
          ],
          examples: [
            {
              correct: 'Sign in to your account',
              incorrect: 'Log into your profile',
              explanation: 'Consistent terminology reduces confusion'
            }
          ]
        }
      ],
      examples: [
        {
          title: 'Navigation Labels',
          description: 'Clear, consistent navigation terminology',
          good: {
            text: 'Account settings',
            context: 'Navigation menu item'
          },
          bad: {
            text: 'User preferences configuration',
            context: 'Overly technical navigation'
          },
          explanation: 'Simple, consistent terms help users navigate confidently',
          category: 'terminology'
        }
      ]
    };
  }  /**
   *
 Create messaging section
   */
  private createMessagingSection(): GuidelineSection {
    return {
      title: 'Messaging Patterns',
      slug: 'messaging',
      description: 'Consistent patterns for different types of messages and communications',
      rules: [
        {
          title: 'Structure error messages consistently',
          description: 'Follow a consistent pattern for error messages: Problem + Solution',
          do: [
            'We couldn\'t save your file. Please check your connection and try again.',
            'This email address is already in use. Try signing in instead.',
            'Your password is too short. Use at least 8 characters.'
          ],
          dont: [
            'Error: Save failed',
            'Email exists',
            'Invalid password'
          ],
          examples: [
            {
              correct: 'We couldn\'t process your payment. Please check your card details and try again.',
              incorrect: 'Payment failed',
              explanation: 'Structured messages provide context and next steps'
            }
          ]
        }
      ],
      examples: [
        {
          title: 'Confirmation Messages',
          description: 'Clear confirmation of user actions',
          good: {
            text: 'Your message has been sent successfully. We\'ll get back to you within 24 hours.',
            context: 'Contact form submission'
          },
          bad: {
            text: 'Message sent',
            context: 'Generic confirmation'
          },
          explanation: 'Detailed confirmations set proper expectations',
          category: 'messaging'
        }
      ]
    };
  }

  /**
   * Create accessibility section
   */
  private createAccessibilitySection(): GuidelineSection {
    return {
      title: 'Accessibility',
      slug: 'accessibility',
      description: 'Guidelines for creating accessible content for all users',
      rules: [
        {
          title: 'Write descriptive link text',
          description: 'Link text should describe the destination or action, not just say "click here"',
          do: [
            'Read our privacy policy',
            'Download the user guide',
            'View your account settings'
          ],
          dont: [
            'Click here for privacy policy',
            'Download here',
            'Click to view settings'
          ],
          examples: [
            {
              correct: 'Learn more about our security features',
              incorrect: 'Click here to learn more',
              explanation: 'Descriptive links help screen reader users understand the destination'
            }
          ]
        }
      ],
      examples: [
        {
          title: 'Form Labels',
          description: 'Clear, descriptive form labels',
          good: {
            text: 'Email address (required)',
            context: 'Form input label'
          },
          bad: {
            text: 'Email*',
            context: 'Unclear form label'
          },
          explanation: 'Clear labels help all users understand what information is needed',
          category: 'accessibility'
        }
      ]
    };
  }

  /**
   * Create localization section
   */
  private createLocalizationSection(): GuidelineSection {
    return {
      title: 'Localization',
      slug: 'localization',
      description: 'Guidelines for creating content that works across different languages and cultures',
      rules: [
        {
          title: 'Avoid cultural references',
          description: 'Use universal concepts that translate well across cultures',
          do: [
            'Save your work',
            'Complete your profile',
            'Choose your preferences'
          ],
          dont: [
            'Hit a home run with your profile',
            'Don\'t throw in the towel',
            'That\'s the cherry on top'
          ],
          examples: [
            {
              correct: 'Your account is ready',
              incorrect: 'You\'re all set to rock and roll',
              explanation: 'Universal language translates better across cultures'
            }
          ]
        }
      ],
      examples: [
        {
          title: 'Universal Icons',
          description: 'Icons with universal meaning',
          good: {
            text: 'Save (with floppy disk icon)',
            context: 'Universal save concept'
          },
          bad: {
            text: 'Thumbs up for approval',
            context: 'Culture-specific gesture'
          },
          explanation: 'Universal symbols work across different cultures',
          category: 'localization'
        }
      ]
    };
  }  /**

   * Create tone guidelines
   */
  private createToneGuidelines(brandPersonality?: BrandPersonality): ToneGuidelines {
    const defaultPersonality: BrandPersonality = {
      helpful: true,
      friendly: true,
      professional: true,
      encouraging: true,
      clear: true
    };

    const personality = { ...defaultPersonality, ...brandPersonality };

    return {
      principles: this.createTonePrinciples(personality),
      contexts: this.createToneContexts(),
      examples: this.createToneExamples(),
      voice: this.createVoiceCharacteristics(personality)
    };
  }

  /**
   * Create tone principles
   */
  private createTonePrinciples(personality: BrandPersonality): TonePrinciple[] {
    const principles: TonePrinciple[] = [];

    if (personality.helpful) {
      principles.push({
        name: 'Helpful',
        description: 'Always aim to help users accomplish their goals',
        application: ['Error messages', 'Instructions', 'Guidance'],
        examples: [
          'Here\'s how to fix this issue',
          'Let me help you get started',
          'Try these steps to resolve the problem'
        ]
      });
    }

    if (personality.friendly) {
      principles.push({
        name: 'Friendly',
        description: 'Use warm, welcoming language that makes users feel comfortable',
        application: ['Welcome messages', 'Onboarding', 'General communication'],
        examples: [
          'Welcome! We\'re excited to have you here',
          'Thanks for joining us',
          'Great choice! Let\'s get started'
        ]
      });
    }

    return principles;
  }

  /**
   * Create tone contexts
   */
  private createToneContexts(): ToneContext[] {
    return [
      {
        context: 'Error messages',
        tone: 'Helpful and reassuring',
        characteristics: ['Acknowledge the problem', 'Provide solutions', 'Stay positive'],
        examples: [
          'We couldn\'t save your changes. Please check your connection and try again.',
          'This email address is already in use. Try signing in instead.'
        ],
        avoid: ['Blame the user', 'Use technical jargon', 'Sound frustrated']
      },
      {
        context: 'Success messages',
        tone: 'Encouraging and positive',
        characteristics: ['Celebrate achievements', 'Acknowledge progress', 'Set expectations'],
        examples: [
          'Perfect! Your account is all set up.',
          'Great job! Your profile is now complete.'
        ],
        avoid: ['Be overly enthusiastic', 'Use generic language', 'Miss the opportunity to guide next steps']
      }
    ];
  }

  /**
   * Create tone examples
   */
  private createToneExamples(): ToneExample[] {
    return [
      {
        context: 'File upload error',
        good: 'We couldn\'t upload your file. It might be too large or in an unsupported format. Try a smaller file or different format.',
        bad: 'Upload failed. Error code: 413.',
        explanation: 'The good example explains the problem and suggests solutions, while the bad example just states the error.'
      },
      {
        context: 'Account creation success',
        good: 'Welcome aboard! Your account is ready. Let\'s start by setting up your profile.',
        bad: 'Account created successfully.',
        explanation: 'The good example celebrates the achievement and guides the next step, while the bad example is generic and unhelpful.'
      }
    ];
  }

  /**
   * Create voice characteristics
   */
  private createVoiceCharacteristics(personality: BrandPersonality): VoiceCharacteristics {
    return {
      personality: Object.entries(personality)
        .filter(([_, value]) => value)
        .map(([key, _]) => key.charAt(0).toUpperCase() + key.slice(1)),
      values: [
        'User-focused',
        'Accessible',
        'Trustworthy',
        'Inclusive',
        'Empowering'
      ],
      style: [
        'Conversational but professional',
        'Active voice',
        'Sentence case',
        'Concise and clear',
        'Solution-oriented'
      ],
      avoid: [
        'Technical jargon',
        'Passive voice',
        'Blame or frustration',
        'Cultural references',
        'Overly casual slang'
      ]
    };
  }  /*
*
   * Create terminology standards
   */
  private createTerminologyStandards(customTerms?: TermEntry[]): TerminologyStandards {
    const standardTerms: TermEntry[] = [
      {
        term: 'sign in',
        definition: 'The process of accessing an account with credentials',
        category: 'authentication',
        usage: ['Login forms', 'Navigation', 'Help text'],
        alternatives: ['log in', 'login'],
        avoid: ['log into', 'sign into'],
        context: ['Use consistently throughout the application']
      },
      {
        term: 'account',
        definition: 'A user\'s personal space and settings in the application',
        category: 'user-management',
        usage: ['Settings', 'Profile pages', 'Navigation'],
        alternatives: ['profile', 'user'],
        avoid: ['user account', 'user profile'],
        context: ['Prefer "account" over "profile" for consistency']
      },
      {
        term: 'settings',
        definition: 'Configurable options and preferences',
        category: 'configuration',
        usage: ['Navigation', 'Configuration pages', 'Help text'],
        alternatives: ['preferences', 'options', 'configuration'],
        avoid: ['configs', 'prefs'],
        context: ['Use "settings" consistently across all configuration areas']
      }
    ];

    const allTerms = customTerms ? [...standardTerms, ...customTerms] : standardTerms;

    return {
      dictionary: allTerms,
      categories: this.createTermCategories(allTerms),
      rules: this.createTerminologyRules(),
      consistency: this.createConsistencyChecks()
    };
  }

  /**
   * Create term categories
   */
  private createTermCategories(terms: TermEntry[]): TermCategory[] {
    const categories = [...new Set(terms.map(term => term.category))];
    
    return categories.map(category => ({
      name: category,
      description: this.getCategoryDescription(category),
      terms: terms.filter(term => term.category === category).map(term => term.term),
      rules: [`Use consistent terminology within ${category} context`]
    }));
  }

  /**
   * Get category description
   */
  private getCategoryDescription(category: string): string {
    const descriptions: Record<string, string> = {
      'authentication': 'Terms related to user authentication and access',
      'user-management': 'Terms related to user accounts and profiles',
      'configuration': 'Terms related to settings and preferences',
      'actions': 'Terms for user actions and operations',
      'navigation': 'Terms used in navigation and wayfinding',
      'content': 'Terms related to content and media',
      'feedback': 'Terms for system feedback and notifications'
    };
    
    return descriptions[category] || `Terms related to ${category}`;
  }

  /**
   * Create terminology rules
   */
  private createTerminologyRules(): any[] {
    return [
      {
        rule: 'Use the same term consistently',
        description: 'Once you choose a term for a concept, use it everywhere',
        examples: ['Always use "sign in", never mix with "log in"'],
        exceptions: ['Context-specific variations may be acceptable']
      },
      {
        rule: 'Prefer simple over complex terms',
        description: 'Choose terms that users understand easily',
        examples: ['Use "delete" instead of "remove from system"'],
        exceptions: ['Technical contexts may require specific terminology']
      }
    ];
  }

  /**
   * Create consistency checks
   */
  private createConsistencyChecks(): any[] {
    return [
      {
        pattern: 'sign in vs log in',
        description: 'Check for consistent use of authentication terminology',
        correct: 'sign in',
        incorrect: ['log in', 'login', 'log into', 'sign into']
      },
      {
        pattern: 'account vs profile',
        description: 'Check for consistent use of user space terminology',
        correct: 'account',
        incorrect: ['profile', 'user account', 'user profile']
      }
    ];
  }  /**

   * Create messaging patterns
   */
  private createMessagingPatterns(customContexts?: MessageContext[]): MessagingPatterns {
    const standardPatterns: MessagePattern[] = [
      {
        name: 'Error message',
        description: 'Pattern for error messages: Problem + Solution',
        structure: '[Problem description]. [Suggested solution].',
        examples: [
          'We couldn\'t save your file. Please check your connection and try again.',
          'This email is already in use. Try signing in instead.'
        ],
        usage: ['Form validation', 'System errors', 'Network issues']
      },
      {
        name: 'Success message',
        description: 'Pattern for success messages: Celebration + Next step',
        structure: '[Positive acknowledgment]! [What happened]. [Optional next step].',
        examples: [
          'Great! Your account is ready. Let\'s set up your profile.',
          'Perfect! Your changes have been saved.'
        ],
        usage: ['Form submissions', 'Task completion', 'Achievements']
      }
    ];

    const standardContexts: MessageContext[] = [
      {
        context: 'Form validation',
        tone: 'Helpful and instructive',
        patterns: ['Error message'],
        examples: [
          {
            situation: 'Required field empty',
            message: 'Please enter your email address to continue.',
            explanation: 'Clear instruction about what\'s needed'
          },
          {
            situation: 'Invalid format',
            message: 'Please enter a valid email address (like <EMAIL>).',
            explanation: 'Explains the problem and shows the expected format'
          }
        ]
      }
    ];

    const allContexts = customContexts ? [...standardContexts, ...customContexts] : standardContexts;

    return {
      patterns: standardPatterns,
      contexts: allContexts,
      templates: this.createMessageTemplates(),
      validation: this.createMessageValidation()
    };
  }

  /**
   * Create message templates
   */
  private createMessageTemplates(): MessageTemplate[] {
    return [
      {
        name: 'Form error',
        template: '{field} {problem}. {solution}.',
        variables: [
          {
            name: 'field',
            type: 'string',
            description: 'The field name or description',
            examples: ['Email address', 'Password', 'Phone number']
          },
          {
            name: 'problem',
            type: 'string',
            description: 'Description of the problem',
            examples: ['is required', 'is too short', 'is not valid']
          },
          {
            name: 'solution',
            type: 'string',
            description: 'Suggested solution',
            examples: ['Please enter your email', 'Use at least 8 characters', 'Check the format']
          }
        ],
        examples: [
          'Email address is required. Please enter your email.',
          'Password is too short. Use at least 8 characters.'
        ],
        usage: 'Form validation errors'
      }
    ];
  }

  /**
   * Create message validation rules
   */
  private createMessageValidation(): any[] {
    return [
      {
        rule: 'Error messages must provide solutions',
        description: 'Every error message should suggest how to fix the problem',
        pattern: '.*\\. (Please|Try|Check).*',
        examples: [
          {
            input: 'We couldn\'t save your file. Please check your connection.',
            valid: true,
            explanation: 'Provides a solution after stating the problem'
          },
          {
            input: 'Save failed.',
            valid: false,
            explanation: 'States problem but provides no solution',
            correction: 'We couldn\'t save your file. Please check your connection and try again.'
          }
        ]
      }
    ];
  }

  /**
   * Create guideline examples
   */
  private createGuidelineExamples(): GuidelineExample[] {
    return [
      {
        title: 'Error Message Structure',
        description: 'How to structure helpful error messages',
        good: {
          text: 'We couldn\'t process your payment. Please check your card details and try again.',
          context: 'Payment form error'
        },
        bad: {
          text: 'Payment failed.',
          context: 'Generic error message'
        },
        explanation: 'Good error messages explain the problem and provide actionable solutions',
        category: 'messaging'
      },
      {
        title: 'Button Text Clarity',
        description: 'Clear, action-oriented button text',
        good: {
          text: 'Save and continue',
          context: 'Form submission button'
        },
        bad: {
          text: 'Submit',
          context: 'Generic button text'
        },
        explanation: 'Specific action words help users understand what will happen',
        category: 'writing-style'
      }
    ];
  }  
/**
   * Generate Markdown documentation
   */
  private async generateMarkdownDocs(guidelines: StyleGuidelines): Promise<void> {
    await fs.mkdir(this.outputDir, { recursive: true });

    // Generate main guidelines document
    const mainDoc = this.generateMainMarkdown(guidelines);
    await fs.writeFile(path.join(this.outputDir, 'README.md'), mainDoc, 'utf-8');

    // Generate section-specific documents
    for (const section of guidelines.sections) {
      const sectionDoc = this.generateSectionMarkdown(section);
      await fs.writeFile(path.join(this.outputDir, `${section.slug}.md`), sectionDoc, 'utf-8');
    }

    // Generate tone and voice guide
    const toneDoc = this.generateToneMarkdown(guidelines.toneAndVoice);
    await fs.writeFile(path.join(this.outputDir, 'tone-and-voice.md'), toneDoc, 'utf-8');

    // Generate terminology guide
    const terminologyDoc = this.generateTerminologyMarkdown(guidelines.terminology);
    await fs.writeFile(path.join(this.outputDir, 'terminology.md'), terminologyDoc, 'utf-8');

    // Generate messaging patterns guide
    const messagingDoc = this.generateMessagingMarkdown(guidelines.messaging);
    await fs.writeFile(path.join(this.outputDir, 'messaging-patterns.md'), messagingDoc, 'utf-8');
  }

  /**
   * Generate main guidelines markdown
   */
  private generateMainMarkdown(guidelines: StyleGuidelines): string {
    return `# Style Guidelines

Comprehensive guidelines for consistent content and communication across the design system.

## Sections

${guidelines.sections.map(section => `
### [${section.title}](${section.slug}.md)

${section.description}

**Rules:** ${section.rules.length} | **Examples:** ${section.examples.length}
`).join('')}

## Key Resources

- **[Tone and Voice](tone-and-voice.md)** - Guidelines for consistent communication tone
- **[Terminology](terminology.md)** - Standard terms and definitions
- **[Messaging Patterns](messaging-patterns.md)** - Templates for common message types

## Quick Reference

| Category | Key Principle | Example |
|----------|---------------|---------|
| Writing Style | Use active voice | "Click Save" not "Save can be clicked" |
| Tone | Be helpful and supportive | "Here's how to fix this" not "Error occurred" |
| Terminology | Use consistent terms | Always "sign in", never "log in" |
| Messaging | Provide solutions | "Problem + Solution" pattern |

## Voice Characteristics

${guidelines.toneAndVoice.voice.personality.map(trait => `- ${trait}`).join('\n')}
`;
  }

  /**
   * Generate section markdown
   */
  private generateSectionMarkdown(section: GuidelineSection): string {
    return `# ${section.title}

${section.description}

## Rules

${section.rules.map(rule => `
### ${rule.title}

${rule.description}

#### ✅ Do

${rule.do.map(item => `- ${item}`).join('\n')}

#### ❌ Don't

${rule.dont.map(item => `- ${item}`).join('\n')}

#### Examples

${rule.examples.map(example => `
**✅ Correct:** ${example.correct}

**❌ Incorrect:** ${example.incorrect}

*${example.explanation}*
`).join('')}
`).join('')}

## Examples

${section.examples.map(example => `
### ${example.title}

${example.description}

**✅ Good:**
> ${example.good.text}
> 
> *Context: ${example.good.context}*

${example.bad ? `
**❌ Bad:**
> ${example.bad.text}
> 
> *Context: ${example.bad.context}*
` : ''}

**Explanation:** ${example.explanation}
`).join('')}
`;
  } 
 /**
   * Generate tone markdown
   */
  private generateToneMarkdown(tone: ToneGuidelines): string {
    return `# Tone and Voice Guidelines

## Voice Characteristics

Our voice is: ${tone.voice.personality.join(', ')}

### Personality Traits
${tone.voice.personality.map(trait => `- **${trait}**`).join('\n')}

### Values
${tone.voice.values.map(value => `- ${value}`).join('\n')}

### Style Guidelines
${tone.voice.style.map(style => `- ${style}`).join('\n')}

### What to Avoid
${tone.voice.avoid.map(avoid => `- ${avoid}`).join('\n')}

## Tone Principles

${tone.principles.map(principle => `
### ${principle.name}

${principle.description}

**Apply to:** ${principle.application.join(', ')}

**Examples:**
${principle.examples.map(example => `- "${example}"`).join('\n')}
`).join('')}

## Contextual Tone

${tone.contexts.map(context => `
### ${context.context}

**Tone:** ${context.tone}

**Characteristics:**
${context.characteristics.map(char => `- ${char}`).join('\n')}

**Examples:**
${context.examples.map(example => `- "${example}"`).join('\n')}

**Avoid:**
${context.avoid.map(avoid => `- ${avoid}`).join('\n')}
`).join('')}

## Tone Examples

${tone.examples.map(example => `
### ${example.context}

**✅ Good tone:**
> ${example.good}

**❌ Poor tone:**
> ${example.bad}

**Why it works:** ${example.explanation}
`).join('')}
`;
  }

  /**
   * Generate terminology markdown
   */
  private generateTerminologyMarkdown(terminology: TerminologyStandards): string {
    return `# Terminology Standards

## Dictionary

${terminology.dictionary.map(term => `
### ${term.term}

**Definition:** ${term.definition}

**Category:** ${term.category}

**Usage:** ${term.usage.join(', ')}

**Alternatives:** ${term.alternatives.join(', ')}

**Avoid:** ${term.avoid.join(', ')}

**Context:** ${term.context.join(', ')}
`).join('')}

## Categories

${terminology.categories.map(category => `
### ${category.name}

${category.description}

**Terms:** ${category.terms.join(', ')}

**Rules:**
${category.rules.map(rule => `- ${rule}`).join('\n')}
`).join('')}

## Consistency Checks

${terminology.consistency.map(check => `
### ${check.pattern}

${check.description}

**✅ Use:** ${check.correct}

**❌ Avoid:** ${check.incorrect.join(', ')}
`).join('')}
`;
  }

  /**
   * Generate messaging markdown
   */
  private generateMessagingMarkdown(messaging: MessagingPatterns): string {
    return `# Messaging Patterns

## Patterns

${messaging.patterns.map(pattern => `
### ${pattern.name}

${pattern.description}

**Structure:** ${pattern.structure}

**Usage:** ${pattern.usage.join(', ')}

**Examples:**
${pattern.examples.map(example => `- "${example}"`).join('\n')}
`).join('')}

## Contextual Messaging

${messaging.contexts.map(context => `
### ${context.context}

**Tone:** ${context.tone}

**Patterns:** ${context.patterns.join(', ')}

**Examples:**
${context.examples.map(example => `
- **${example.situation}:** "${example.message}"
  - *${example.explanation}*
`).join('')}
`).join('')}

## Message Templates

${messaging.templates.map(template => `
### ${template.name}

**Template:** \`${template.template}\`

**Usage:** ${template.usage}

**Variables:**
${template.variables.map(variable => `
- **${variable.name}** (${variable.type}): ${variable.description}
  - Examples: ${variable.examples.join(', ')}
`).join('')}

**Examples:**
${template.examples.map(example => `- "${example}"`).join('\n')}
`).join('')}
`;
  }

  /**
   * Generate JSON documentation
   */
  private async generateJSONDocs(guidelines: StyleGuidelines): Promise<void> {
    const jsonPath = path.join(this.outputDir, 'guidelines.json');
    await fs.writeFile(jsonPath, JSON.stringify(guidelines, null, 2), 'utf-8');
  }

  /**
   * Generate validation rules for linting
   */
  private async generateValidationRules(guidelines: StyleGuidelines): Promise<void> {
    const rules = {
      terminology: guidelines.terminology.consistency.map(check => ({
        pattern: check.pattern,
        correct: check.correct,
        incorrect: check.incorrect
      })),
      messaging: guidelines.messaging.validation.map(rule => ({
        rule: rule.rule,
        pattern: rule.pattern,
        description: rule.description
      }))
    };

    await fs.writeFile(
      path.join(this.outputDir, 'validation-rules.json'),
      JSON.stringify(rules, null, 2),
      'utf-8'
    );
  }

  /**
   * Generate linting configuration
   */
  private async generateLintingConfig(guidelines: StyleGuidelines): Promise<void> {
    const config = {
      rules: {
        'consistent-terminology': {
          level: 'error',
          terms: guidelines.terminology.dictionary.reduce((acc, term) => {
            acc[term.term] = {
              alternatives: term.alternatives,
              avoid: term.avoid
            };
            return acc;
          }, {} as Record<string, any>)
        },
        'message-structure': {
          level: 'warning',
          patterns: guidelines.messaging.patterns.map(pattern => ({
            name: pattern.name,
            structure: pattern.structure
          }))
        }
      }
    };

    await fs.writeFile(
      path.join(this.outputDir, 'lint-config.json'),
      JSON.stringify(config, null, 2),
      'utf-8'
    );
  }
}

// Supporting interfaces
interface StyleGuidelineOptions {
  brandPersonality?: BrandPersonality;
  customTerms?: TermEntry[];
  messageContexts?: MessageContext[];
}

interface BrandPersonality {
  helpful?: boolean;
  friendly?: boolean;
  professional?: boolean;
  encouraging?: boolean;
  clear?: boolean;
}