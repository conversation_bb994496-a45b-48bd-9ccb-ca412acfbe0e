import { describe, it, expect, beforeEach, vi } from 'vitest';
import { CICDIntegration } from '../cicd-integration';
import { ConsistencyMonitor } from '../consistency-monitor';
import { MonitoringConfig } from '../types';
import * as fs from 'fs/promises';

// Mock dependencies
vi.mock('fs/promises');
vi.mock('../consistency-monitor');

describe('CICDIntegration', () => {
  let cicdIntegration: CICDIntegration;
  let mockMonitor: vi.Mocked<ConsistencyMonitor>;
  let config: MonitoringConfig;

  beforeEach(() => {
    config = {
      enabled: true,
      checkInterval: 5000,
      thresholds: {
        errorCount: 10,
        warningCount: 20,
        criticalSeverityLimit: 2
      },
      notifications: {},
      cicd: {
        failOnErrors: true,
        failOnWarnings: false,
        generateReports: true,
        reportPath: './reports'
      }
    };

    mockMonitor = {
      performCheck: vi.fn()
    } as any;

    cicdIntegration = new CICDIntegration(mockMonitor, config);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('runComplianceCheck', () => {
    it('should pass when no issues found', async () => {
      mockMonitor.performCheck.mockResolvedValue({
        timestamp: new Date(),
        newIssues: [],
        resolvedIssues: [],
        totalIssues: 0,
        severityBreakdown: { low: 0, medium: 0, high: 0, critical: 0 },
        complianceScore: 100,
        trend: 'stable'
      });

      const result = await cicdIntegration.runComplianceCheck();

      expect(result.success).toBe(true);
      expect(result.exitCode).toBe(0);
      expect(result.summary.totalIssues).toBe(0);
    });

    it('should fail when failOnErrors is true and issues exist', async () => {
      mockMonitor.performCheck.mockResolvedValue({
        timestamp: new Date(),
        newIssues: [],
        resolvedIssues: [],
        totalIssues: 5,
        severityBreakdown: { low: 3, medium: 2, high: 0, critical: 0 },
        complianceScore: 80,
        trend: 'stable'
      });

      const result = await cicdIntegration.runComplianceCheck();

      expect(result.success).toBe(false);
      expect(result.exitCode).toBe(1);
      expect(result.summary.totalIssues).toBe(5);
    });

    it('should fail when failOnWarnings is true and high/critical issues exist', async () => {
      config.cicd.failOnWarnings = true;
      cicdIntegration = new CICDIntegration(mockMonitor, config);

      mockMonitor.performCheck.mockResolvedValue({
        timestamp: new Date(),
        newIssues: [],
        resolvedIssues: [],
        totalIssues: 2,
        severityBreakdown: { low: 0, medium: 0, high: 1, critical: 1 },
        complianceScore: 70,
        trend: 'stable'
      });

      const result = await cicdIntegration.runComplianceCheck();

      expect(result.success).toBe(false);
      expect(result.exitCode).toBe(1);
      expect(result.summary.criticalIssues).toBe(1);
      expect(result.summary.highIssues).toBe(1);
    });

    it('should generate reports when configured', async () => {
      mockMonitor.performCheck.mockResolvedValue({
        timestamp: new Date(),
        newIssues: [{
          id: '1',
          type: 'color-issue',
          severity: 'medium',
          category: 'visual',
          description: 'Test issue',
          location: { filePath: 'test.tsx', lineNumber: 10, columnNumber: 5 },
          hash: 'hash1'
        }],
        resolvedIssues: [],
        totalIssues: 1,
        severityBreakdown: { low: 0, medium: 1, high: 0, critical: 0 },
        complianceScore: 90,
        trend: 'stable'
      });

      // Mock fs operations
      (fs.mkdir as any).mockResolvedValue(undefined);
      (fs.writeFile as any).mockResolvedValue(undefined);

      const result = await cicdIntegration.runComplianceCheck();

      expect(result.reportPath).toBeDefined();
      expect(fs.mkdir).toHaveBeenCalled();
      expect(fs.writeFile).toHaveBeenCalledTimes(2); // JSON and HTML reports
    });

    it('should handle errors gracefully', async () => {
      mockMonitor.performCheck.mockRejectedValue(new Error('Test error'));

      const result = await cicdIntegration.runComplianceCheck();

      expect(result.success).toBe(false);
      expect(result.exitCode).toBe(2);
      expect(result.message).toContain('Test error');
    });
  });

  describe('createGitHubActionsOutput', () => {
    it('should create GitHub Actions outputs when in GitHub Actions environment', () => {
      const originalEnv = process.env.GITHUB_ACTIONS;
      process.env.GITHUB_ACTIONS = 'true';

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      const result = {
        success: true,
        exitCode: 0,
        message: 'Test passed',
        summary: {
          totalIssues: 0,
          criticalIssues: 0,
          highIssues: 0,
          complianceScore: 100
        }
      };

      cicdIntegration.createGitHubActionsOutput(result);

      expect(consoleSpy).toHaveBeenCalledWith('::set-output name=success::true');
      expect(consoleSpy).toHaveBeenCalledWith('::set-output name=total-issues::0');
      expect(consoleSpy).toHaveBeenCalledWith('::set-output name=compliance-score::100');

      consoleSpy.mockRestore();
      process.env.GITHUB_ACTIONS = originalEnv;
    });
  });

  describe('generateCICDConfigs', () => {
    it('should generate CI/CD configuration templates', () => {
      const configs = CICDIntegration.generateCICDConfigs();

      expect(configs['github-actions']).toContain('Design System Compliance');
      expect(configs['gitlab-ci']).toContain('design-system-check');
      expect(configs['jenkins']).toContain('Design System Check');
    });
  });

  describe('status message creation', () => {
    it('should create appropriate status messages', async () => {
      mockMonitor.performCheck.mockResolvedValue({
        timestamp: new Date(),
        newIssues: [],
        resolvedIssues: [],
        totalIssues: 5,
        severityBreakdown: { low: 3, medium: 1, high: 1, critical: 0 },
        complianceScore: 85,
        trend: 'improving'
      });

      const result = await cicdIntegration.runComplianceCheck();

      expect(result.message).toContain('Total Issues: 5');
      expect(result.message).toContain('Critical: 0, High: 1');
      expect(result.message).toContain('Compliance Score: 85%');
    });
  });
});