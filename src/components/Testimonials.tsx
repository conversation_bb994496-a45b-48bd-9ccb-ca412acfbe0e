
// SECTION: Imports
// ==================================================================================
import { cn } from "@/lib/utils";
import { useEffect, useRef } from "react";
import styles from "./TestimonialCard.module.css";
import { getResponsiveTextSize, getResponsivePadding, getTouchTargetClass } from "@/utils/responsive-utilities";

// SECTION: Interface Definition
// ==================================================================================
interface TestimonialProps {
  quote: string;
  name: string;
  title: string;
  image?: string;
}

// SECTION: TestimonialCard Component
// ==================================================================================
const TestimonialCard = ({ quote, name, title, image }: TestimonialProps) => {
  // --- Component JSX ---
  return (
    <div className={cn(
      "bg-white rounded-lg shadow-lg flex flex-col h-full",
      getResponsivePadding("6", "8", "8")
    )}>
      <div className="mb-6 flex-grow">
        <svg className="text-elev-navy h-6 w-6 md:h-8 md:w-8 mb-2" fill="currentColor" viewBox="0 0 32 32" aria-hidden="true">
          <path d="M10 8v8h6c0 3.314-2.686 6-6 6v2c4.418 0 8-3.582 8-8v-8h-8zM22 8v8h6c0 3.314-2.686 6-6 6v2c4.418 0 8-3.582 8-8v-8h-8z" />
        </svg>
        <p className={cn(
          "text-elev-text-secondary italic mb-4 leading-relaxed",
          getResponsiveTextSize("sm", "base", "base")
        )}>{quote}</p>
      </div>

      <div className="mt-auto flex items-center">
        <div 
          className={image ? styles.testimonialImage : styles.testimonialImageInitials}
          style={image ? { '--testimonial-image': `url(${image})` } as React.CSSProperties : undefined}
          aria-hidden="true"
        >
          {!image && name.charAt(0)}
        </div>
        <div>
          <p className={cn(
            "font-bold text-elev-navy",
            getResponsiveTextSize("sm", "base", "base")
          )}>{name}</p>
          <p className={cn(
            "text-elev-text-secondary",
            getResponsiveTextSize("xs", "sm", "sm")
          )}>{title}</p>
        </div>
      </div>
    </div>
  );
};

// SECTION: Testimonials Component (Main)
// ==================================================================================
const Testimonials = () => {
  // --- Hooks ---
  const sectionRef = useRef<HTMLDivElement>(null);
  const testimonialsRef = useRef<HTMLDivElement>(null);

  // Effect for reveal animations
  useEffect(() => {
    const currentSectionRef = sectionRef.current;
    const currentTestimonialsRef = testimonialsRef.current;
    
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("active");
          }
        });
      },
      { threshold: 0.1 }
    );

    if (currentSectionRef) observer.observe(currentSectionRef);
    if (currentTestimonialsRef) observer.observe(currentTestimonialsRef);

    return () => {
      if (currentSectionRef) observer.unobserve(currentSectionRef);
      if (currentTestimonialsRef) observer.unobserve(currentTestimonialsRef);
    };
  }, []);

  // --- Data ---
  const testimonials = [
    {
      quote: "702HOCKEY is the real deal! My son has been training here for 6 months and his skating has improved tremendously. The coaches are knowledgeable and patient, and the facility is top-notch with year-round ice. As a hockey parent in Vegas, having access to this level of training locally is a game-changer. ⭐⭐⭐⭐⭐",
      name: "Mike R.",
      title: "Las Vegas, NV",
    },
    {
      quote: "I've been bringing my daughter here for the past year and I can't say enough good things about 702HOCKEY. The coaches truly care about each player's development and create personalized training plans. The small group sessions ensure everyone gets attention. Worth every penny for serious hockey players in Vegas! ⭐⭐⭐⭐⭐",
      name: "Sarah K.",
      title: "Henderson, NV",
    },
    {
      quote: "As a coach myself, I'm extremely impressed with the training methodology at 702HOCKEY. They focus on proper technique and hockey IQ, not just drills. My team has shown remarkable improvement since we started training here. The custom ice surface is perfect for skills work and the staff is professional and knowledgeable. ⭐⭐⭐⭐⭐",
      name: "David M.",
      title: "North Las Vegas, NV",
    }
  ];

  // --- Component JSX ---
  return (
    <section className="section-padding bg-elev-background-muted" id="testimonials">
      <div className="container mx-auto px-4 md:px-6 lg:px-8">
        <div className="text-center mb-8 md:mb-12 reveal" ref={sectionRef}>
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-4 text-elev-navy px-4">
            Yelp Reviews
          </h2>
          <p className="text-base md:text-lg text-gray-600 max-w-3xl mx-auto px-4">
            Read what our customers are saying about us on Yelp. We're proud of our 5-star rating!
          </p>
        </div>

        <div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 reveal"
          ref={testimonialsRef}
        >
          {testimonials.map((testimonial, index) => (
            <TestimonialCard
              key={index}
              quote={testimonial.quote}
              name={testimonial.name}
              title={testimonial.title}
            />
          ))}
        </div>

        {/* Yelp Reviews Call-to-Action Section */}
        <div className="text-center mt-8 md:mt-12 reveal">
          <div className="bg-gray-50 rounded-lg p-6 md:p-8 max-w-2xl mx-auto">
            <h3 className="text-lg md:text-xl font-semibold mb-4 text-elev-navy">
              Want to see more reviews or share your experience?
            </h3>
            <p className="text-gray-600 mb-6 text-sm md:text-base">
              Check out all our reviews on Yelp or leave us a review to help other hockey families find us!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                 href="https://www.yelp.com/biz/elev802-vegas-las-vegas"
                 target="_blank"
                 rel="noopener noreferrer"
                 className="inline-flex items-center justify-center px-6 py-3 bg-elev-navy text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors duration-200"
               >
                 <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                   <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                 </svg>
                 Read More Reviews
               </a>
               <a
                 href="https://www.yelp.com/writeareview/biz/elev802-vegas-las-vegas"
                 target="_blank"
                 rel="noopener noreferrer"
                 className="inline-flex items-center justify-center px-6 py-3 bg-white text-elev-navy font-semibold rounded-lg border-2 border-elev-navy hover:bg-elev-navy hover:text-white transition-colors duration-200"
               >
                 <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                   <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                 </svg>
                 Leave a Review
               </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
