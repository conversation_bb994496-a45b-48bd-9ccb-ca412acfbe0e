// Verification script for audit infrastructure setup

import { DesignSystemAuditEngine } from './core/audit-engine';
import { FileScanner } from './core/file-scanner';
import { auditLogger, LogLevel } from './core/logger';
import { 
  Analyzer, 
  AnalyzerR<PERSON>ult, 
  FileInfo, 
  Severity, 
  IssueType, 
  Category 
} from './types';

// Mock analyzer for verification
class VerificationAnalyzer implements Analyzer {
  name = 'verification-analyzer';

  async analyze(files: FileInfo[]): Promise<AnalyzerResult> {
    console.log(`Analyzing ${files.length} files...`);
    
    return {
      analyzerName: this.name,
      issues: [
        {
          id: 'verify-issue-1',
          type: IssueType.COLOR_INCONSISTENCY,
          severity: Severity.MEDIUM,
          category: Category.VISUAL,
          description: 'Verification test issue',
          location: {
            filePath: 'src/components/test.tsx',
            lineNumber: 10,
            columnNumber: 5,
            context: 'const color = "#ff0000"'
          },
          suggestion: 'Use design token instead of hardcoded color',
          autoFixable: true,
          impact: {
            userExperience: 6,
            maintenanceEffort: 3,
            implementationComplexity: 2
          }
        }
      ],
      summary: {
        totalIssues: 1,
        severityBreakdown: {
          [Severity.LOW]: 0,
          [Severity.MEDIUM]: 1,
          [Severity.HIGH]: 0,
          [Severity.CRITICAL]: 0
        },
        categoryBreakdown: {
          [Category.VISUAL]: 1,
          [Category.CONTENT]: 0,
          [Category.COMPONENT]: 0,
          [Category.ACCESSIBILITY]: 0
        }
      },
      executionTime: 150
    };
  }

  getSeverity(): Severity {
    return Severity.MEDIUM;
  }
}

// Verification function
export async function verifyAuditSetup(): Promise<boolean> {
  console.log('🔍 Verifying audit infrastructure setup...\n');

  try {
    // Test 1: Logger functionality
    console.log('✅ Testing logger...');
    auditLogger.setLogLevel(LogLevel.DEBUG);
    auditLogger.info('Logger test successful');
    auditLogger.debug('Debug logging works');
    auditLogger.warn('Warning logging works');
    
    // Test 2: File scanner initialization
    console.log('✅ Testing file scanner...');
    const fileScanner = new FileScanner();
    console.log('File scanner initialized successfully');
    console.log('Supported file types:', fileScanner.getFileTypes());
    
    // Test 3: Audit engine initialization
    console.log('✅ Testing audit engine...');
    const auditEngine = new DesignSystemAuditEngine({
      projectPath: './src',
      logLevel: 'info'
    });
    console.log('Audit engine initialized successfully');
    
    // Test 4: Analyzer registration
    console.log('✅ Testing analyzer registration...');
    const verificationAnalyzer = new VerificationAnalyzer();
    auditEngine.registerAnalyzer(verificationAnalyzer);
    const registeredAnalyzers = auditEngine.getRegisteredAnalyzers();
    console.log('Registered analyzers:', registeredAnalyzers);
    
    // Test 5: Report generation
    console.log('✅ Testing report generation...');
    const mockResults: AnalyzerResult[] = [
      await verificationAnalyzer.analyze([])
    ];
    const report = auditEngine.generateReport(mockResults);
    console.log('Report generated successfully');
    console.log('Report summary:', {
      totalIssues: report.summary.totalIssues,
      recommendations: report.recommendations.length,
      tasks: report.remediationPlan.tasks.length
    });
    
    // Test 6: Configuration access
    console.log('✅ Testing configuration...');
    const config = auditEngine.getConfig();
    console.log('Configuration loaded:', {
      includePatterns: config.includePatterns.length,
      excludePatterns: config.excludePatterns.length,
      outputFormat: config.outputFormat
    });
    
    console.log('\n🎉 All audit infrastructure components verified successfully!');
    console.log('\nSetup Summary:');
    console.log('- ✅ Core types and interfaces defined');
    console.log('- ✅ Logging system operational');
    console.log('- ✅ File scanner ready');
    console.log('- ✅ Audit engine functional');
    console.log('- ✅ Analyzer registration working');
    console.log('- ✅ Report generation working');
    console.log('- ✅ Configuration management working');
    
    return true;
    
  } catch (error) {
    console.error('❌ Verification failed:', error);
    return false;
  }
}

// Run verification if this file is executed directly
if (typeof window === 'undefined') {
  verifyAuditSetup().then(success => {
    process.exit(success ? 0 : 1);
  });
}