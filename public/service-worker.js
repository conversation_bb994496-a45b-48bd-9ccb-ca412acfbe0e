// Service Worker for 702Hockey - handles caching and offline support

// Set this to false if you don't want to use precaching
const ENABLE_PRECACHING = true;

// Name of the cache to use for precaching
const PRECACHE = 'precache-v2'; // Incremented version

// List of URLs to precache
const PRECACHE_URLS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.ico',
  // Add other assets you want to precache here
];

// External domains that might cause CORS issues
const PROBLEMATIC_DOMAINS = [
  'cdn.gpteng.co',
  'doubleclick.net'
];

// Helper to check if a URL is from a problematic domain
const isProblematicUrl = (url) => {
  try {
    const urlObj = new URL(url);
    return PROBLEMATIC_DOMAINS.some(domain => urlObj.hostname.includes(domain));
  } catch (e) {
    return false;
  }
};

// Install event - cache all static assets with error handling
self.addEventListener('install', (event) => {
  if (ENABLE_PRECACHING) {
    event.waitUntil(
      caches.open(PRECACHE)
        .then((cache) => {
          // Use Promise.allSettled instead of Promise.all to handle individual failures
          return Promise.allSettled(
            PRECACHE_URLS.map(url => 
              cache.add(url).catch(error => {
                console.error(`Failed to cache ${url}:`, error);
                // Continue despite the error
                return Promise.resolve();
              })
            )
          );
        })
        .then(() => self.skipWaiting())
        .catch(error => {
          console.error('Precaching failed:', error);
          // Skip waiting even if precaching fails
          return self.skipWaiting();
        })
    );
  } else {
    self.skipWaiting();
  }
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  const currentCaches = [PRECACHE, 'api-cache', 'runtime-cache'];
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (!currentCaches.includes(cacheName)) {
              return caches.delete(cacheName);
            }
            return null;
          }).filter(Boolean)
        );
      })
      .then(() => self.clients.claim())
      .catch(error => {
        console.error('Cache cleanup failed:', error);
        // Claim clients even if cleanup fails
        return self.clients.claim();
      })
  );
});

// Fetch event - cache-first strategy for static assets, network-first for API calls
self.addEventListener('fetch', (event) => {
  // Skip non-GET requests
  if (event.request.method !== 'GET') {
    return;
  }

  // Skip browser extensions and problematic domains
  const url = event.request.url;
  if (
    url.startsWith('chrome-extension://') || 
    url.startsWith('moz-extension://') ||
    isProblematicUrl(url)
  ) {
    return; // Let the browser handle these requests normally
  }

  // Handle API requests with network-first strategy
  if (url.includes('/api/')) {
    event.respondWith(
      fetch(event.request)
        .then((response) => {
          if (!response || response.status !== 200) {
            throw new Error('Bad response status');
          }
          // Clone the response for potential caching
          const responseToCache = response.clone();
          caches.open('api-cache')
            .then((cache) => cache.put(event.request, responseToCache))
            .catch(err => console.error('API cache error:', err));
          return response;
        })
        .catch((error) => {
          console.log(`Network request failed for ${url}:`, error);
          // If network fails, try to get from cache
          return caches.match(event.request)
            .then(cachedResponse => {
              return cachedResponse || Promise.reject('No cached response available');
            });
        })
    );
    return;
  }

  // For all other requests, use cache-first strategy
  event.respondWith(
    caches.match(event.request)
      .then((cachedResponse) => {
        // Return cached response if found
        if (cachedResponse) {
          return cachedResponse;
        }

        // Clone the request for potential caching
        const fetchRequest = event.request.clone();

        // Make network request and cache the response
        return fetch(fetchRequest)
          .then((response) => {
            // Check if we received a valid response
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }

            // Clone the response for potential caching
            const responseToCache = response.clone();

            caches.open('runtime-cache')
              .then((cache) => cache.put(event.request, responseToCache))
              .catch(err => console.error('Runtime cache error:', err));

            return response;
          })
          .catch(error => {
            console.error(`Fetch failed for ${url}:`, error);
            return Promise.reject(`Failed to fetch: ${error.message}`);
          });
      })
      .catch(error => {
        console.error(`Cache match failed for ${url}:`, error);
        // If all else fails, return a fallback response or let the error propagate
        return new Response('Network and cache both failed', { status: 503, headers: { 'Content-Type': 'text/plain' } });
      })
  );
});

// Background sync for failed API requests
self.addEventListener('sync', (event) => {
  if (event.tag === 'sync-api-requests') {
    // Implement background sync logic here
    console.log('Background sync triggered:', event.tag);
  }
});

// Push notifications
self.addEventListener('push', (event) => {
  if (!(self.Notification && self.Notification.permission === 'granted')) {
    return;
  }

  try {
    const data = event.data ? event.data.json() : {};
    const title = data?.title || 'New Notification';
    const options = {
      body: data?.body || 'You have a new notification',
      icon: data?.icon || '/icons/icon-192x192.png',
      badge: '/icons/icon-96x96.png',
      data: data?.data || {},
    };

    event.waitUntil(self.registration.showNotification(title, options));
  } catch (error) {
    console.error('Push notification error:', error);
  }
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  try {
    const urlToOpen = new URL(event.notification.data?.url || '/', self.location.origin).href;

    event.waitUntil(
      clients.matchAll({ type: 'window' })
        .then((windowClients) => {
          // Check if there's already a window/tab open with the target URL
          const existingWindow = windowClients.find(
            (client) => client.url === urlToOpen && 'focus' in client
          );

          if (existingWindow) {
            // Focus the existing window
            return existingWindow.focus();
          }
          
          // Open a new window/tab if none exists
          if (clients.openWindow) {
            return clients.openWindow(urlToOpen);
          }
          
          return null;
        })
        .catch(error => {
          console.error('Notification click handling error:', error);
        })
    );
  } catch (error) {
    console.error('Notification URL processing error:', error);
  }
});
