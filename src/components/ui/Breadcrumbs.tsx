import React from 'react';
import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { ChevronRight, Home } from 'lucide-react';

export interface BreadcrumbItem {
  label: string;
  href: string;
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[];
  className?: string;
  showHomeIcon?: boolean;
  separator?: 'chevron' | 'slash';
}

/**
 * Enhanced Breadcrumbs component with improved accessibility and responsive design
 * Features:
 * - Semantic HTML structure with proper ARIA labels
 * - Keyboard navigation support
 * - Mobile-responsive design with text truncation
 * - Consistent styling with design system
 * - Optional home icon and separator customization
 */
export const Breadcrumbs = React.forwardRef<HTMLElement, BreadcrumbsProps>(
  ({ items, className, showHomeIcon = false, separator = 'chevron' }, ref) => {
    const SeparatorIcon = separator === 'chevron' ? ChevronRight : null;
    
    return (
      <nav 
        ref={ref}
        aria-label="Breadcrumb navigation" 
        className={cn(
          "py-3 px-4 sm:px-6 bg-elev-background-muted/50 border-b border-elev-background-accent",
          "sticky top-0 z-10 backdrop-blur-sm", // Make breadcrumbs sticky for better UX
          className
        )}
      >
        <div className="container mx-auto max-w-7xl">
          <ol 
            className="flex flex-wrap items-center gap-1 sm:gap-2 text-sm sm:text-base"
            role="list"
          >
            {/* Home link */}
            <li className="flex items-center">
              <Link 
                to="/" 
                className={cn(
                  "flex items-center gap-1 px-2 py-1 rounded-md transition-all duration-200",
                  "text-elev-text-primary hover:text-elev-navy hover:bg-white/50",
                  "focus:outline-none focus:ring-2 focus:ring-elev-blue focus:ring-offset-2",
                  "min-h-[32px] min-w-[32px] justify-center sm:justify-start"
                )}
                aria-label="Go to homepage"
              >
                {showHomeIcon ? (
                  <>
                    <Home className="h-4 w-4" aria-hidden="true" />
                    <span className="hidden sm:inline">Home</span>
                  </>
                ) : (
                  "Home"
                )}
              </Link>
            </li>
            
            {/* Breadcrumb items */}
            {items.map((item, index) => (
              <li key={`${item.href}-${index}`} className="flex items-center">
                {/* Separator */}
                <div className="mx-1 sm:mx-2 text-elev-text-secondary" aria-hidden="true">
                  {separator === 'chevron' ? (
                    <ChevronRight className="h-4 w-4" />
                  ) : (
                    <span className="text-lg">/</span>
                  )}
                </div>
                
                {/* Breadcrumb link or current page */}
                {index === items.length - 1 ? (
                  <span 
                    className={cn(
                      "px-2 py-1 rounded-md font-medium text-elev-navy",
                      "bg-white/70 border border-elev-background-accent",
                      "max-w-[120px] sm:max-w-[200px] md:max-w-none truncate"
                    )}
                    aria-current="page"
                    title={item.label} // Tooltip for truncated text
                  >
                    {item.label}
                  </span>
                ) : (
                  <Link
                    to={item.href}
                    className={cn(
                      "px-2 py-1 rounded-md transition-all duration-200",
                      "text-elev-text-primary hover:text-elev-navy hover:bg-white/50",
                      "focus:outline-none focus:ring-2 focus:ring-elev-blue focus:ring-offset-2",
                      "max-w-[120px] sm:max-w-[200px] md:max-w-none truncate",
                      "min-h-[32px] flex items-center"
                    )}
                    title={item.label} // Tooltip for truncated text
                  >
                    {item.label}
                  </Link>
                )}
              </li>
            ))}
          </ol>
        </div>
      </nav>
    );
  }
);

Breadcrumbs.displayName = 'Breadcrumbs';
