import React, { useState, useCallback, useRef, useEffect, useMemo, ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';
import NavigationContext from './NavigationContext';

interface NavigationProviderProps {
  children: ReactNode;
}

const NavigationProvider = ({ children }: NavigationProviderProps) => {
  const [isNavigating, setIsNavigating] = useState(false);
  const [currentDestination, setCurrentDestination] = useState<string | null>(null);
  const navigate = useNavigate();
  
  // Refs to store timeout IDs
  const navigationTimeoutRef = useRef<number | null>(null);
  const resetTimeoutRef = useRef<number | null>(null);
  
  // Clean up timeouts when component unmounts
  useEffect(() => {
    return () => {
      // Clear any existing timeouts to prevent memory leaks
      if (navigationTimeoutRef.current !== null) {
        window.clearTimeout(navigationTimeoutRef.current);
      }
      if (resetTimeoutRef.current !== null) {
        window.clearTimeout(resetTimeoutRef.current);
      }
    };
  }, []);

  const navigateTo = useCallback((path: string, loadingTime = 500) => {
    // Clear any existing timeouts to prevent race conditions
    if (navigationTimeoutRef.current !== null) {
      window.clearTimeout(navigationTimeoutRef.current);
      navigationTimeoutRef.current = null;
    }
    if (resetTimeoutRef.current !== null) {
      window.clearTimeout(resetTimeoutRef.current);
      resetTimeoutRef.current = null;
    }
    
    setIsNavigating(true);
    setCurrentDestination(path);
    
    // Navigate after a minimum delay to ensure loading state is visible
    navigationTimeoutRef.current = window.setTimeout(() => {
      navigate(path);
      
      // Reset loading state after navigation
      resetTimeoutRef.current = window.setTimeout(() => {
        setIsNavigating(false);
        setCurrentDestination(null);
        resetTimeoutRef.current = null;
      }, 100); // Small delay to ensure loading state is reset after navigation
      
      navigationTimeoutRef.current = null;
    }, loadingTime);
  }, [navigate]);

  const ctx = useMemo(
    () => ({ isNavigating, navigateTo, currentDestination }),
    [isNavigating, navigateTo, currentDestination]
  );

  return (
    <NavigationContext.Provider value={ctx}>
      {children}
    </NavigationContext.Provider>
  );
};

export default NavigationProvider;
