/**
 * Visual Regression Validator
 * Validates visual consistency after remediation changes
 */

import { ValidationRule, ValidationViolation, Validator, ValidationResult, ValidationCategory, ValidationFile, VisualRegressionTest, RegressionResult, Viewport } from './types';
import { DesignTokenSystem } from '../design-tokens/types';
import { Severity } from '../types';

export class VisualRegressionValidator implements Validator {
  name = 'Visual Regression Validator';
  category = ValidationCategory.VISUAL_REGRESSION;
  rules: ValidationRule[] = [
    new ComponentRenderingRule(),
    new ResponsiveLayoutRule(),
    new ColorContrastRule(),
    new TypographyRenderingRule()
  ];

  private tests: VisualRegressionTest[] = [];
  private baselineDirectory = 'tests/visual-regression/baselines';
  private outputDirectory = 'tests/visual-regression/output';

  async validate(files: ValidationFile[], tokens?: DesignTokenSystem): Promise<ValidationResult> {
    const startTime = Date.now();
    const violations: ValidationViolation[] = [];

    // Generate tests for component files
    const componentFiles = files.filter(file => file.type === 'component' as any);
    this.generateTests(componentFiles);

    // Run visual regression tests
    const regressionResults = await this.runVisualTests();
    
    // Convert regression failures to violations
    for (const result of regressionResults) {
      if (!result.passed) {
        violations.push(this.createViolationFromResult(result));
      }
    }

    // Run additional validation rules
    for (const file of componentFiles) {
      for (const rule of this.rules) {
        const ruleViolations = rule.validate(file.content, file.path, tokens);
        violations.push(...ruleViolations);
      }
    }

    const executionTime = Date.now() - startTime;

    return {
      isValid: violations.length === 0,
      violations,
      summary: this.generateSummary(violations),
      executionTime
    };
  }

  private generateTests(componentFiles: ValidationFile[]): void {
    this.tests = [];
    
    const viewports: Viewport[] = [
      { width: 375, height: 667, deviceScaleFactor: 2 }, // Mobile
      { width: 768, height: 1024, deviceScaleFactor: 1 }, // Tablet
      { width: 1920, height: 1080, deviceScaleFactor: 1 } // Desktop
    ];

    componentFiles.forEach(file => {
      const componentName = this.extractComponentName(file.path);
      
      viewports.forEach((viewport, index) => {
        this.tests.push({
          id: `${componentName}-${viewport.width}x${viewport.height}`,
          name: `${componentName} at ${viewport.width}x${viewport.height}`,
          component: componentName,
          viewport,
          baseline: `${this.baselineDirectory}/${componentName}-${viewport.width}x${viewport.height}.png`,
          threshold: 0.1 // 10% difference threshold
        });
      });
    });
  }

  private async runVisualTests(): Promise<RegressionResult[]> {
    const results: RegressionResult[] = [];
    
    // This is a mock implementation - in a real scenario, you'd use tools like:
    // - Puppeteer/Playwright for screenshot generation
    // - Pixelmatch or similar for image comparison
    // - Jest or Vitest for test orchestration
    
    for (const test of this.tests) {
      try {
        // Mock screenshot capture and comparison
        const result = await this.mockVisualTest(test);
        results.push(result);
      } catch (error) {
        results.push({
          testId: test.id,
          passed: false,
          difference: 1.0,
          actualImage: '',
          expectedImage: test.baseline,
          diffImage: ''
        });
      }
    }
    
    return results;
  }

  private async mockVisualTest(test: VisualRegressionTest): Promise<RegressionResult> {
    // Mock implementation - replace with actual visual testing logic
    return {
      testId: test.id,
      passed: Math.random() > 0.1, // 90% pass rate for demo
      difference: Math.random() * 0.2, // Random difference up to 20%
      actualImage: `${this.outputDirectory}/${test.id}-actual.png`,
      expectedImage: test.baseline,
      diffImage: `${this.outputDirectory}/${test.id}-diff.png`
    };
  }

  private createViolationFromResult(result: RegressionResult): ValidationViolation {
    const test = this.tests.find(t => t.id === result.testId);
    
    return {
      id: `visual-regression-${result.testId}`,
      rule: 'visual-regression',
      severity: result.difference > 0.5 ? Severity.HIGH : Severity.MEDIUM,
      message: `Visual regression detected in ${test?.name || result.testId}`,
      location: {
        filePath: result.actualImage,
        lineNumber: 1,
        columnNumber: 1,
        context: `Visual test: ${test?.name || result.testId}`
      },
      actualValue: `${(result.difference * 100).toFixed(1)}% difference`,
      suggestion: 'Review visual changes and update baseline if intentional',
      autoFixable: false
    };
  }

  private extractComponentName(filePath: string): string {
    return filePath.split('/').pop()?.replace(/\.(tsx|jsx)$/, '') || 'Unknown';
  }

  private generateSummary(violations: ValidationViolation[]) {
    const severityBreakdown = violations.reduce((acc, v) => {
      acc[v.severity] = (acc[v.severity] || 0) + 1;
      return acc;
    }, {} as Record<Severity, number>);

    const ruleBreakdown = violations.reduce((acc, v) => {
      acc[v.rule] = (acc[v.rule] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const fileBreakdown = violations.reduce((acc, v) => {
      acc[v.location.filePath] = (acc[v.location.filePath] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalViolations: violations.length,
      severityBreakdown,
      ruleBreakdown,
      fileBreakdown
    };
  }
}

class ComponentRenderingRule implements ValidationRule {
  id = 'component-rendering';
  name = 'Component Rendering Consistency';
  description = 'Ensures components render consistently across different states';
  category = ValidationCategory.VISUAL_REGRESSION;
  severity = Severity.MEDIUM;

  validate(content: string, filePath: string): ValidationViolation[] {
    const violations: ValidationViolation[] = [];
    
    // Check for components that might have rendering issues
    const problematicPatterns = [
      { pattern: /style={{[^}]*undefined[^}]*}}/g, message: 'Undefined values in inline styles' },
      { pattern: /className={[^}]*undefined[^}]*}/g, message: 'Undefined values in className' },
      { pattern: /\?\?\s*''/g, message: 'Empty string fallback might cause layout issues' }
    ];

    const lines = content.split('\n');
    
    problematicPatterns.forEach(({ pattern, message }) => {
      lines.forEach((line, lineIndex) => {
        let match;
        while ((match = pattern.exec(line)) !== null) {
          violations.push({
            id: `${filePath}:${lineIndex + 1}:${match.index}:rendering-issue`,
            rule: this.id,
            severity: this.severity,
            message,
            location: {
              filePath,
              lineNumber: lineIndex + 1,
              columnNumber: match.index,
              context: line.trim()
            },
            actualValue: match[0],
            suggestion: 'Ensure all style and className values are properly defined',
            autoFixable: false
          });
        }
      });
    });

    return violations;
  }
}

class ResponsiveLayoutRule implements ValidationRule {
  id = 'responsive-layout';
  name = 'Responsive Layout Consistency';
  description = 'Ensures responsive layouts work consistently across breakpoints';
  category = ValidationCategory.VISUAL_REGRESSION;
  severity = Severity.MEDIUM;

  validate(content: string, filePath: string): ValidationViolation[] {
    const violations: ValidationViolation[] = [];
    
    // Check for responsive design patterns
    const hasResponsiveClasses = /(?:sm:|md:|lg:|xl:)/.test(content);
    const hasMediaQueries = /@media/.test(content);
    const hasFlexbox = /(?:flex|grid)/.test(content);
    
    if (!hasResponsiveClasses && !hasMediaQueries && content.includes('width') && content.includes('height')) {
      violations.push({
        id: `${filePath}:1:1:missing-responsive`,
        rule: this.id,
        severity: Severity.LOW,
        message: 'Component may not be responsive - consider adding responsive design patterns',
        location: {
          filePath,
          lineNumber: 1,
          columnNumber: 1,
          context: 'Component layout'
        },
        actualValue: 'Fixed dimensions detected',
        suggestion: 'Add responsive breakpoints or flexible layout patterns',
        autoFixable: false
      });
    }

    return violations;
  }
}

class ColorContrastRule implements ValidationRule {
  id = 'color-contrast';
  name = 'Color Contrast Validation';
  description = 'Ensures adequate color contrast for accessibility';
  category = ValidationCategory.VISUAL_REGRESSION;
  severity = Severity.HIGH;

  validate(content: string, filePath: string): ValidationViolation[] {
    const violations: ValidationViolation[] = [];
    
    // Look for potential contrast issues
    const lightColors = ['white', '#fff', '#ffffff', 'rgb(255,255,255)', 'hsl(0,0%,100%)'];
    const darkColors = ['black', '#000', '#000000', 'rgb(0,0,0)', 'hsl(0,0%,0%)'];
    
    const lines = content.split('\n');
    
    lines.forEach((line, lineIndex) => {
      // Check for light text on light background or dark text on dark background
      const hasLightColor = lightColors.some(color => line.includes(color));
      const hasDarkColor = darkColors.some(color => line.includes(color));
      
      if (hasLightColor && line.includes('color') && line.includes('background')) {
        violations.push({
          id: `${filePath}:${lineIndex + 1}:1:potential-contrast-issue`,
          rule: this.id,
          severity: this.severity,
          message: 'Potential color contrast issue detected',
          location: {
            filePath,
            lineNumber: lineIndex + 1,
            columnNumber: 1,
            context: line.trim()
          },
          actualValue: line.trim(),
          suggestion: 'Verify color contrast meets WCAG AA standards (4.5:1 ratio)',
          autoFixable: false
        });
      }
    });

    return violations;
  }
}

class TypographyRenderingRule implements ValidationRule {
  id = 'typography-rendering';
  name = 'Typography Rendering Consistency';
  description = 'Ensures typography renders consistently across different contexts';
  category = ValidationCategory.VISUAL_REGRESSION;
  severity = Severity.LOW;

  validate(content: string, filePath: string): ValidationViolation[] {
    const violations: ValidationViolation[] = [];
    
    // Check for typography issues that might cause rendering problems
    const typographyIssues = [
      { pattern: /font-size:\s*0/g, message: 'Zero font size will make text invisible' },
      { pattern: /line-height:\s*0/g, message: 'Zero line height may cause text overlap' },
      { pattern: /font-weight:\s*[0-9]{4,}/g, message: 'Invalid font weight value' }
    ];

    const lines = content.split('\n');
    
    typographyIssues.forEach(({ pattern, message }) => {
      lines.forEach((line, lineIndex) => {
        let match;
        while ((match = pattern.exec(line)) !== null) {
          violations.push({
            id: `${filePath}:${lineIndex + 1}:${match.index}:typography-rendering`,
            rule: this.id,
            severity: this.severity,
            message,
            location: {
              filePath,
              lineNumber: lineIndex + 1,
              columnNumber: match.index,
              context: line.trim()
            },
            actualValue: match[0],
            suggestion: 'Use valid typography values from design system',
            autoFixable: false
          });
        }
      });
    });

    return violations;
  }
}