import { ComponentType, lazy, LazyExoticComponent, ComponentProps } from 'react';

type PreloadableComponent<T extends ComponentType<unknown>> = LazyExoticComponent<T> & {
  preload: () => Promise<{ default: T }>;
};

/**
 * Creates a lazy-loaded component with preloading support
 */
function lazyWithPreload<T extends ComponentType<unknown>>(
  factory: () => Promise<{ default: T }>
): PreloadableComponent<T> {
  const Component = lazy(factory);
  (Component as PreloadableComponent<T>).preload = factory;
  return Component as PreloadableComponent<T>;
}

// Export the lazyWithPreload function
export { lazyWithPreload };
export type { PreloadableComponent };
