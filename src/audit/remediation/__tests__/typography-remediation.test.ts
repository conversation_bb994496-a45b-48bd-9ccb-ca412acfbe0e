import { describe, it, expect, beforeEach } from 'vitest';
import { TypographyRemediationEngine } from '../typography-remediation';
import { DesignTokenSystem, TypographyTokens } from '../../design-tokens/types';
import { FileInfo, Issue, IssueType, Severity, Category } from '../../types';

describe('TypographyRemediationEngine', () => {
  let engine: TypographyRemediationEngine;
  let mockDesignTokens: DesignTokenSystem;
  let mockFiles: FileInfo[];

  beforeEach(() => {
    mockDesignTokens = {
      colors: {
        primary: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#0ea5e9',
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
          950: '#082f49'
        },
        secondary: {
          50: '#fafafa',
          100: '#f4f4f5',
          200: '#e4e4e7',
          300: '#d4d4d8',
          400: '#a1a1aa',
          500: '#71717a',
          600: '#52525b',
          700: '#3f3f46',
          800: '#27272a',
          900: '#18181b',
          950: '#09090b'
        },
        semantic: {
          success: {
            light: '#dcfce7',
            default: '#16a34a',
            dark: '#15803d',
            contrast: '#ffffff'
          },
          warning: {
            light: '#fef3c7',
            default: '#f59e0b',
            dark: '#d97706',
            contrast: '#ffffff'
          },
          error: {
            light: '#fee2e2',
            default: '#dc2626',
            dark: '#b91c1c',
            contrast: '#ffffff'
          },
          info: {
            light: '#dbeafe',
            default: '#3b82f6',
            dark: '#2563eb',
            contrast: '#ffffff'
          }
        },
        neutral: {
          50: '#fafafa',
          100: '#f4f4f5',
          200: '#e4e4e7',
          300: '#d4d4d8',
          400: '#a1a1aa',
          500: '#71717a',
          600: '#52525b',
          700: '#3f3f46',
          800: '#27272a',
          900: '#18181b',
          950: '#09090b'
        },
        surface: {
          background: '#ffffff',
          foreground: '#09090b',
          card: '#ffffff',
          cardForeground: '#09090b',
          popover: '#ffffff',
          popoverForeground: '#09090b',
          muted: '#f4f4f5',
          mutedForeground: '#71717a',
          accent: '#f4f4f5',
          accentForeground: '#18181b',
          border: '#e4e4e7',
          input: '#e4e4e7',
          ring: '#3b82f6'
        }
      },
      typography: {
        fontFamilies: {
          sans: ['Inter', 'ui-sans-serif', 'system-ui', 'sans-serif'],
          serif: ['Georgia', 'ui-serif', 'serif'],
          mono: ['JetBrains Mono', 'ui-monospace', 'monospace'],
          display: ['Inter Display', 'Inter', 'sans-serif']
        },
        fontSizes: {
          xs: '0.75rem',
          sm: '0.875rem',
          base: '1rem',
          lg: '1.125rem',
          xl: '1.25rem',
          '2xl': '1.5rem',
          '3xl': '1.875rem',
          '4xl': '2.25rem',
          '5xl': '3rem',
          '6xl': '3.75rem',
          '7xl': '4.5rem',
          '8xl': '6rem',
          '9xl': '8rem'
        },
        fontWeights: {
          thin: 100,
          extralight: 200,
          light: 300,
          normal: 400,
          medium: 500,
          semibold: 600,
          bold: 700,
          extrabold: 800,
          black: 900
        },
        lineHeights: {
          none: 1,
          tight: 1.25,
          snug: 1.375,
          normal: 1.5,
          relaxed: 1.625,
          loose: 2
        },
        letterSpacing: {
          tighter: '-0.05em',
          tight: '-0.025em',
          normal: '0em',
          wide: '0.025em',
          wider: '0.05em',
          widest: '0.1em'
        },
        typeScale: {
          h1: {
            fontSize: '2.25rem',
            fontWeight: 700,
            lineHeight: 1.2,
            letterSpacing: '-0.025em',
            fontFamily: ['Inter Display', 'Inter', 'sans-serif']
          },
          h2: {
            fontSize: '1.875rem',
            fontWeight: 600,
            lineHeight: 1.3,
            letterSpacing: '-0.025em',
            fontFamily: ['Inter Display', 'Inter', 'sans-serif']
          },
          h3: {
            fontSize: '1.5rem',
            fontWeight: 600,
            lineHeight: 1.4,
            letterSpacing: '0em',
            fontFamily: ['Inter', 'sans-serif']
          },
          h4: {
            fontSize: '1.25rem',
            fontWeight: 500,
            lineHeight: 1.4,
            letterSpacing: '0em',
            fontFamily: ['Inter', 'sans-serif']
          },
          h5: {
            fontSize: '1.125rem',
            fontWeight: 500,
            lineHeight: 1.5,
            letterSpacing: '0em',
            fontFamily: ['Inter', 'sans-serif']
          },
          h6: {
            fontSize: '1rem',
            fontWeight: 500,
            lineHeight: 1.5,
            letterSpacing: '0em',
            fontFamily: ['Inter', 'sans-serif']
          },
          body: {
            fontSize: '1rem',
            fontWeight: 400,
            lineHeight: 1.5,
            letterSpacing: '0em',
            fontFamily: ['Inter', 'sans-serif']
          },
          bodyLarge: {
            fontSize: '1.125rem',
            fontWeight: 400,
            lineHeight: 1.5,
            letterSpacing: '0em',
            fontFamily: ['Inter', 'sans-serif']
          },
          bodySmall: {
            fontSize: '0.875rem',
            fontWeight: 400,
            lineHeight: 1.5,
            letterSpacing: '0em',
            fontFamily: ['Inter', 'sans-serif']
          },
          caption: {
            fontSize: '0.75rem',
            fontWeight: 400,
            lineHeight: 1.4,
            letterSpacing: '0.025em',
            fontFamily: ['Inter', 'sans-serif']
          },
          overline: {
            fontSize: '0.75rem',
            fontWeight: 600,
            lineHeight: 1.4,
            letterSpacing: '0.1em',
            fontFamily: ['Inter', 'sans-serif']
          }
        }
      },
      spacing: {
        scale: {
          0: '0px',
          px: '1px',
          0.5: '0.125rem',
          1: '0.25rem',
          1.5: '0.375rem',
          2: '0.5rem',
          2.5: '0.625rem',
          3: '0.75rem',
          3.5: '0.875rem',
          4: '1rem',
          5: '1.25rem',
          6: '1.5rem',
          7: '1.75rem',
          8: '2rem',
          9: '2.25rem',
          10: '2.5rem',
          11: '2.75rem',
          12: '3rem',
          14: '3.5rem',
          16: '4rem',
          20: '5rem',
          24: '6rem',
          28: '7rem',
          32: '8rem',
          36: '9rem',
          40: '10rem',
          44: '11rem',
          48: '12rem',
          52: '13rem',
          56: '14rem',
          60: '15rem',
          64: '16rem',
          72: '18rem',
          80: '20rem',
          96: '24rem'
        },
        semantic: {
          component: {
            padding: {
              xs: '0.5rem',
              sm: '0.75rem',
              md: '1rem',
              lg: '1.5rem',
              xl: '2rem'
            },
            margin: {
              xs: '0.5rem',
              sm: '0.75rem',
              md: '1rem',
              lg: '1.5rem',
              xl: '2rem'
            },
            gap: {
              xs: '0.5rem',
              sm: '0.75rem',
              md: '1rem',
              lg: '1.5rem',
              xl: '2rem'
            }
          },
          layout: {
            section: '4rem',
            container: '2rem',
            content: '1.5rem'
          }
        },
        layout: {
          containerMaxWidth: {
            sm: '640px',
            md: '768px',
            lg: '1024px',
            xl: '1280px',
            '2xl': '1536px'
          },
          containerPadding: {
            mobile: '1rem',
            tablet: '2rem',
            desktop: '3rem'
          }
        }
      },
      layout: {
        grid: {
          columns: {
            default: 12,
            sm: 4,
            md: 8,
            lg: 12,
            xl: 12
          },
          gutters: {
            xs: '0.5rem',
            sm: '1rem',
            md: '1.5rem',
            lg: '2rem',
            xl: '3rem'
          },
          margins: {
            mobile: '1rem',
            tablet: '2rem',
            desktop: '3rem',
            wide: '4rem'
          },
          baseline: {
            unit: '0.25rem',
            scale: 4
          }
        },
        breakpoints: {
          xs: '475px',
          sm: '640px',
          md: '768px',
          lg: '1024px',
          xl: '1280px',
          '2xl': '1536px',
          ranges: {
            mobile: '0-640px',
            tablet: '641px-1024px',
            desktop: '1025px-1536px',
            wide: '1537px+'
          }
        },
        containers: {
          maxWidths: {
            xs: '475px',
            sm: '640px',
            md: '768px',
            lg: '1024px',
            xl: '1280px',
            '2xl': '1536px',
            full: '100%'
          },
          padding: {
            mobile: '1rem',
            tablet: '2rem',
            desktop: '3rem'
          },
          centering: {
            auto: 'margin: 0 auto',
            flex: {
              horizontal: 'justify-content: center',
              vertical: 'align-items: center',
              both: 'justify-content: center; align-items: center'
            },
            grid: {
              horizontal: 'justify-items: center',
              vertical: 'align-items: center',
              both: 'place-items: center'
            }
          }
        },
        patterns: {
          stack: {
            gap: {
              xs: '0.5rem',
              sm: '1rem',
              md: '1.5rem',
              lg: '2rem',
              xl: '3rem'
            },
            alignment: {
              start: 'align-items: flex-start',
              center: 'align-items: center',
              end: 'align-items: flex-end',
              stretch: 'align-items: stretch'
            }
          },
          cluster: {
            gap: {
              xs: '0.5rem',
              sm: '1rem',
              md: '1.5rem',
              lg: '2rem',
              xl: '3rem'
            },
            justify: {
              start: 'justify-content: flex-start',
              center: 'justify-content: center',
              end: 'justify-content: flex-end',
              between: 'justify-content: space-between',
              around: 'justify-content: space-around',
              evenly: 'justify-content: space-evenly'
            },
            align: {
              start: 'align-items: flex-start',
              center: 'align-items: center',
              end: 'align-items: flex-end',
              baseline: 'align-items: baseline'
            }
          },
          sidebar: {
            sidebarWidth: {
              narrow: '200px',
              default: '300px',
              wide: '400px'
            },
            gap: '1rem',
            breakpoint: '768px',
            contentMinWidth: '50%'
          },
          switcher: {
            threshold: '768px',
            gap: '1rem',
            limit: 4
          },
          cover: {
            minHeight: {
              viewport: '100vh',
              container: '100%',
              content: 'auto'
            },
            padding: {
              top: '2rem',
              bottom: '2rem',
              sides: '1rem'
            }
          },
          grid: {
            minItemWidth: {
              xs: '200px',
              sm: '250px',
              md: '300px',
              lg: '350px'
            },
            gap: {
              xs: '0.5rem',
              sm: '1rem',
              md: '1.5rem',
              lg: '2rem',
              xl: '3rem'
            },
            autoFit: true,
            autoFill: false
          }
        },
        validation: {
          rules: [],
          patterns: [],
          accessibility: {
            focusManagement: {
              tabOrder: true,
              focusVisible: true,
              skipLinks: true
            },
            semanticStructure: {
              headingHierarchy: true,
              landmarkRoles: true,
              listStructure: true
            },
            responsiveDesign: {
              minTouchTarget: '44px',
              maxLineLength: '75ch',
              scalableText: true
            }
          }
        }
      },
      metadata: {
        version: '1.0.0',
        generatedAt: '2024-01-01T00:00:00Z',
        source: 'test'
      }
    };

    mockFiles = [
      {
        path: 'src/components/Button.tsx',
        extension: '.tsx',
        content: `
import React from 'react';

const Button = ({ children }: { children: React.ReactNode }) => {
  return (
    <button style={{ fontSize: '16px', fontWeight: '600', lineHeight: '1.6' }}>
      {children}
    </button>
  );
};

export default Button;
        `.trim()
      },
      {
        path: 'src/components/Header.css',
        extension: '.css',
        content: `
.header {
  font-size: 24px;
  font-weight: 700;
  line-height: 1.3;
}

.subtitle {
  font-size: 18px;
  font-weight: 400;
  line-height: 1.7;
}
        `.trim()
      },
      {
        path: 'src/components/Typography.tsx',
        extension: '.tsx',
        content: `
import React from 'react';

const Typography = () => {
  return (
    <div>
      <h1 style={{ fontSize: '32px', fontWeight: '800', lineHeight: '1.1' }}>Main Title</h1>
      <h2 style={{ fontSize: '28px', fontWeight: '700' }}>Subtitle</h2>
      <p style={{ fontSize: '14px', lineHeight: '1.8' }}>Body text</p>
    </div>
  );
};

export default Typography;
        `.trim()
      }
    ];

    engine = new TypographyRemediationEngine(mockDesignTokens);
  });

  describe('Font Size Standardization', () => {
    it('should identify and standardize hardcoded font sizes in CSS', async () => {
      const replacements = await engine.standardizeFontSizes(mockFiles);
      
      const cssReplacements = replacements.filter(r => r.filePath.includes('.css'));
      expect(cssReplacements.length).toBeGreaterThan(0);
      
      const headerReplacement = cssReplacements.find(r => 
        r.originalValue === '24px' && r.property === 'font-size'
      );
      expect(headerReplacement).toBeDefined();
      expect(headerReplacement?.tokenValue).toBe('var(--font-size-2xl)');
    });

    it('should identify and standardize hardcoded font sizes in React components', async () => {
      const replacements = await engine.standardizeFontSizes(mockFiles);
      
      const reactReplacements = replacements.filter(r => r.filePath.includes('.tsx'));
      expect(reactReplacements.length).toBeGreaterThan(0);
      
      const buttonReplacement = reactReplacements.find(r => 
        r.originalValue.includes('16px') && r.property === 'font-size'
      );
      expect(buttonReplacement).toBeDefined();
    });

    it('should convert pixel values to appropriate rem-based tokens', async () => {
      const replacements = await engine.standardizeFontSizes(mockFiles);
      
      const pixelReplacements = replacements.filter(r => r.originalValue.includes('px'));
      expect(pixelReplacements.length).toBeGreaterThan(0);
      
      pixelReplacements.forEach(replacement => {
        expect(replacement.tokenValue).toMatch(/var\(--font-size-\w+\)/);
      });
    });
  });

  describe('Typography Hierarchy Correction', () => {
    it('should identify heading elements with incorrect typography', async () => {
      // Create files with heading elements that have inline styles
      const filesWithHeadings: FileInfo[] = [
        {
          path: 'src/components/HeadingTest.tsx',
          extension: '.tsx',
          content: `
import React from 'react';

const HeadingTest = () => {
  return (
    <div>
      <h1 style={{ fontSize: '32px', fontWeight: '800', lineHeight: '1.1' }}>Main Title</h1>
      <h2 style={{ fontSize: '28px', fontWeight: '700' }}>Subtitle</h2>
    </div>
  );
};

export default HeadingTest;
          `.trim()
        }
      ];

      const corrections = await engine.correctTypographyHierarchy(filesWithHeadings);
      
      expect(corrections.length).toBeGreaterThan(0);
      
      const h1Correction = corrections.find(c => 
        c.element === 'h1' && c.property === 'fontSize'
      );
      expect(h1Correction).toBeDefined();
      expect(h1Correction?.currentValue).toBe('32px');
      expect(h1Correction?.correctedValue).toBe('2.25rem');
    });

    it('should suggest appropriate font weights for heading hierarchy', async () => {
      const filesWithHeadings: FileInfo[] = [
        {
          path: 'src/components/HeadingTest.tsx',
          extension: '.tsx',
          content: `
import React from 'react';

const HeadingTest = () => {
  return (
    <div>
      <h1 style={{ fontSize: '32px', fontWeight: '800', lineHeight: '1.1' }}>Main Title</h1>
      <h2 style={{ fontSize: '28px', fontWeight: '700' }}>Subtitle</h2>
    </div>
  );
};

export default HeadingTest;
          `.trim()
        }
      ];

      const corrections = await engine.correctTypographyHierarchy(filesWithHeadings);
      
      const weightCorrections = corrections.filter(c => c.property === 'fontWeight');
      expect(weightCorrections.length).toBeGreaterThan(0);
      
      const h1WeightCorrection = weightCorrections.find(c => c.element === 'h1');
      expect(h1WeightCorrection?.correctedValue).toBe('700');
    });
  });

  describe('Line Height Standardization', () => {
    it('should identify non-standard line heights', async () => {
      const standardizations = await engine.standardizeLineHeights(mockFiles);
      
      expect(standardizations.length).toBeGreaterThan(0);
      
      const nonStandardLineHeight = standardizations.find(s => 
        s.currentValue === '1.6' || s.currentValue === '1.7' || s.currentValue === '1.8'
      );
      expect(nonStandardLineHeight).toBeDefined();
    });

    it('should suggest closest standard line height values', async () => {
      const standardizations = await engine.standardizeLineHeights(mockFiles);
      
      const lineHeight16 = standardizations.find(s => s.currentValue === '1.6');
      if (lineHeight16) {
        expect(lineHeight16.standardizedValue).toBe('1.625'); // closest to 1.6
        expect(lineHeight16.tokenName).toBe('relaxed');
      }
    });

    it('should provide appropriate token names for standardized values', async () => {
      const standardizations = await engine.standardizeLineHeights(mockFiles);
      
      standardizations.forEach(standardization => {
        expect(standardization.tokenName).toMatch(/^(none|tight|snug|normal|relaxed|loose|custom)$/);
      });
    });
  });

  describe('Typography Token Validation', () => {
    it('should validate font-size token usage', async () => {
      const filesWithTokens = [
        {
          path: 'src/components/ValidTokens.tsx',
          extension: '.tsx',
          content: `
const ValidComponent = () => (
  <div style={{ fontSize: 'var(--font-size-lg)' }}>
    <h1 style={{ fontSize: 'var(--font-size-4xl)' }}>Title</h1>
    <p style={{ fontSize: 'var(--font-size-invalid)' }}>Invalid token</p>
  </div>
);
          `.trim()
        }
      ];

      const validation = await engine.validateTypographyTokenUsage(filesWithTokens);
      
      expect(validation.validUsages).toBe(2);
      expect(validation.invalidUsages).toBe(1);
      expect(validation.suggestions.length).toBeGreaterThan(0);
      expect(validation.suggestions[0]).toContain('Invalid font-size token');
    });

    it('should track typography token coverage', async () => {
      const filesWithTokens = [
        {
          path: 'src/components/TokenUsage.tsx',
          extension: '.tsx',
          content: `
const Component = () => (
  <div style={{ 
    fontSize: 'var(--font-size-lg)',
    fontFamily: 'var(--font-family-sans)',
    fontWeight: 'var(--font-weight-bold)',
    lineHeight: 'var(--line-height-normal)'
  }}>
    Content
  </div>
);
          `.trim()
        }
      ];

      const validation = await engine.validateTypographyTokenUsage(filesWithTokens);
      
      expect(validation.coverage.fontSize).toBe(1);
      expect(validation.coverage.fontFamily).toBe(1);
      expect(validation.coverage.fontWeight).toBe(1);
      expect(validation.coverage.lineHeight).toBe(1);
    });
  });

  describe('Full Remediation Process', () => {
    it('should process typography issues and generate comprehensive remediation', async () => {
      const mockIssues: Issue[] = [
        {
          id: 'typography-1',
          type: IssueType.TYPOGRAPHY_INCONSISTENCY,
          severity: Severity.MEDIUM,
          category: Category.VISUAL,
          description: "Hardcoded font-size value '16px'. Consider using design tokens for consistency.",
          location: {
            filePath: 'src/components/Button.tsx',
            lineNumber: 6,
            columnNumber: 25,
            context: "    <button style={{ fontSize: '16px', fontWeight: '600', lineHeight: '1.6' }}>"
          },
          suggestion: "Replace '16px' with a typography token like 'var(--font-size-base)'",
          autoFixable: true,
          impact: {
            userExperience: 4,
            maintenanceEffort: 6,
            implementationComplexity: 3
          }
        },
        {
          id: 'typography-2',
          type: IssueType.TYPOGRAPHY_INCONSISTENCY,
          severity: Severity.HIGH,
          category: Category.VISUAL,
          description: "Hardcoded font-size value '24px'. Consider using design tokens for consistency.",
          location: {
            filePath: 'src/components/Header.css',
            lineNumber: 2,
            columnNumber: 3,
            context: "  font-size: 24px;"
          },
          suggestion: "Replace '24px' with a typography token like 'var(--font-size-2xl)'",
          autoFixable: true,
          impact: {
            userExperience: 6,
            maintenanceEffort: 7,
            implementationComplexity: 3
          }
        }
      ];

      const result = await engine.remediateTypographyIssues(mockIssues, mockFiles);
      
      expect(result.success).toBe(true);
      expect(result.replacements.length).toBeGreaterThan(0);
      expect(result.filesModified.length).toBeGreaterThan(0);
      expect(result.summary.totalReplacements).toBeGreaterThan(0);
    });

    it('should handle errors gracefully during remediation', async () => {
      // Create an issue that references a file that doesn't exist in mockFiles
      const invalidIssues: Issue[] = [
        {
          id: 'invalid-typography',
          type: IssueType.TYPOGRAPHY_INCONSISTENCY,
          severity: Severity.MEDIUM,
          category: Category.VISUAL,
          description: "Hardcoded font-size value '16px'. Consider using design tokens for consistency.",
          location: {
            filePath: 'non-existent-file.tsx',
            lineNumber: 1,
            columnNumber: 1,
            context: "fontSize: '16px'"
          },
          suggestion: "Invalid suggestion",
          autoFixable: true,
          impact: {
            userExperience: 1,
            maintenanceEffort: 1,
            implementationComplexity: 1
          }
        }
      ];

      // Create a file that will cause processing errors
      const problematicFiles: FileInfo[] = [
        {
          path: 'src/components/Problematic.tsx',
          extension: '.tsx',
          content: '' // Empty content that might cause issues
        }
      ];

      const result = await engine.remediateTypographyIssues(invalidIssues, problematicFiles);
      
      // The result should still be successful since we handle errors gracefully
      // but there should be no replacements since the file doesn't match the issue
      expect(result.replacements.length).toBe(0);
      expect(result.success).toBe(true);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle files with no typography issues', async () => {
      const cleanFiles: FileInfo[] = [
        {
          path: 'src/components/Clean.tsx',
          extension: '.tsx',
          content: `
const CleanComponent = () => (
  <div style={{ fontSize: 'var(--font-size-base)' }}>
    Clean typography
  </div>
);
          `.trim()
        }
      ];

      const replacements = await engine.standardizeFontSizes(cleanFiles);
      expect(replacements.length).toBe(0);
    });

    it('should skip comment lines during analysis', async () => {
      const filesWithComments: FileInfo[] = [
        {
          path: 'src/components/Comments.css',
          extension: '.css',
          content: `
/* This is a comment with font-size: 16px; */
.actual-rule {
  font-size: 18px; /* This should be detected */
}
// Another comment with font-size: 20px;
          `.trim()
        }
      ];

      const replacements = await engine.standardizeFontSizes(filesWithComments);
      
      // Should only find the actual CSS rule, not the comments
      expect(replacements.length).toBe(1);
      expect(replacements[0].originalValue).toBe('18px');
    });

    it('should handle malformed CSS gracefully', async () => {
      const malformedFiles: FileInfo[] = [
        {
          path: 'src/components/Malformed.css',
          extension: '.css',
          content: `
.incomplete {
  font-size: 
}
.valid {
  font-size: 16px;
}
          `.trim()
        }
      ];

      const replacements = await engine.standardizeFontSizes(malformedFiles);
      
      // Should still process the valid rule
      expect(replacements.length).toBe(1);
      expect(replacements[0].originalValue).toBe('16px');
    });
  });
});