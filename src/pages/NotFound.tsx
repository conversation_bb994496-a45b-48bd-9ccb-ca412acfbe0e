import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import SEO from "@/components/SEO";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-elev-background-muted">
      <SEO
        title="Page Not Found - ELEV802 | 702HOCKEY - Las Vegas"
        description="The page you're looking for doesn't exist. Return to ELEV802's homepage for premier hockey training in Las Vegas."
        keywords="ELEV802, 702Hockey, page not found"
        canonical="/404"
        robots="noindex, nofollow"
      />
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-4">404</h1>
        <p className="text-xl text-elev-text-secondary mb-4">Oops! Page not found</p>
        <a href="/" className="text-blue-500 hover:text-blue-700 underline">
          Return to Home
        </a>
      </div>
    </div>
  );
};

export default NotFound;
