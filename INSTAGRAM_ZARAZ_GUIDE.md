# Debugging and Implementing Instagram Embeds with Cloudflare Zaraz

This guide provides a best-practice approach for embedding Instagram content on a modern website (specifically a React Single-Page Application) using Cloudflare Zaraz.

### 1. Diagnosis of the Failure

Given your setup (a React SPA) and the common behavior of embed scripts, the failure is likely due to one or more of the following race conditions:

*   **Script Execution vs. Component Rendering:** The Zaraz `Pageview` or `DOM Ready` trigger fires and loads Instagram's `embed.js` script *before* your React component containing the `<blockquote>` has finished rendering. The script runs, finds no embed codes to process, and does nothing.
*   **Single-Page Application Lifecycle:** The `embed.js` script runs only once on the initial page load. When your users navigate to a different route within your SPA, the DOM is updated, but the Instagram script does not automatically re-run to process new embeds that appear.
*   **Script Initialization:** The `embed.js` script defines a global object `window.instgrm`. The function needed to process embeds is `window.instgrm.Embeds.process()`. If you try to call this function before the script has loaded and initialized this object, you will get a console error.

### 2. Best-Practice Method in Zaraz

To solve this, we need to separate the **loading** of the script from the **processing** of the embeds. This gives us full control over the timing.

1.  **Tool 1: Custom HTML (to load the script):** We'll use the "Custom HTML" tool to inject the Instagram `embed.js` script tag. This makes the script's functions available globally on the `window` object.
2.  **Tool 2: Custom Action (to process the embeds):** We'll create a separate "Action" using the "Custom JavaScript" type. This action will call the `window.instgrm.Embeds.process()` function, which manually tells Instagram's script to find and render any new embeds on the page.

This two-step approach is reliable and performant.

### 3. Precise Code and Configuration

Here is the exact setup for your webpage and Zaraz.

#### On Your Webpage (React Component)

Place the standard Instagram `<blockquote>` embed code within the component where you want it to appear. You don't need to add the `<script>` tag here; Zaraz will handle that.

**Example `InstagramEmbed.tsx` component:**
```tsx
import React, { useEffect } from 'react';

const InstagramEmbed = () => {
  // This is an advanced option for the trigger, explained in the next section.
  useEffect(() => {
    // Optional: Fire a custom event for Zaraz when the component is ready.
    // window.zaraz?.track('instagram_embed_ready');
  }, []);

  return (
    <blockquote
      className="instagram-media"
      data-instgrm-permalink="https://www.instagram.com/p/C78_gA7yZ6J/"
      data-instgrm-version="14"
      style={{
        background: '#FFF',
        border: 0,
        borderRadius: '3px',
        boxShadow: '0 0 1px 0 rgba(0,0,0,0.5),0 1px 10px 0 rgba(0,0,0,0.15)',
        margin: '1px',
        maxWidth: '540px',
        minWidth: '326px',
        padding: 0,
        width: 'calc(100% - 2px)',
      }}
    >
      <div style={{ padding: '16px' }}>
        <a
          href="https://www.instagram.com/p/C78_gA7yZ6J/"
          style={{
            background: '#FFFFFF',
            lineHeight: 0,
            padding: '0 0',
            textAlign: 'center',
            textDecoration: 'none',
            width: '100%',
          }}
          target="_blank"
        >
          {/* This is where the visual content of the embed will be rendered. */}
        </a>
      </div>
    </blockquote>
  );
};

export default InstagramEmbed;
```

#### In Cloudflare Zaraz

**Step A: Create the "Custom HTML" Tool to Load the Script**

1.  Go to Zaraz -> Tools -> Add a new tool -> Select "Custom HTML" from the tool catalog.
2.  **Name it:** "Instagram Embed Script".
3.  **In the "HTML Code" box, paste the following:**
    ```html
    <script async src="//www.instagram.com/embed.js" charset="utf-8"></script>
    ```
4.  **Create a Trigger:**
    *   **Name:** "Pageview"
    *   **Rule:** Match `{{ system.page.url.pathname }}` -> Contains -> `/` (This will fire it on all pages).
    *   **Action:** "Set up Action" -> Select "Instagram Embed Script" -> "Fire".

**Step B: Create the "Action" to Process the Embed**

1.  Go to Zaraz -> Actions -> Create a new action.
2.  **Name it:** "Process Instagram Embeds".
3.  **Tool:** Choose "Custom JavaScript".
4.  **Custom JavaScript Code:** Paste the following code. This safely checks if the Instagram script is loaded before trying to run the process function.
    ```javascript
    try {
      if (window.instgrm && typeof window.instgrm.Embeds.process === 'function') {
        window.instgrm.Embeds.process();
      }
    } catch (e) {
      // You can add console.error(e) here for debugging if needed.
    }
    ```

### 4. Optimal Trigger Settings

This is the most critical step. The "Process Instagram Embeds" action needs to fire at the right moment.

**Recommended Trigger: Element Visibility**

This is the most reliable and performant method. The action will only fire when the user actually scrolls to the embed.

1.  In Zaraz, go to Triggers -> Create a new trigger.
2.  **Name it:** "Instagram Embed Visible".
3.  **Rule Type:** Select "Element Visibility".
4.  **CSS Selector:** Enter `.instagram-media`. This targets the `<blockquote>` element.
5.  Save the trigger.
6.  Go back to your "Process Instagram Embeds" action and assign this "Instagram Embed Visible" trigger to it.

**Alternative Trigger: Custom Event (for more control)**

If you need absolute control, you can have your React component tell Zaraz exactly when it's ready.

1.  Uncomment the `useEffect` code in the `InstagramEmbed.tsx` example above.
2.  In Zaraz, create a new trigger:
    *   **Name:** "Instagram Component Ready".
    *   **Rule Type:** "Custom Event".
    *   **Event Name:** `instagram_embed_ready`.
3.  Assign this trigger to your "Process Instagram Embeds" action.

### 5. Consent Management Integration

Zaraz makes this very simple. If you use Cloudflare's consent management tool, you can ensure the Instagram embed only loads for users who agree.

1.  Navigate to the action you created ("Process Instagram Embeds").
2.  Find the **"Required Consent"** dropdown.
3.  Select the appropriate consent purpose. For Instagram, this would typically be a purpose you've defined as "Social Media" or "Functional".
4.  Do the same for the "Instagram Embed Script" tool you created in Step A.

With this setting, Zaraz will not fire the tool or the action unless the user has granted the required consent. The embed will simply not appear, with no errors.

By following this guide, you will have a performant, reliable, and privacy-compliant Instagram embed system managed entirely through Cloudflare Zaraz.