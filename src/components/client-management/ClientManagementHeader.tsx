import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Plus, Search, Filter } from 'lucide-react';

/**
 * ClientManagementHeader component provides header functionality for client management
 * Includes search, filtering, and action buttons for client operations
 */
interface ClientManagementHeaderProps {
  onAddClient?: () => void;
  onSearch?: (query: string) => void;
  onFilter?: () => void;
  clientCount?: number;
  activeFilters?: string[];
}

const ClientManagementHeader: React.FC<ClientManagementHeaderProps> = ({
  onAddClient,
  onSearch,
  onFilter,
  clientCount = 0,
  activeFilters = []
}) => {
  /**
   * Handle search input change
   */
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const query = event.target.value;
    onSearch?.(query);
  };

  /**
   * Handle add client button click
   */
  const handleAddClient = () => {
    onAddClient?.();
  };

  /**
   * Handle filter button click
   */
  const handleFilter = () => {
    onFilter?.();
  };

  return (
    <div className="bg-white border-b border-elev-background-accent px-6 py-4">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        {/* Title and Client Count */}
        <div className="flex items-center gap-3">
          <h1 className="text-2xl font-bold text-elev-text-primary">
            Client Management
          </h1>
          <Badge variant="secondary" className="text-sm">
            {clientCount} {clientCount === 1 ? 'Client' : 'Clients'}
          </Badge>
        </div>

        {/* Actions */}
        <div className="flex items-center gap-3">
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-elev-text-muted" />
            <Input
              type="text"
              placeholder="Search clients..."
              className="pl-10 w-64"
              onChange={handleSearchChange}
            />
          </div>

          {/* Filter Button */}
          <Button
            variant="outline"
            onClick={handleFilter}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            Filter
            {activeFilters.length > 0 && (
              <Badge variant="destructive" className="ml-1 text-xs">
                {activeFilters.length}
              </Badge>
            )}
          </Button>

          {/* Add Client Button */}
          <Button
            onClick={handleAddClient}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add Client
          </Button>
        </div>
      </div>

      {/* Active Filters Display */}
      {activeFilters.length > 0 && (
        <div className="mt-4 flex items-center gap-2">
          <span className="text-sm text-elev-text-secondary">Active filters:</span>
          <div className="flex gap-2">
            {activeFilters.map((filter, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {filter}
              </Badge>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ClientManagementHeader;