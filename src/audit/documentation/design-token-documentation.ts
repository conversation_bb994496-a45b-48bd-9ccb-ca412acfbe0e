/**
 * Design Token Documentation Generator
 * Creates comprehensive documentation for design tokens with usage examples and guidelines
 */

import { promises as fs } from 'fs';
import * as path from 'path';
import {
  DesignTokenDocumentation,
  TokenSection,
  TokenCategory,
  TokenDocEntry,
  TokenExample,
  CodeExample,
  TokenUsageGuidelines,
  MigrationGuide
} from './types';
import { DesignTokenSystem } from '../design-tokens/types';

export class DesignTokenDocumentationGenerator {
  private outputDir: string;

  constructor(outputDir: string = './docs/tokens') {
    this.outputDir = outputDir;
  }

  /**
   * Generate comprehensive design token documentation
   */
  async generateDocumentation(
    tokens: DesignTokenSystem,
    options: TokenDocumentationOptions = {}
  ): Promise<DesignTokenDocumentation> {
    const sections = this.createTokenSections(tokens);
    const examples = this.createTokenExamples(tokens);
    const usage = this.createUsageGuidelines(tokens);
    const migration = this.createMigrationGuide(tokens, options.previousVersion);

    const documentation: DesignTokenDocumentation = {
      tokens,
      sections,
      examples,
      usage,
      migration
    };

    // Generate documentation files
    await this.generateMarkdownDocs(documentation);
    await this.generateJSONDocs(documentation);
    await this.generateCSSVariables(tokens);
    await this.generateSCSSVariables(tokens);
    await this.generateJavaScriptTokens(tokens);
    await this.generateTailwindConfig(tokens);

    return documentation;
  }

  /**
   * Create token sections organized by category
   */
  private createTokenSections(tokens: DesignTokenSystem): TokenSection[] {
    return [
      this.createColorSection(tokens.colors),
      this.createTypographySection(tokens.typography),
      this.createSpacingSection(tokens.spacing),
      this.createLayoutSection(tokens.layout)
    ];
  }

  /**
   * Create color token section
   */
  private createColorSection(colors: any): TokenSection {
    const colorTokens: TokenDocEntry[] = [];

    // Primary colors
    Object.entries(colors.primary).forEach(([shade, value]) => {
      colorTokens.push({
        name: `color.primary.${shade}`,
        value: value as string,
        description: `Primary color shade ${shade}`,
        usage: ['Brand elements', 'Primary actions', 'Key highlights'],
        examples: [
          `background-color: var(--color-primary-${shade})`,
          `color: var(--color-primary-${shade})`
        ]
      });
    });

    // Secondary colors
    Object.entries(colors.secondary).forEach(([shade, value]) => {
      colorTokens.push({
        name: `color.secondary.${shade}`,
        value: value as string,
        description: `Secondary color shade ${shade}`,
        usage: ['Secondary actions', 'Supporting elements', 'Accents'],
        examples: [
          `background-color: var(--color-secondary-${shade})`,
          `border-color: var(--color-secondary-${shade})`
        ]
      });
    });

    // Semantic colors
    Object.entries(colors.semantic).forEach(([type, variants]) => {
      Object.entries(variants).forEach(([variant, value]) => {
        colorTokens.push({
          name: `color.${type}.${variant}`,
          value: value as string,
          description: `${type.charAt(0).toUpperCase() + type.slice(1)} ${variant} color`,
          usage: [`${type} states`, `${type} feedback`, `${type} indicators`],
          examples: [
            `background-color: var(--color-${type}-${variant})`,
            `color: var(--color-${type}-${variant})`
          ]
        });
      });
    });

    return {
      category: 'color',
      title: 'Color Tokens',
      description: 'Color palette including primary, secondary, semantic, and neutral colors',
      tokens: colorTokens,
      examples: this.createColorExamples()
    };
  }

  /**
   * Create typography token section
   */
  private createTypographySection(typography: any): TokenSection {
    const typographyTokens: TokenDocEntry[] = [];

    // Font families
    Object.entries(typography.fontFamilies).forEach(([family, fonts]) => {
      typographyTokens.push({
        name: `font.family.${family}`,
        value: (fonts as string[]).join(', '),
        description: `${family.charAt(0).toUpperCase() + family.slice(1)} font family`,
        usage: [`${family} text`, 'Typography hierarchy'],
        examples: [
          `font-family: var(--font-family-${family})`,
          `font-family: ${(fonts as string[]).join(', ')}`
        ]
      });
    });

    // Font sizes
    Object.entries(typography.fontSizes).forEach(([size, value]) => {
      typographyTokens.push({
        name: `font.size.${size}`,
        value: value as string,
        description: `Font size ${size}`,
        usage: ['Text sizing', 'Typography scale'],
        examples: [
          `font-size: var(--font-size-${size})`,
          `font-size: ${value}`
        ]
      });
    });

    // Type scale
    Object.entries(typography.typeScale).forEach(([element, style]) => {
      const styleObj = style as any;
      typographyTokens.push({
        name: `typography.${element}`,
        value: JSON.stringify(styleObj, null, 2),
        description: `Complete typography style for ${element}`,
        usage: [`${element} elements`, 'Typography hierarchy'],
        examples: [
          `@apply typography-${element}`,
          `font-size: ${styleObj.fontSize}; font-weight: ${styleObj.fontWeight}; line-height: ${styleObj.lineHeight};`
        ]
      });
    });

    return {
      category: 'typography',
      title: 'Typography Tokens',
      description: 'Typography system including fonts, sizes, weights, and complete type styles',
      tokens: typographyTokens,
      examples: this.createTypographyExamples()
    };
  }

  /**
   * Create spacing token section
   */
  private createSpacingSection(spacing: any): TokenSection {
    const spacingTokens: TokenDocEntry[] = [];

    // Spacing scale
    Object.entries(spacing.scale).forEach(([size, value]) => {
      spacingTokens.push({
        name: `spacing.${size}`,
        value: value as string,
        description: `Spacing value ${size}`,
        usage: ['Margins', 'Padding', 'Gaps'],
        examples: [
          `margin: var(--spacing-${size})`,
          `padding: var(--spacing-${size})`,
          `gap: var(--spacing-${size})`
        ]
      });
    });

    // Semantic spacing
    const semantic = spacing.semantic;
    Object.entries(semantic.component).forEach(([type, sizes]) => {
      Object.entries(sizes).forEach(([size, value]) => {
        spacingTokens.push({
          name: `spacing.component.${type}.${size}`,
          value: value as string,
          description: `Component ${type} ${size}`,
          usage: [`Component ${type}`, 'Layout spacing'],
          examples: [
            `${type}: var(--spacing-component-${type}-${size})`,
            `${type}: ${value}`
          ]
        });
      });
    });

    return {
      category: 'spacing',
      title: 'Spacing Tokens',
      description: 'Spacing system including scale values and semantic spacing for components',
      tokens: spacingTokens,
      examples: this.createSpacingExamples()
    };
  }

  /**
   * Create layout token section
   */
  private createLayoutSection(layout: any): TokenSection {
    const layoutTokens: TokenDocEntry[] = [];

    // Breakpoints
    Object.entries(layout.breakpoints).forEach(([breakpoint, value]) => {
      if (breakpoint !== 'ranges') {
        layoutTokens.push({
          name: `breakpoint.${breakpoint}`,
          value: value as string,
          description: `${breakpoint.toUpperCase()} breakpoint`,
          usage: ['Responsive design', 'Media queries'],
          examples: [
            `@media (min-width: var(--breakpoint-${breakpoint}))`,
            `@media (min-width: ${value})`
          ]
        });
      }
    });

    // Container max widths
    Object.entries(layout.containers.maxWidths).forEach(([size, value]) => {
      layoutTokens.push({
        name: `container.${size}`,
        value: value as string,
        description: `Container max width ${size}`,
        usage: ['Container sizing', 'Layout constraints'],
        examples: [
          `max-width: var(--container-${size})`,
          `max-width: ${value}`
        ]
      });
    });

    return {
      category: 'layout',
      title: 'Layout Tokens',
      description: 'Layout system including breakpoints, containers, and grid specifications',
      tokens: layoutTokens,
      examples: this.createLayoutExamples()
    };
  }

  /**
   * Create token usage examples
   */
  private createTokenExamples(tokens: DesignTokenSystem): TokenExample[] {
    return [
      ...this.createColorExamples(),
      ...this.createTypographyExamples(),
      ...this.createSpacingExamples(),
      ...this.createLayoutExamples()
    ];
  }

  /**
   * Create color examples
   */
  private createColorExamples(): TokenExample[] {
    return [
      {
        title: 'Primary Button',
        description: 'Using primary color tokens for button styling',
        tokens: ['color.primary.500', 'color.primary.600', 'color.primary.50'],
        code: {
          css: `.btn-primary {
  background-color: var(--color-primary-500);
  color: var(--color-primary-50);
  border: 1px solid var(--color-primary-600);
}

.btn-primary:hover {
  background-color: var(--color-primary-600);
}`,
          tailwind: `<button className="bg-primary-500 text-primary-50 border border-primary-600 hover:bg-primary-600">
  Primary Button
</button>`
        },
        preview: 'Button with primary color styling',
        category: 'color'
      },
      {
        title: 'Status Indicators',
        description: 'Using semantic color tokens for status feedback',
        tokens: ['color.success.default', 'color.error.default', 'color.warning.default'],
        code: {
          css: `.status-success { color: var(--color-success-default); }
.status-error { color: var(--color-error-default); }
.status-warning { color: var(--color-warning-default); }`,
          tailwind: `<div className="text-success">Success message</div>
<div className="text-error">Error message</div>
<div className="text-warning">Warning message</div>`
        },
        preview: 'Status messages with semantic colors',
        category: 'color'
      }
    ];
  }

  /**
   * Create typography examples
   */
  private createTypographyExamples(): TokenExample[] {
    return [
      {
        title: 'Typography Hierarchy',
        description: 'Using typography tokens for consistent text hierarchy',
        tokens: ['typography.h1', 'typography.h2', 'typography.body'],
        code: {
          css: `.heading-1 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  font-family: var(--font-family-display);
}`,
          tailwind: `<h1 className="text-4xl font-bold leading-tight font-display">
  Main Heading
</h1>`
        },
        preview: 'Consistent typography hierarchy',
        category: 'typography'
      }
    ];
  }

  /**
   * Create spacing examples
   */
  private createSpacingExamples(): TokenExample[] {
    return [
      {
        title: 'Component Spacing',
        description: 'Using spacing tokens for consistent component layout',
        tokens: ['spacing.4', 'spacing.8', 'spacing.component.padding.md'],
        code: {
          css: `.card {
  padding: var(--spacing-component-padding-md);
  margin-bottom: var(--spacing-8);
  gap: var(--spacing-4);
}`,
          tailwind: `<div className="p-6 mb-8 space-y-4">
  Card content
</div>`
        },
        preview: 'Card with consistent spacing',
        category: 'spacing'
      }
    ];
  }

  /**
   * Create layout examples
   */
  private createLayoutExamples(): TokenExample[] {
    return [
      {
        title: 'Responsive Container',
        description: 'Using layout tokens for responsive containers',
        tokens: ['container.lg', 'breakpoint.md'],
        code: {
          css: `.container {
  max-width: var(--container-lg);
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

@media (min-width: var(--breakpoint-md)) {
  .container {
    padding: 0 var(--spacing-8);
  }
}`,
          tailwind: `<div className="max-w-lg mx-auto px-4 md:px-8">
  Container content
</div>`
        },
        preview: 'Responsive container with consistent sizing',
        category: 'layout'
      }
    ];
  }

  /**
   * Create usage guidelines
   */
  private createUsageGuidelines(tokens: DesignTokenSystem): TokenUsageGuidelines {
    return {
      naming: [
        {
          pattern: 'category.subcategory.variant',
          description: 'Use hierarchical naming for token organization',
          examples: ['color.primary.500', 'spacing.component.padding.md'],
          category: 'color'
        }
      ],
      hierarchy: [
        {
          rule: 'Use semantic tokens over raw values',
          description: 'Prefer semantic meaning over specific values',
          examples: ['color.success.default instead of color.green.500'],
          violations: ['Using hardcoded hex values', 'Bypassing token system']
        }
      ],
      combinations: [
        {
          tokens: ['color.primary.500', 'color.primary.50'],
          description: 'High contrast pairing for accessibility',
          example: 'Primary button with white text',
          context: 'Interactive elements'
        }
      ],
      antiPatterns: [
        {
          description: 'Using hardcoded values instead of tokens',
          example: 'color: #3b82f6',
          solution: 'color: var(--color-primary-500)',
          tokens: ['color.primary.500']
        }
      ]
    };
  }

  /**
   * Create migration guide
   */
  private createMigrationGuide(tokens: DesignTokenSystem, previousVersion?: string): MigrationGuide {
    return {
      version: tokens.metadata.version,
      changes: [],
      steps: [
        {
          order: 1,
          title: 'Update token references',
          description: 'Replace old token names with new ones',
          commands: ['npm run tokens:update'],
          validation: ['npm run tokens:validate']
        }
      ],
      automation: [
        {
          name: 'Token Migration Script',
          description: 'Automatically update token references in codebase',
          script: 'scripts/migrate-tokens.js',
          usage: 'npm run migrate-tokens'
        }
      ]
    };
  }

  /**
   * Generate Markdown documentation
   */
  private async generateMarkdownDocs(documentation: DesignTokenDocumentation): Promise<void> {
    await fs.mkdir(this.outputDir, { recursive: true });

    // Generate main documentation
    const mainDoc = this.generateMainMarkdown(documentation);
    await fs.writeFile(path.join(this.outputDir, 'README.md'), mainDoc, 'utf-8');

    // Generate section-specific docs
    for (const section of documentation.sections) {
      const sectionDoc = this.generateSectionMarkdown(section);
      await fs.writeFile(path.join(this.outputDir, `${section.category}.md`), sectionDoc, 'utf-8');
    }

    // Generate usage guidelines
    const usageDoc = this.generateUsageMarkdown(documentation.usage);
    await fs.writeFile(path.join(this.outputDir, 'usage.md'), usageDoc, 'utf-8');

    // Generate migration guide
    const migrationDoc = this.generateMigrationMarkdown(documentation.migration);
    await fs.writeFile(path.join(this.outputDir, 'migration.md'), migrationDoc, 'utf-8');
  }

  /**
   * Generate main documentation markdown
   */
  private generateMainMarkdown(documentation: DesignTokenDocumentation): string {
    return `# Design Tokens

Design tokens are the visual design atoms of the design system — specifically, they are named entities that store visual design attributes.

## Token Categories

${documentation.sections.map(section => `
### [${section.title}](${section.category}.md)

${section.description}

**Tokens:** ${section.tokens.length}
`).join('')}

## Quick Reference

| Category | Tokens | Examples |
|----------|--------|----------|
${documentation.sections.map(section => 
  `| [${section.title}](${section.category}.md) | ${section.tokens.length} | ${section.examples.length} |`
).join('\n')}

## Usage Guidelines

See [Usage Guidelines](usage.md) for detailed information on:
- Naming conventions
- Token hierarchy
- Best practices
- Anti-patterns

## Migration

See [Migration Guide](migration.md) for information on updating to the latest token version.

## Generated Files

This documentation includes generated files for different platforms:

- **CSS Variables:** \`tokens.css\`
- **SCSS Variables:** \`tokens.scss\`
- **JavaScript/TypeScript:** \`tokens.js\`, \`tokens.ts\`
- **Tailwind Config:** \`tailwind.tokens.js\`
`;
  }

  /**
   * Generate section-specific markdown
   */
  private generateSectionMarkdown(section: TokenSection): string {
    return `# ${section.title}

${section.description}

## Tokens

| Name | Value | Description | Usage |
|------|-------|-------------|-------|
${section.tokens.map(token => 
  `| \`${token.name}\` | \`${token.value}\` | ${token.description} | ${token.usage.join(', ')} |`
).join('\n')}

## Examples

${section.examples.map(example => `
### ${example.title}

${example.description}

**Tokens used:** ${example.tokens.map(t => `\`${t}\``).join(', ')}

\`\`\`css
${example.code.css}
\`\`\`

${example.code.tailwind ? `
**Tailwind:**
\`\`\`jsx
${example.code.tailwind}
\`\`\`
` : ''}
`).join('')}
`;
  }

  /**
   * Generate usage guidelines markdown
   */
  private generateUsageMarkdown(usage: TokenUsageGuidelines): string {
    return `# Token Usage Guidelines

## Naming Conventions

${usage.naming.map(convention => `
### ${convention.pattern}

${convention.description}

**Examples:**
${convention.examples.map(ex => `- \`${ex}\``).join('\n')}
`).join('')}

## Hierarchy Rules

${usage.hierarchy.map(rule => `
### ${rule.rule}

${rule.description}

**Examples:**
${rule.examples.map(ex => `- ${ex}`).join('\n')}

**Violations:**
${rule.violations.map(v => `- ${v}`).join('\n')}
`).join('')}

## Token Combinations

${usage.combinations.map(combo => `
### ${combo.description}

**Tokens:** ${combo.tokens.map(t => `\`${t}\``).join(', ')}

**Example:** ${combo.example}

**Context:** ${combo.context}
`).join('')}

## Anti-Patterns

${usage.antiPatterns.map(pattern => `
### ${pattern.description}

**❌ Don't:**
\`\`\`css
${pattern.example}
\`\`\`

**✅ Do:**
\`\`\`css
${pattern.solution}
\`\`\`

**Related tokens:** ${pattern.tokens.map(t => `\`${t}\``).join(', ')}
`).join('')}
`;
  }

  /**
   * Generate migration guide markdown
   */
  private generateMigrationMarkdown(migration: MigrationGuide): string {
    return `# Migration Guide

## Version ${migration.version}

${migration.changes.length > 0 ? `
## Changes

${migration.changes.map(change => `
### ${change.type.toUpperCase()}: ${change.token}

${change.reason}

${change.oldValue ? `**Old value:** \`${change.oldValue}\`` : ''}
${change.newValue ? `**New value:** \`${change.newValue}\`` : ''}

**Impact:** ${change.impact}
`).join('')}
` : ''}

## Migration Steps

${migration.steps.map(step => `
### ${step.order}. ${step.title}

${step.description}

**Commands:**
${step.commands.map(cmd => `\`${cmd}\``).join('\n')}

**Validation:**
${step.validation.map(val => `\`${val}\``).join('\n')}
`).join('')}

## Automation Scripts

${migration.automation.map(script => `
### ${script.name}

${script.description}

**Usage:** \`${script.usage}\`

**Script:** \`${script.script}\`
`).join('')}
`;
  }

  /**
   * Generate JSON documentation
   */
  private async generateJSONDocs(documentation: DesignTokenDocumentation): Promise<void> {
    const jsonPath = path.join(this.outputDir, 'tokens.json');
    await fs.writeFile(jsonPath, JSON.stringify(documentation, null, 2), 'utf-8');
  }

  /**
   * Generate CSS variables
   */
  private async generateCSSVariables(tokens: DesignTokenSystem): Promise<void> {
    const css = this.generateCSSFromTokens(tokens);
    await fs.writeFile(path.join(this.outputDir, 'tokens.css'), css, 'utf-8');
  }

  /**
   * Generate SCSS variables
   */
  private async generateSCSSVariables(tokens: DesignTokenSystem): Promise<void> {
    const scss = this.generateSCSSFromTokens(tokens);
    await fs.writeFile(path.join(this.outputDir, 'tokens.scss'), scss, 'utf-8');
  }

  /**
   * Generate JavaScript tokens
   */
  private async generateJavaScriptTokens(tokens: DesignTokenSystem): Promise<void> {
    const js = this.generateJSFromTokens(tokens);
    await fs.writeFile(path.join(this.outputDir, 'tokens.js'), js, 'utf-8');
    
    const ts = this.generateTSFromTokens(tokens);
    await fs.writeFile(path.join(this.outputDir, 'tokens.ts'), ts, 'utf-8');
  }

  /**
   * Generate Tailwind config
   */
  private async generateTailwindConfig(tokens: DesignTokenSystem): Promise<void> {
    const config = this.generateTailwindFromTokens(tokens);
    await fs.writeFile(path.join(this.outputDir, 'tailwind.tokens.js'), config, 'utf-8');
  }

  /**
   * Convert tokens to CSS variables
   */
  private generateCSSFromTokens(tokens: DesignTokenSystem): string {
    let css = ':root {\n';
    
    // Colors
    Object.entries(tokens.colors.primary).forEach(([shade, value]) => {
      css += `  --color-primary-${shade}: ${value};\n`;
    });
    
    // Typography
    Object.entries(tokens.typography.fontSizes).forEach(([size, value]) => {
      css += `  --font-size-${size}: ${value};\n`;
    });
    
    // Spacing
    Object.entries(tokens.spacing.scale).forEach(([size, value]) => {
      css += `  --spacing-${size}: ${value};\n`;
    });
    
    css += '}\n';
    return css;
  }

  /**
   * Convert tokens to SCSS variables
   */
  private generateSCSSFromTokens(tokens: DesignTokenSystem): string {
    let scss = '// Design Tokens\n\n';
    
    // Colors
    scss += '// Colors\n';
    Object.entries(tokens.colors.primary).forEach(([shade, value]) => {
      scss += `$color-primary-${shade}: ${value};\n`;
    });
    
    return scss;
  }

  /**
   * Convert tokens to JavaScript
   */
  private generateJSFromTokens(tokens: DesignTokenSystem): string {
    return `export const tokens = ${JSON.stringify(tokens, null, 2)};`;
  }

  /**
   * Convert tokens to TypeScript
   */
  private generateTSFromTokens(tokens: DesignTokenSystem): string {
    return `import { DesignTokenSystem } from './types';

export const tokens: DesignTokenSystem = ${JSON.stringify(tokens, null, 2)};`;
  }

  /**
   * Convert tokens to Tailwind config
   */
  private generateTailwindFromTokens(tokens: DesignTokenSystem): string {
    const config = {
      theme: {
        extend: {
          colors: tokens.colors,
          fontFamily: tokens.typography.fontFamilies,
          fontSize: tokens.typography.fontSizes,
          spacing: tokens.spacing.scale
        }
      }
    };
    
    return `module.exports = ${JSON.stringify(config, null, 2)};`;
  }
}

// Supporting interfaces
interface TokenDocumentationOptions {
  previousVersion?: string;
}