/**
 * WixCalendar Component
 * 
 * OVERVIEW:
 * This component provides an embedded scheduling interface for 702 Hockey training sessions.
 * It integrates with an external Wix-based calendar system through an iframe, providing
 * a seamless booking experience within the main application.
 * 
 * ARCHITECTURE:
 * - Uses iframe embedding to display external Wix calendar
 * - Implements loading state management for better UX
 * - Provides responsive design with Tailwind CSS
 * - Includes security sandbox restrictions for iframe content
 * 
 * TECHNICAL DECISIONS:
 * 1. Iframe Approach: Chosen to integrate with existing Wix booking system
 *    without requiring API integration or rebuilding booking functionality
 * 2. Loading State: Prevents layout shift and provides user feedback
 * 3. Sandbox Security: Restricts iframe capabilities for security
 * 4. Responsive Design: Ensures calendar works across all device sizes
 * 
 * DEPENDENCIES:
 * - React (hooks: useState)
 * - Tailwind CSS for styling
 * - External Wix calendar at https://www.702hockey.com/weekly-calendar
 * 
 * MAINTENANCE NOTES:
 * - Monitor iframe source URL for availability
 * - Update sandbox permissions if Wix calendar requires additional capabilities
 * - Consider implementing error handling for iframe load failures
 * - Test across browsers for iframe compatibility
 * 
 * <AUTHOR> Hockey Development Team
 * @version 1.0.0
 * @since 2024
 */

// SECTION: Imports
// ==================================================================================
import React, { useState, useEffect } from "react";

// SECTION: Type Definitions
// ==================================================================================
/**
 * Component props interface (currently no props required)
 * Future enhancement: Could accept calendar configuration props
 */
type WixCalendarProps = Record<string, never>;
  // Reserved for future props like:
  // calendarUrl?: string;
  // theme?: 'light' | 'dark';
  // height?: string;

// SECTION: WixCalendar Component
// ==================================================================================
/**
 * WixCalendar - Embedded scheduling interface component
 * 
 * This component renders an iframe containing the Wix-based booking calendar
 * for 702 Hockey training sessions. It manages loading states and provides
 * a responsive container for the external calendar interface.
 * 
 * COMPONENT LIFECYCLE:
 * 1. Component mounts with loading state = true
 * 2. Iframe begins loading external calendar
 * 3. onLoad event fires when calendar is ready
 * 4. Loading state updates to false, revealing calendar
 * 
 * STYLING APPROACH:
 * - Uses Tailwind utility classes for responsive design
 * - Implements smooth opacity transitions for loading states
 * - Calculates dynamic heights to fill viewport appropriately
 * - Mobile-first responsive design with touch optimizations
 * 
 * SECURITY CONSIDERATIONS:
 * - Iframe sandbox restricts potentially harmful operations
 * - Only allows necessary permissions for calendar functionality
 * - External URL is hardcoded to prevent injection attacks
 */
const WixCalendar: React.FC<WixCalendarProps> = () => {
  // SECTION: Responsive Height Calculation
  // ==================================================================================
  
  /**
   * Calculate responsive iframe height based on screen size
   * 
   * PURPOSE: Provides optimal calendar height for different devices
   * MOBILE: Smaller header, more calendar space
   * TABLET: Balanced header and calendar proportions
   * DESKTOP: Full header with maximum calendar visibility
   */
  const getResponsiveHeight = (): React.CSSProperties => {
    return {
      height: 'calc(100vh - 120px)',
      minHeight: '500px',
      touchAction: 'manipulation',
      WebkitOverflowScrolling: 'touch'
    };
  };
  // SECTION: State Management
  // ==================================================================================
  
  /**
   * Loading state management
   * 
   * PURPOSE: Tracks whether the iframe content has finished loading
   * INITIAL VALUE: true (assumes content is loading on mount)
   * UPDATES: Set to false when iframe onLoad event fires
   * 
   * UX BENEFIT: Prevents showing empty iframe while content loads
   * TECHNICAL BENEFIT: Allows for smooth transition animations
   */
  const [isLoading, setIsLoading] = useState<boolean>(true);

  /**
   * Mobile viewport state management
   * 
   * PURPOSE: Tracks mobile device state for touch optimizations
   * UPDATES: Set based on window width and touch capability detection
   */
  const [isMobile, setIsMobile] = useState<boolean>(false);

  // SECTION: Effects
  // ==================================================================================
  
  /**
   * Mobile detection and viewport optimization
   * 
   * PURPOSE: Detects mobile devices and optimizes calendar behavior
   * TRIGGERS: On component mount and window resize
   * OPTIMIZATIONS: Touch interactions, viewport handling, scroll behavior
   */
  useEffect(() => {
    const checkMobile = () => {
      const isMobileDevice = window.innerWidth < 768 || 
                           ('ontouchstart' in window) || 
                           (navigator.maxTouchPoints > 0);
      setIsMobile(isMobileDevice);
    };

    // Initial check
    checkMobile();

    // Listen for viewport changes
    const handleResize = () => {
      checkMobile();
    };

    const handleOrientationChange = () => {
      // Delay to allow for orientation change to complete
      setTimeout(checkMobile, 100);
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, []);

  // SECTION: Event Handlers
  // ==================================================================================
  
  /**
   * Handles iframe load completion
   * 
   * TRIGGER: Fired when iframe content finishes loading
   * ACTION: Updates loading state to hide loading indicator
   * 
   * IMPLEMENTATION NOTES:
   * - Uses React's SyntheticEvent system
   * - Automatically called by browser when iframe content is ready
   * - Critical for UX as it determines when to show actual calendar
   * 
   * POTENTIAL ENHANCEMENTS:
   * - Add error handling for load failures
   * - Implement timeout for slow-loading content
   * - Add retry mechanism for failed loads
   */
  const handleIframeLoad = (): void => {
    setIsLoading(false);
  };

  // SECTION: Component Render
  // ==================================================================================
  
  /**
   * Component JSX Structure
   * 
   * LAYOUT HIERARCHY:
   * 1. Main Container - Responsive with mobile-first design
   * 2. Header Section - Mobile-optimized title and description
   * 3. Loading Indicator - Touch-friendly spinner
   * 4. Iframe Container - Responsive calendar content
   * 
   * RESPONSIVE DESIGN:
   * - Mobile-first approach with progressive enhancement
   * - Touch-friendly minimum 44px targets
   * - Responsive typography and spacing
   * - Optimized iframe sizing for all devices
   * 
   * ACCESSIBILITY FEATURES:
   * - Semantic HTML structure with proper headings
   * - Descriptive iframe title for screen readers
   * - High contrast colors for readability
   * - Loading state provides user feedback
   * - Touch-friendly interactions
   */
  return (
    <div className="w-full bg-elev-background-muted">
      {/* 
        MOBILE-OPTIMIZED HEADER SECTION
        ===============================
        
        PURPOSE: Provides context and branding for the calendar interface
        
        RESPONSIVE DESIGN DECISIONS:
        - Mobile-first typography scaling (text-xl → text-2xl → text-3xl)
        - Responsive padding (px-3 py-4 → px-4 py-5 → px-6 py-6)
        - Improved line height for mobile readability
        - Reduced spacing on small screens for better space utilization
        
        MOBILE OPTIMIZATIONS:
        - Smaller font sizes on mobile for better fit
        - Reduced padding to maximize calendar space
        - Better text contrast and readability
        - Touch-friendly spacing between elements
      */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-3 py-4 sm:px-4 sm:py-5 lg:px-6 lg:py-6">
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-elev-text-primary mb-2 leading-tight">
            Schedule Your Training
          </h1>
          <p className="text-sm sm:text-base text-elev-text-secondary leading-relaxed">
            Book your hockey training sessions directly through our integrated scheduling system.
          </p>
        </div>
      </div>

      {/* 
        MOBILE-OPTIMIZED LOADING INDICATOR SECTION
        ==========================================
        
        PURPOSE: Provides visual feedback during iframe content loading
        
        MOBILE OPTIMIZATIONS:
        - Responsive padding (py-8 → py-10 → py-12)
        - Touch-friendly spinner size (h-10 w-10 → h-12 w-12)
        - Responsive text sizing for better mobile readability
        - Improved spacing between spinner and text
        
        UX CONSIDERATIONS:
        - Prevents blank space during load time
        - Spinner animation indicates active loading
        - Text provides clear status message
        - Centered layout maintains visual balance
        - Better mobile touch target sizing
        
        ACCESSIBILITY:
        - Text provides context for screen readers
        - Visual spinner for sighted users
        - Non-intrusive positioning
        - Adequate contrast ratios
      */}
      {isLoading && (
        <div className="flex flex-col sm:flex-row items-center justify-center py-8 sm:py-10 lg:py-12 px-4">
          <div className="animate-spin rounded-full h-10 w-10 sm:h-12 sm:w-12 border-b-2 border-blue-600 mb-3 sm:mb-0"></div>
          <span className="text-sm sm:text-base text-elev-text-secondary sm:ml-3 text-center">
            Loading scheduling calendar...
          </span>
        </div>
      )}

      {/* 
        MOBILE-OPTIMIZED IFRAME CONTAINER SECTION
        =========================================
        
        PURPOSE: Embeds external Wix calendar within application with mobile optimization
        
        MOBILE-RESPONSIVE DESIGN:
        - Dynamic height calculations for different screen sizes
        - Touch-friendly iframe interactions
        - Optimized spacing for mobile devices
        - Better viewport utilization on small screens
        
        CONTAINER DESIGN:
        - relative positioning for potential overlay elements
        - Responsive height calculation to fill remaining viewport
        - Mobile-first approach with progressive enhancement
        - Touch-optimized interactions
        
        IFRAME CONFIGURATION:
        
        SRC: https://www.702hockey.com/weekly-calendar
        - External Wix-hosted calendar interface
        - Provides full booking functionality
        - Maintained by Wix platform
        - Mobile-responsive calendar interface
        
        SECURITY SANDBOX:
        - allow-same-origin: Enables proper iframe functionality
        - allow-scripts: Required for calendar interactivity
        - allow-forms: Enables booking form submissions
        - allow-popups: Supports calendar popup windows
        - allow-top-navigation: Allows navigation within calendar
        - allow-pointer-lock: Enables touch interactions
        
        MOBILE OPTIMIZATIONS:
        - Responsive height calculations for different screen sizes
        - Touch-friendly minimum heights (500px mobile, 600px tablet, 700px desktop)
        - Better viewport calculations accounting for mobile browser UI
        - Smooth transitions optimized for touch devices
        
        HEIGHT CALCULATIONS:
        - Mobile: calc(100vh - 120px) with 500px minimum
        - Tablet: calc(100vh - 130px) with 600px minimum  
        - Desktop: calc(100vh - 140px) with 700px minimum
        - Accounts for responsive header heights
        
        ACCESSIBILITY:
        - title attribute describes iframe content
        - allow="fullscreen" enables accessibility features
        - frameBorder="0" removes visual border
        - Touch-friendly interactions
        - Screen reader compatible
      */}
      <div className="relative">
        {/* Mobile-specific helper text */}
        {isMobile && (
          <div className="bg-blue-50 border-l-4 border-blue-400 p-3 mb-2 sm:hidden">
            <div className="flex">
              <div className="ml-3">
                <p className="text-sm text-blue-700">
                  💡 Tip: Rotate your device to landscape mode for the best calendar viewing experience.
                </p>
              </div>
            </div>
          </div>
        )}
        
        <iframe
          src="https://www.702hockey.com/weekly-calendar"
          frameBorder="0"
          className={`w-full transition-opacity duration-300 rounded-none sm:rounded-t-lg ${
            isLoading ? 'opacity-0' : 'opacity-100'
          }`}
          style={getResponsiveHeight()}
          onLoad={handleIframeLoad}
          title="702 Hockey Scheduling Calendar - Mobile Optimized"
          allow="fullscreen; pointer-lock"
          sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation allow-pointer-lock"
        ></iframe>
      </div>
    </div>
  );
};

// SECTION: Component Export & Usage Documentation
// ==================================================================================

/**
 * COMPONENT EXPORT
 * ================
 * 
 * Default export of the WixCalendar component for use throughout the application.
 * 
 * USAGE EXAMPLES:
 * 
 * Basic Implementation:
 * ```tsx
 * import WixCalendar from '@/components/WixCalendar';
 * 
 * function SchedulePage() {
 *   return (
 *     <div>
 *       <WixCalendar />
 *     </div>
 *   );
 * }
 * ```
 * 
 * With Error Boundary (Recommended):
 * ```tsx
 * import { ErrorBoundary } from 'react-error-boundary';
 * import WixCalendar from '@/components/WixCalendar';
 * 
 * function SchedulePage() {
 *   return (
 *     <ErrorBoundary fallback={<div>Calendar unavailable</div>}>
 *       <WixCalendar />
 *     </ErrorBoundary>
 *   );
 * }
 * ```
 * 
 * INTEGRATION NOTES:
 * 
 * 1. ROUTING SETUP:
 *    - Component should be used on dedicated calendar/schedule routes
 *    - Consider lazy loading for performance optimization
 *    - Ensure proper route protection if authentication required
 * 
 * 2. LAYOUT CONSIDERATIONS:
 *    - Component assumes full-page layout (min-h-screen)
 *    - Works best without additional page headers/footers
 *    - Consider viewport meta tag for mobile optimization
 * 
 * 3. PERFORMANCE OPTIMIZATION:
 *    - Iframe content loads asynchronously
 *    - Consider preloading calendar URL for faster initial load
 *    - Monitor Core Web Vitals impact of iframe embedding
 * 
 * 4. ERROR HANDLING:
 *    - Implement error boundaries for iframe load failures
 *    - Consider fallback UI for network connectivity issues
 *    - Monitor iframe availability and provide alternatives
 * 
 * 5. TESTING CONSIDERATIONS:
 *    - Mock iframe interactions in unit tests
 *    - Test loading states and transitions
 *    - Verify responsive behavior across devices
 *    - Test accessibility features with screen readers
 * 
 * FUTURE ENHANCEMENTS:
 * 
 * 1. CONFIGURATION OPTIONS:
 *    - Accept calendar URL as prop for environment flexibility
 *    - Theme customization props for brand consistency
 *    - Height/sizing configuration options
 * 
 * 2. ADVANCED FEATURES:
 *    - Error retry mechanisms for failed loads
 *    - Offline detection and messaging
 *    - Calendar event integration with parent application
 *    - Custom loading animations or branding
 * 
 * 3. ANALYTICS INTEGRATION:
 *    - Track calendar interaction events
 *    - Monitor booking completion rates
 *    - Measure user engagement with scheduling interface
 * 
 * TROUBLESHOOTING:
 * 
 * Common Issues:
 * - Iframe not loading: Check network connectivity and URL availability
 * - Security errors: Verify sandbox permissions match Wix requirements
 * - Layout issues: Ensure parent containers don't conflict with full-screen design
 * - Mobile problems: Test viewport meta tags and responsive behavior
 * 
 * Browser Compatibility:
 * - Modern browsers: Full functionality
 * - IE11: Limited support, consider polyfills
 * - Mobile Safari: Test iframe behavior and touch interactions
 * - Chrome/Firefox: Optimal performance and feature support
 * 
 * MAINTENANCE CHECKLIST:
 * 
 * Regular Tasks:
 * □ Verify external calendar URL accessibility
 * □ Test iframe loading across different browsers
 * □ Monitor console for security or loading errors
 * □ Validate responsive design on new device sizes
 * □ Check accessibility compliance with latest standards
 * 
 * Quarterly Reviews:
 * □ Evaluate iframe security sandbox permissions
 * □ Review loading performance and optimization opportunities
 * □ Assess user feedback and usage analytics
 * □ Consider updates to Wix calendar integration
 * □ Update documentation with any discovered patterns or issues
 */

export default WixCalendar;
