import { ComponentVariantAnalyzer } from '../component-variant-analyzer';
import { FileInfo, IssueType, Severity, Category } from '../../types';

describe('ComponentVariantAnalyzer', () => {
  let analyzer: ComponentVariantAnalyzer;

  beforeEach(() => {
    analyzer = new ComponentVariantAnalyzer();
  });

  describe('analyze', () => {
    it('should detect mixed styling approaches in components', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
import React from 'react';
import styles from './Button.module.css';
import styled from 'styled-components';

const StyledButton = styled.button\`
  background: blue;
\`;

export const Button = ({ variant, children }) => {
  return (
    <button 
      className={styles.button}
      style={{ color: 'red' }}
    >
      {children}
    </button>
  );
};
          `,
          extension: '.tsx',
          size: 500,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues.length).toBeGreaterThan(0);
      expect(result.issues[0].type).toBe(IssueType.COMPONENT_INCONSISTENCY);
      expect(result.issues[0].severity).toBe(Severity.HIGH);
      expect(result.issues[0].description).toContain('different styling approaches');
    });

    it('should detect inconsistent component styling patterns', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
import styles from './Button.module.css';
import styled from 'styled-components';

const StyledButton = styled.button\`
  background: blue;
\`;

export const Button = ({ variant, size }) => {
  return <button className={styles.button} style={{ color: 'red' }}>Click me</button>;
};
          `,
          extension: '.tsx',
          size: 300,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues.length).toBeGreaterThan(0);
      const stylingIssue = result.issues.find(issue => 
        issue.description.includes('different styling approaches')
      );
      expect(stylingIssue).toBeDefined();
      expect(stylingIssue?.severity).toBe(Severity.HIGH);
    });

    it('should detect mixed component state implementations', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Counter.tsx',
          content: `
import React, { useState } from 'react';
import styles from './Counter.module.css';
import styled from 'styled-components';

const StyledDiv = styled.div\`
  padding: 10px;
\`;

export const Counter = ({ initialValue }) => {
  const [count, setCount] = useState(initialValue);
  return <div className={styles.counter} style={{ padding: '10px' }}>{count}</div>;
};
          `,
          extension: '.tsx',
          size: 300,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues.length).toBeGreaterThan(0);
      const stateIssue = result.issues.find(issue => 
        issue.description.includes('different styling approaches')
      );
      expect(stateIssue).toBeDefined();
    });

    it('should analyze component usage patterns', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/pages/HomePage.tsx',
          content: `
import { Button } from '../components/Button';

export const HomePage = () => {
  return (
    <div>
      <Button variant="primary" size="large">Submit</Button>
      <Button variant="secondary">Cancel</Button>
      <Button size="small" color="red">Delete</Button>
      <Button variant="primary" size="large" disabled>Loading</Button>
      <Button variant="outline" size="medium">Edit</Button>
    </div>
  );
};
          `,
          extension: '.tsx',
          size: 400,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      // Should detect usage pattern inconsistencies
      expect(result.issues.length).toBeGreaterThan(0);
      const usageIssue = result.issues.find(issue => 
        issue.description.includes('usage patterns')
      );
      expect(usageIssue).toBeDefined();
    });

    it('should detect inconsistent prop usage across component variants', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Card.tsx',
          content: `
import styles from './Card.module.css';
import styled from 'styled-components';

const StyledCard = styled.div\`
  padding: 10px;
\`;

export const Card = ({ title, content, variant }) => {
  return <div className={styles.card} style={{ padding: '10px' }}>{title}: {content}</div>;
};
          `,
          extension: '.tsx',
          size: 250,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues.length).toBeGreaterThan(0);
      const propIssue = result.issues.find(issue => 
        issue.description.includes('different styling approaches')
      );
      expect(propIssue).toBeDefined();
    });

    it('should identify underutilized styling approaches', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
import styles from './Button.module.css';
export const Button = () => <button className={styles.button}>Click</button>;
          `,
          extension: '.tsx',
          size: 150,
          lastModified: new Date()
        },
        {
          path: 'src/components/Input.tsx',
          content: `
import styles from './Input.module.css';
export const Input = () => <input className={styles.input} />;
          `,
          extension: '.tsx',
          size: 150,
          lastModified: new Date()
        },
        {
          path: 'src/components/Card.tsx',
          content: `
import styles from './Card.module.css';
export const Card = () => <div className={styles.card}>Card</div>;
          `,
          extension: '.tsx',
          size: 150,
          lastModified: new Date()
        },
        {
          path: 'src/components/Modal.tsx',
          content: `
import styles from './Modal.module.css';
export const Modal = () => <div className={styles.modal}>Modal</div>;
          `,
          extension: '.tsx',
          size: 150,
          lastModified: new Date()
        },
        {
          path: 'src/components/Alert.tsx',
          content: `
export const Alert = () => <div style={{ padding: '10px', background: 'red' }}>Alert</div>;
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        },
        {
          path: 'src/components/Badge.tsx',
          content: `
export const Badge = () => <span style={{ fontSize: '12px' }}>Badge</span>;
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      const underutilizedIssue = result.issues.find(issue => 
        issue.description.includes('underutilized')
      );
      expect(underutilizedIssue).toBeDefined();
      expect(underutilizedIssue?.severity).toBe(Severity.MEDIUM);
    });

    it('should handle files without components gracefully', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/utils/helpers.ts',
          content: `
export const formatDate = (date: Date) => {
  return date.toISOString();
};
          `,
          extension: '.ts',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues).toHaveLength(0);
      expect(result.summary.totalIssues).toBe(0);
    });

    it('should exclude test and story files from analysis', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.test.tsx',
          content: `
export const Button = () => <button>Test Button</button>;
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        },
        {
          path: 'src/components/Button.stories.tsx',
          content: `
export const Button = () => <button>Story Button</button>;
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues).toHaveLength(0);
    });

    it('should provide accurate severity assessment', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
import styles from './Button.module.css';
import styled from 'styled-components';

export const Button = ({ variant }) => {
  return <button className={styles.button} style={{ color: 'red' }}>Click</button>;
};
          `,
          extension: '.tsx',
          size: 200,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      const highSeverityIssues = result.issues.filter(issue => issue.severity === Severity.HIGH);
      const mediumSeverityIssues = result.issues.filter(issue => issue.severity === Severity.MEDIUM);

      expect(highSeverityIssues.length).toBeGreaterThan(0);
      expect(result.summary.severityBreakdown[Severity.HIGH]).toBeGreaterThan(0);
    });

    it('should categorize issues correctly', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
export const Button = ({ variant, size }) => {
  return <button className="btn">Click me</button>;
};
          `,
          extension: '.tsx',
          size: 150,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      result.issues.forEach(issue => {
        expect(issue.category).toBe(Category.COMPONENT);
      });

      expect(result.summary.categoryBreakdown[Category.COMPONENT]).toBe(result.issues.length);
    });

    it('should provide actionable suggestions', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
import styles from './Button.module.css';
export const Button = ({ variant }) => {
  return <button className={styles.button} style={{ padding: '10px' }}>Click</button>;
};
          `,
          extension: '.tsx',
          size: 200,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      result.issues.forEach(issue => {
        expect(issue.suggestion).toBeTruthy();
        expect(issue.suggestion.length).toBeGreaterThan(10);
      });
    });

    it('should track execution time', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `export const Button = () => <button>Click</button>;`,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.executionTime).toBeGreaterThan(0);
      expect(typeof result.executionTime).toBe('number');
    });
  });

  describe('getSeverity', () => {
    it('should return HIGH severity for mixed styling approaches', () => {
      const issue = {
        id: 'test',
        type: IssueType.COMPONENT_INCONSISTENCY,
        category: Category.COMPONENT,
        description: 'Component uses mixed styling approaches',
        location: { filePath: 'test.tsx', lineNumber: 1, columnNumber: 0, context: '' },
        suggestion: 'Fix it',
        autoFixable: false,
        impact: { userExperience: 5, maintenanceEffort: 5, implementationComplexity: 5 }
      };

      const severity = analyzer.getSeverity(issue);
      expect(severity).toBe(Severity.HIGH);
    });

    it('should return MEDIUM severity for usage pattern issues', () => {
      const issue = {
        id: 'test',
        type: IssueType.COMPONENT_INCONSISTENCY,
        category: Category.COMPONENT,
        description: 'Component has inconsistent usage patterns',
        location: { filePath: 'test.tsx', lineNumber: 1, columnNumber: 0, context: '' },
        suggestion: 'Fix it',
        autoFixable: false,
        impact: { userExperience: 5, maintenanceEffort: 5, implementationComplexity: 5 }
      };

      const severity = analyzer.getSeverity(issue);
      expect(severity).toBe(Severity.MEDIUM);
    });

    it('should return LOW severity for missing prop issues', () => {
      const issue = {
        id: 'test',
        type: IssueType.COMPONENT_INCONSISTENCY,
        category: Category.COMPONENT,
        description: 'Prop is missing prop in some instances',
        location: { filePath: 'test.tsx', lineNumber: 1, columnNumber: 0, context: '' },
        suggestion: 'Fix it',
        autoFixable: false,
        impact: { userExperience: 5, maintenanceEffort: 5, implementationComplexity: 5 }
      };

      const severity = analyzer.getSeverity(issue);
      expect(severity).toBe(Severity.LOW);
    });
  });
});