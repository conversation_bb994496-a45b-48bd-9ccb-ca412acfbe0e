import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ConsistencyMonitor } from '../consistency-monitor';
import { MonitoringConfig, Issue } from '../types';
import { AuditEngine } from '../../core/audit-engine';
import * as fs from 'fs/promises';

// Mock dependencies
vi.mock('fs/promises');
vi.mock('../../core/audit-engine');
vi.mock('../../core/logger', () => ({
  AuditLogger: vi.fn().mockImplementation(() => ({
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn()
  }))
}));

describe('ConsistencyMonitor', () => {
  let monitor: ConsistencyMonitor;
  let mockAuditEngine: vi.Mocked<AuditEngine>;
  let config: MonitoringConfig;

  beforeEach(() => {
    config = {
      enabled: true,
      checkInterval: 5000,
      thresholds: {
        errorCount: 10,
        warningCount: 20,
        criticalSeverityLimit: 2
      },
      notifications: {
        webhook: 'https://example.com/webhook'
      },
      cicd: {
        failOnErrors: true,
        failOnWarnings: false,
        generateReports: true,
        reportPath: './reports'
      }
    };

    mockAuditEngine = {
      scanProject: vi.fn()
    } as any;

    monitor = new ConsistencyMonitor(config, mockAuditEngine);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('performCheck', () => {
    it('should perform audit and compare results', async () => {
      const mockIssues: Issue[] = [
        {
          id: '1',
          type: 'color-inconsistency',
          severity: 'medium',
          category: 'visual',
          description: 'Hardcoded color found',
          location: { filePath: 'test.tsx', lineNumber: 10, columnNumber: 5 },
          hash: 'hash1'
        }
      ];

      mockAuditEngine.scanProject.mockResolvedValue({
        issues: mockIssues,
        summary: {},
        recommendations: [],
        remediationPlan: { tasks: [], dependencies: [], estimatedEffort: 0, priority: 'medium' }
      } as any);

      const result = await monitor.performCheck();

      expect(result.totalIssues).toBe(1);
      expect(result.newIssues).toHaveLength(1);
      expect(result.resolvedIssues).toHaveLength(0);
      expect(result.complianceScore).toBeGreaterThan(0);
    });

    it('should detect new and resolved issues', async () => {
      // First run with initial issues
      const initialIssues: Issue[] = [
        {
          id: '1',
          type: 'color-inconsistency',
          severity: 'medium',
          category: 'visual',
          description: 'Issue 1',
          location: { filePath: 'test1.tsx', lineNumber: 10, columnNumber: 5 },
          hash: 'hash1'
        }
      ];

      mockAuditEngine.scanProject.mockResolvedValue({
        issues: initialIssues,
        summary: {},
        recommendations: [],
        remediationPlan: { tasks: [], dependencies: [], estimatedEffort: 0, priority: 'medium' }
      } as any);

      await monitor.performCheck();

      // Second run with different issues
      const newIssues: Issue[] = [
        {
          id: '2',
          type: 'spacing-inconsistency',
          severity: 'low',
          category: 'visual',
          description: 'Issue 2',
          location: { filePath: 'test2.tsx', lineNumber: 15, columnNumber: 8 },
          hash: 'hash2'
        }
      ];

      mockAuditEngine.scanProject.mockResolvedValue({
        issues: newIssues,
        summary: {},
        recommendations: [],
        remediationPlan: { tasks: [], dependencies: [], estimatedEffort: 0, priority: 'medium' }
      } as any);

      const result = await monitor.performCheck();

      expect(result.newIssues).toHaveLength(1);
      expect(result.resolvedIssues).toHaveLength(1);
      expect(result.newIssues[0].description).toBe('Issue 2');
      expect(result.resolvedIssues[0].description).toBe('Issue 1');
    });

    it('should generate alerts when thresholds are exceeded', async () => {
      const manyIssues: Issue[] = Array.from({ length: 15 }, (_, i) => ({
        id: `${i}`,
        type: 'test-issue',
        severity: 'medium' as const,
        category: 'test',
        description: `Issue ${i}`,
        location: { filePath: `test${i}.tsx`, lineNumber: 10, columnNumber: 5 },
        hash: `hash${i}`
      }));

      mockAuditEngine.scanProject.mockResolvedValue({
        issues: manyIssues,
        summary: {},
        recommendations: [],
        remediationPlan: { tasks: [], dependencies: [], estimatedEffort: 0, priority: 'medium' }
      } as any);

      // Mock fetch for webhook
      global.fetch = vi.fn().mockResolvedValue({ ok: true });

      const result = await monitor.performCheck();

      expect(result.totalIssues).toBe(15);
      expect(global.fetch).toHaveBeenCalled();
    });
  });

  describe('startMonitoring and stopMonitoring', () => {
    it('should start and stop monitoring interval', () => {
      vi.useFakeTimers();
      
      monitor.startMonitoring();
      
      // Fast-forward time to trigger interval
      vi.advanceTimersByTime(5000);
      
      monitor.stopMonitoring();
      
      vi.useRealTimers();
    });

    it('should not start monitoring when disabled', () => {
      config.enabled = false;
      monitor = new ConsistencyMonitor(config, mockAuditEngine);
      
      monitor.startMonitoring();
      
      // Should not have called scanProject
      expect(mockAuditEngine.scanProject).not.toHaveBeenCalled();
    });
  });

  describe('getComplianceMetrics', () => {
    it('should return compliance metrics', async () => {
      // Mock fs.readdir and fs.readFile
      (fs.readdir as any).mockResolvedValue(['monitoring-2024-01-01.json']);
      (fs.readFile as any).mockResolvedValue(JSON.stringify([{
        timestamp: new Date(),
        complianceScore: 85,
        totalIssues: 5,
        severityBreakdown: { low: 3, medium: 2, high: 0, critical: 0 }
      }]));

      const metrics = await monitor.getComplianceMetrics();

      expect(metrics.compliancePercentage).toBe(85);
      expect(metrics.trendData).toHaveLength(1);
    });
  });

  describe('alert generation', () => {
    it('should generate critical alert for critical issues', async () => {
      const criticalIssues: Issue[] = Array.from({ length: 5 }, (_, i) => ({
        id: `${i}`,
        type: 'critical-issue',
        severity: 'critical' as const,
        category: 'critical',
        description: `Critical issue ${i}`,
        location: { filePath: `test${i}.tsx`, lineNumber: 10, columnNumber: 5 },
        hash: `hash${i}`
      }));

      mockAuditEngine.scanProject.mockResolvedValue({
        issues: criticalIssues,
        summary: {},
        recommendations: [],
        remediationPlan: { tasks: [], dependencies: [], estimatedEffort: 0, priority: 'high' }
      } as any);

      global.fetch = vi.fn().mockResolvedValue({ ok: true });

      const result = await monitor.performCheck();

      expect(result.severityBreakdown.critical).toBe(5);
      expect(global.fetch).toHaveBeenCalled();
    });
  });

  describe('trend calculation', () => {
    it('should calculate improving trend', async () => {
      // First run with more issues
      const moreIssues: Issue[] = Array.from({ length: 10 }, (_, i) => ({
        id: `${i}`,
        type: 'test-issue',
        severity: 'medium' as const,
        category: 'test',
        description: `Issue ${i}`,
        location: { filePath: `test${i}.tsx`, lineNumber: 10, columnNumber: 5 },
        hash: `hash${i}`
      }));

      mockAuditEngine.scanProject.mockResolvedValue({
        issues: moreIssues,
        summary: {},
        recommendations: [],
        remediationPlan: { tasks: [], dependencies: [], estimatedEffort: 0, priority: 'medium' }
      } as any);

      await monitor.performCheck();

      // Second run with fewer issues
      const fewerIssues: Issue[] = Array.from({ length: 5 }, (_, i) => ({
        id: `${i}`,
        type: 'test-issue',
        severity: 'medium' as const,
        category: 'test',
        description: `Issue ${i}`,
        location: { filePath: `test${i}.tsx`, lineNumber: 10, columnNumber: 5 },
        hash: `hash${i}`
      }));

      mockAuditEngine.scanProject.mockResolvedValue({
        issues: fewerIssues,
        summary: {},
        recommendations: [],
        remediationPlan: { tasks: [], dependencies: [], estimatedEffort: 0, priority: 'medium' }
      } as any);

      const result = await monitor.performCheck();

      expect(result.trend).toBe('improving');
    });
  });
});