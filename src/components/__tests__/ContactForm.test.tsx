import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import ContactForm from '../ContactForm';

// Mock the hooks
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}));

vi.mock('@/hooks/use-reveal-on-scroll', () => ({
  useRevealOnScroll: () => ({
    ref: { current: null },
    isVisible: true,
  }),
}));

describe('ContactForm Responsive Tests', () => {
  it('renders form with proper touch targets for mobile', () => {
    render(<ContactForm />);
    
    // Check that input fields have minimum 44px height for touch targets
    const athleteNameInput = screen.getByLabelText(/athlete name/i);
    const emailInput = screen.getByLabelText(/email address/i);
    const phoneInput = screen.getByLabelText(/phone number/i);
    const ageInput = screen.getByLabelText(/athlete age/i);
    const submitButton = screen.getByRole('button', { name: /schedule your assessment/i });
    
    // Check that elements exist
    expect(athleteNameInput).toBeInTheDocument();
    expect(emailInput).toBeInTheDocument();
    expect(phoneInput).toBeInTheDocument();
    expect(ageInput).toBeInTheDocument();
    expect(submitButton).toBeInTheDocument();
    
    // Check that inputs have proper classes for responsive sizing
    expect(athleteNameInput).toHaveClass('min-h-[44px]');
    expect(emailInput).toHaveClass('min-h-[44px]');
    expect(phoneInput).toHaveClass('min-h-[44px]');
    expect(ageInput).toHaveClass('min-h-[44px]');
  });

  it('displays validation errors properly on mobile', async () => {
    render(<ContactForm />);
    
    const submitButton = screen.getByRole('button', { name: /schedule your assessment/i });
    
    // Try to submit empty form
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      // Check that validation errors are displayed
      expect(screen.getByText(/athlete name is required/i)).toBeInTheDocument();
      expect(screen.getByText(/email address is required/i)).toBeInTheDocument();
      expect(screen.getByText(/phone number is required/i)).toBeInTheDocument();
      expect(screen.getByText(/age or birth year is required/i)).toBeInTheDocument();
      expect(screen.getByText(/please select a service/i)).toBeInTheDocument();
    });
  });

  it('clears validation errors when user starts typing', async () => {
    render(<ContactForm />);
    
    const athleteNameInput = screen.getByLabelText(/athlete name/i);
    const submitButton = screen.getByRole('button', { name: /schedule your assessment/i });
    
    // Submit empty form to trigger validation
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/athlete name is required/i)).toBeInTheDocument();
    });
    
    // Start typing in the field
    fireEvent.change(athleteNameInput, { target: { value: 'John Doe' } });
    
    // Error should be cleared
    await waitFor(() => {
      expect(screen.queryByText(/athlete name is required/i)).not.toBeInTheDocument();
    });
  });

  it('validates email format correctly', async () => {
    render(<ContactForm />);
    
    const emailInput = screen.getByLabelText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /schedule your assessment/i });
    
    // Enter invalid email
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument();
    });
    
    // Enter valid email
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    
    await waitFor(() => {
      expect(screen.queryByText(/please enter a valid email address/i)).not.toBeInTheDocument();
    });
  });

  it('validates phone format correctly', async () => {
    render(<ContactForm />);
    
    const phoneInput = screen.getByLabelText(/phone number/i);
    const submitButton = screen.getByRole('button', { name: /schedule your assessment/i });
    
    // Enter invalid phone
    fireEvent.change(phoneInput, { target: { value: '123' } });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/please enter a valid phone number/i)).toBeInTheDocument();
    });
    
    // Enter valid phone
    fireEvent.change(phoneInput, { target: { value: '(*************' } });
    
    await waitFor(() => {
      expect(screen.queryByText(/please enter a valid phone number/i)).not.toBeInTheDocument();
    });
  });

  it('has proper responsive grid layout classes', () => {
    render(<ContactForm />);
    
    // Check that form uses responsive grid classes
    const formContainer = screen.getByRole('form');
    expect(formContainer.closest('.grid')).toHaveClass('grid-cols-1', 'lg:grid-cols-2');
  });

  it('has proper responsive text sizing', () => {
    render(<ContactForm />);
    
    // Check that labels have responsive text sizing
    const labels = screen.getAllByText(/\*/);
    labels.forEach(label => {
      const labelElement = label.closest('label');
      expect(labelElement).toHaveClass('text-sm');
    });
  });
});