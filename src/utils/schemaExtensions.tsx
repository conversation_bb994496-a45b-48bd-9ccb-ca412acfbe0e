import React from 'react';

// Common interface for schema components
interface SchemaBaseProps {
  url?: string;
  image?: string;
}

/**
 * Interface for Course schema 
 */
interface CourseSchemaProps extends SchemaBaseProps {
  name: string;
  description: string;
  provider: string;
  courseCode?: string;
  hasCourseInstance?: Array<{
    name: string;
    description?: string;
    courseMode?: string; 
    startDate?: string;
    endDate?: string;
    price?: number;
    priceCurrency?: string;
  }>;
}

/**
 * Generates JSON-LD schema markup for a course (training program)
 * Helps attract students looking for hockey training programs
 */
export function CourseSchema({
  name,
  description,
  provider,
  url,
  image,
  courseCode,
  hasCourseInstance
}: CourseSchemaProps) {
  const schema = {
    '@context': 'https://schema.org',
    '@type': 'Course',
    name,
    description,
    provider: {
      '@type': 'Organization',
      name: provider
    },
    ...(url && { url }),
    ...(image && { image }),
    ...(courseCode && { courseCode }),
    ...(hasCourseInstance && {
      hasCourseInstance: hasCourseInstance.map(instance => ({
        '@type': 'CourseInstance',
        name: instance.name,
        ...(instance.description && { description: instance.description }),
        ...(instance.courseMode && { courseMode: instance.courseMode }),
        ...(instance.startDate && { startDate: instance.startDate }),
        ...(instance.endDate && { endDate: instance.endDate }),
        ...(instance.price && {
          offers: {
            '@type': 'Offer',
            price: instance.price,
            priceCurrency: instance.priceCurrency || 'USD'
          }
        })
      }))
    })
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  );
}

/**
 * Interface for Product schema
 */
interface ProductSchemaProps extends SchemaBaseProps {
  name: string;
  description: string;
  brand?: string;
  reviews?: Array<{
    reviewRating: number;
    author: string;
    reviewBody?: string;
    datePublished?: string;
  }>;
  offers?: {
    price: number;
    priceCurrency: string;
    availability?: string;
    validFrom?: string;
  };
}

/**
 * Generates JSON-LD schema markup for products/services
 * Improves visibility for hockey training programs as products
 */
export function ProductSchema({
  name,
  description,
  url,
  image,
  brand = 'ELEV802 | 702HOCKEY',
  reviews,
  offers
}: ProductSchemaProps) {
  const schema = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name,
    description,
    ...(brand && { brand: { '@type': 'Brand', name: brand } }),
    ...(image && { image }),
    ...(url && { url }),
    ...(reviews && {
      review: reviews.map(review => ({
        '@type': 'Review',
        reviewRating: {
          '@type': 'Rating',
          ratingValue: review.reviewRating,
          bestRating: '5'
        },
        author: {
          '@type': 'Person',
          name: review.author
        },
        ...(review.reviewBody && { reviewBody: review.reviewBody }),
        ...(review.datePublished && { datePublished: review.datePublished })
      }))
    }),
    ...(offers && {
      offers: {
        '@type': 'Offer',
        price: offers.price,
        priceCurrency: offers.priceCurrency,
        ...(offers.availability && { availability: offers.availability }),
        ...(offers.validFrom && { validFrom: offers.validFrom })
      }
    })
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  );
}

/**
 * Interface for FAQPage schema 
 */
interface FAQSchemaProps {
  questions: Array<{
    question: string;
    answer: string;
  }>;
}

/**
 * Generates JSON-LD schema markup for an FAQ page
 * Important for getting FAQ rich results in search engines
 */
export function FAQSchema({ questions }: FAQSchemaProps) {
  const schema = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: questions.map(({ question, answer }) => ({
      '@type': 'Question',
      name: question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: answer
      }
    }))
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  );
}
