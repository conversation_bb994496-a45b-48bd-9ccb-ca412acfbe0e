// SECTION: Imports
// ==================================================================================
import { useEffect, useRef } from "react";
import { cn } from "@/lib/utils";
import styles from "./Facility.module.css";
import { getResponsiveTextSize, getResponsivePadding } from "@/utils/responsive-utilities";

// SECTION: Facility Component
// ==================================================================================
const Facility = () => {
  // --- Hooks ---
  const sectionRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // Effect for reveal animations
  useEffect(() => {
    const currentSectionRef = sectionRef.current;
    const currentImageRef = imageRef.current;
    const currentContentRef = contentRef.current;
    
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("active");
          }
        });
      },
      { threshold: 0.1 }
    );

    if (currentSectionRef) observer.observe(currentSectionRef);
    if (currentImageRef) observer.observe(currentImageRef);
    if (currentContentRef) observer.observe(currentContentRef);

    return () => {
      if (currentSectionRef) observer.unobserve(currentSectionRef);
      if (currentImageRef) observer.unobserve(currentImageRef);
      if (currentContentRef) observer.unobserve(currentContentRef);
    };
  }, []);

  // --- Component JSX ---
  return (
    <section className="section-padding bg-white" id="facility">
      <div className="container mx-auto">
        <div className="text-center mb-12 reveal" ref={sectionRef}>
          <h2 className={cn(
            "font-bold mb-4 text-elev-navy",
            getResponsiveTextSize("2xl", "3xl", "4xl")
          )}>
            Train On Real Ice
          </h2>
          <p className={cn(
            "text-elev-text-secondary max-w-3xl mx-auto",
            getResponsiveTextSize("base", "lg", "lg")
          )}>
            Train year-round in Las Vegas's premier hockey training center, featuring a custom ice rink and facility dedicated to skills training.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 items-center">
          <div
            ref={imageRef}
            className="reveal rounded-lg overflow-hidden shadow-xl h-[400px] lg:h-[500px]"
          >
            <div
              className={`w-full h-full ${styles.facilityImage}`}
              role="img"
              aria-label="Ice rink facility"
            />
          </div>

          <div ref={contentRef} className="reveal space-y-6">
            <div>
              <h3 className="text-2xl font-bold text-elev-navy mb-2">
                Custom Ice Rink <span className="text-elev-navy">Year-Round</span>
              </h3>
              <p className="text-elev-text-secondary mb-4">
                Our facility features a custom ice rink available 365 days a year, providing the perfect training environment regardless of the outdoor temperature. With controlled conditions and professional-grade ice, you'll have consistent access to top-quality training ice.
              </p>
            </div>

            <div>
              <h3 className="text-2xl font-bold text-elev-navy mb-2">
                Real Ice vs. Synthetic Ice
              </h3>
              <p className="text-elev-text-secondary mb-4">
                Training on real ice provides the most accurate simulation of game conditions. Movements, stops, turns, and puck handling all feel and respond as they would in competition, making it ideal for refining skills that need to translate directly to games.
              </p>
              <p className="text-elev-text-secondary mb-4">
                Synthetic ice can alter mechanics due to higher resistance. While this can help develop muscle strength and control, it requires players to adjust their technique when transitioning back to real ice.
              </p>
            </div>

            <div>
              <h3 className="text-2xl font-bold text-elev-navy mb-2">
                Premier Hockey Destination
              </h3>
              <p className="text-elev-text-secondary mb-4">
                We serve the growing hockey community with the highest quality training available. Experience the advantage of year-round on-ice training in the desert, giving you a competitive edge in your development.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Facility;
