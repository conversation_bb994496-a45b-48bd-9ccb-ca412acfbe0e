/**
 * Debug import test
 */

import { describe, it, expect } from 'vitest';

describe('Debug Import', () => {
  it('should show what is exported', async () => {
    const module = await import('../color-remediation');
    console.log('Module exports:', Object.keys(module));
    console.log('ColorRemediationEngine:', module.ColorRemediationEngine);
    console.log('Type of ColorRemediationEngine:', typeof module.ColorRemediationEngine);
    
    expect(true).toBe(true); // Just to make the test pass
  });
});