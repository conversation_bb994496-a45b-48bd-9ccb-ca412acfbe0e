// Tests for ColorAnalyzer

import { ColorAnalyzer } from '../color-analyzer';
import { FileInfo, IssueType, Severity, Category } from '../../types';

describe('ColorAnalyzer', () => {
  let analyzer: ColorAnalyzer;

  beforeEach(() => {
    analyzer = new ColorAnalyzer();
  });

  describe('analyze', () => {
    it('should detect hardcoded hex colors', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
            const Button = () => (
              <button style={{ backgroundColor: '#ff0000', color: '#333' }}>
                Click me
              </button>
            );
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues).toHaveLength(2);
      expect(result.issues[0].type).toBe(IssueType.COLOR_INCONSISTENCY);
      expect(result.issues[0].description).toContain('#ff0000');
      expect(result.issues[1].description).toContain('#333');
    });

    it('should detect RGB and RGBA colors', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/styles/main.css',
          content: `
            .header {
              background-color: rgb(255, 0, 0);
              border-color: rgba(0, 0, 0, 0.5);
            }
          `,
          extension: '.css',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues).toHaveLength(2);
      expect(result.issues.some(issue => issue.description.includes('rgb(255, 0, 0)'))).toBe(true);
      expect(result.issues.some(issue => issue.description.includes('rgba(0, 0, 0, 0.5)'))).toBe(true);
    });

    it('should detect named colors', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Text.tsx',
          content: `
            const Text = () => (
              <p style={{ color: 'red', backgroundColor: 'blue' }}>
                Hello World
              </p>
            );
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues).toHaveLength(2);
      expect(result.issues.some(issue => issue.description.includes('red'))).toBe(true);
      expect(result.issues.some(issue => issue.description.includes('blue'))).toBe(true);
    });

    it('should not flag design tokens', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
            const Button = () => (
              <button style={{ 
                backgroundColor: 'var(--color-primary)',
                color: 'theme("colors.text.primary")',
                borderColor: '$border-color'
              }}>
                Click me
              </button>
            );
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      // Should not create issues for proper token usage
      const hardcodedColorIssues = result.issues.filter(issue => 
        issue.description.includes('Hardcoded color value')
      );
      expect(hardcodedColorIssues).toHaveLength(0);
    });

    it('should not flag comments', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
            // TODO: Change color to #ff0000
            /* Background should be #00ff00 */
            const Button = () => <button>Click me</button>;
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues).toHaveLength(0);
    });

    it('should not flag utility colors', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/styles/main.css',
          content: `
            .utility {
              color: transparent;
              background: inherit;
              border-color: currentColor;
            }
          `,
          extension: '.css',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues).toHaveLength(0);
    });

    it('should detect frequently used colors', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
            const Button = () => (
              <div>
                <button style={{ backgroundColor: '#ff0000' }}>Button 1</button>
                <button style={{ backgroundColor: '#ff0000' }}>Button 2</button>
                <button style={{ backgroundColor: '#ff0000' }}>Button 3</button>
                <button style={{ backgroundColor: '#ff0000' }}>Button 4</button>
              </div>
            );
          `,
          extension: '.tsx',
          size: 200,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      // Should have individual hardcoded color issues plus a frequent usage issue
      const frequentUsageIssues = result.issues.filter(issue => 
        issue.description.includes('is used') && issue.description.includes('times')
      );
      expect(frequentUsageIssues.length).toBeGreaterThan(0);
    });

    it('should detect mixed token patterns', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Mixed.tsx',
          content: `
            const Mixed = () => (
              <div style={{ 
                color: 'var(--color-primary)',
                backgroundColor: 'theme("colors.secondary")'
              }}>
                Mixed tokens
              </div>
            );
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      const mixedTokenIssues = result.issues.filter(issue => 
        issue.description.includes('Mixed color token patterns')
      );
      expect(mixedTokenIssues.length).toBeGreaterThan(0);
    });

    it('should provide correct severity levels', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
            const Button = () => (
              <button style={{ backgroundColor: '#ff0000' }}>
                Click me
              </button>
            );
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues[0].severity).toBe(Severity.MEDIUM);
      expect(result.issues[0].category).toBe(Category.VISUAL);
    });

    it('should provide actionable suggestions', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
            const Button = () => (
              <button style={{ backgroundColor: '#ff0000' }}>
                Click me
              </button>
            );
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues[0].suggestion).toContain('design token');
      expect(result.issues[0].suggestion).toContain('var(--color-primary)');
    });

    it('should mark simple colors as auto-fixable', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/styles/main.css',
          content: `
            .button {
              background-color: #ff0000;
            }
          `,
          extension: '.css',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      expect(result.issues[0].autoFixable).toBe(true);
    });

    it('should calculate impact assessment', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `
            const Button = () => (
              <button style={{ backgroundColor: '#ff0000' }}>
                Click me
              </button>
            );
          `,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      const issue = result.issues[0];
      expect(issue.impact.userExperience).toBeGreaterThan(0);
      expect(issue.impact.maintenanceEffort).toBeGreaterThan(0);
      expect(issue.impact.implementationComplexity).toBeGreaterThan(0);
    });

    it('should provide correct file locations', async () => {
      const files: FileInfo[] = [
        {
          path: 'src/components/Button.tsx',
          content: `const Button = () => (
  <button style={{ backgroundColor: '#ff0000' }}>
    Click me
  </button>
);`,
          extension: '.tsx',
          size: 100,
          lastModified: new Date()
        }
      ];

      const result = await analyzer.analyze(files);

      const issue = result.issues[0];
      expect(issue.location.filePath).toBe('src/components/Button.tsx');
      expect(issue.location.lineNumber).toBe(2);
      expect(issue.location.context).toContain('#ff0000');
    });
  });

  describe('getSeverity', () => {
    it('should return appropriate severity for different issue types', () => {
      const highSeverityIssue = {
        id: 'test',
        type: IssueType.COLOR_INCONSISTENCY,
        category: Category.VISUAL,
        description: 'frequently used color',
        location: { filePath: '', lineNumber: 1, columnNumber: 1, context: '' },
        suggestion: '',
        autoFixable: false,
        impact: { userExperience: 5, maintenanceEffort: 5, implementationComplexity: 5 }
      };

      expect(analyzer.getSeverity(highSeverityIssue)).toBe(Severity.HIGH);

      const mediumSeverityIssue = {
        ...highSeverityIssue,
        description: 'hardcoded color value'
      };

      expect(analyzer.getSeverity(mediumSeverityIssue)).toBe(Severity.MEDIUM);
    });
  });
});