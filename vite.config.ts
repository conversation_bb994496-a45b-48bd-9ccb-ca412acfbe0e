
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { visualizer } from "rollup-plugin-visualizer";
import type { UserConfig } from 'vite';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const config: UserConfig = {
    server: {
      host: "0.0.0.0",
      port: 8080,
    },
    plugins: [
      react(),
      !!process.env.ANALYZE_BUNDLE && visualizer({ open: true, filename: "bundle-stats.html" }),
    ].filter(Boolean),
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    build: {
      // Improve chunk size and distribution
      rollupOptions: {
        output: {
          manualChunks: (id) => {
            // Keep all React in a single chunk to prevent forwardRef issues
            if (id.includes('node_modules/react') || 
                id.includes('node_modules/react-dom') || 
                id.includes('node_modules/scheduler') ||
                id.includes('node_modules/@types/react')) {
              return 'react-vendor';
            }
            
            // Group UI component libraries
            if (id.includes('node_modules/@radix-ui') || 
                id.includes('node_modules/class-variance-authority') ||
                id.includes('node_modules/clsx') ||
                id.includes('node_modules/tailwind-merge')) {
              return 'ui-components';
            }
            
            // Group routing libraries
            if (id.includes('node_modules/react-router') ||
                id.includes('node_modules/react-helmet')) {
              return 'routing';
            }
            
            // Keep other node_modules in a separate chunk
            if (id.includes('node_modules')) {
              return 'vendor';
            }
          },
          // Improve caching by using content hashing
          chunkFileNames: 'assets/[name]-[hash].js',
          entryFileNames: 'assets/[name]-[hash].js',
        },
      },
      // Enable minification optimizations
      minify: 'terser',
      // Reduce chunk size
      chunkSizeWarningLimit: 1000,
      // Generate sourcemaps for production builds
      sourcemap: mode !== 'production',
    },
    // Optimize dependencies for faster dev startup
    optimizeDeps: {
      include: ['react', 'react-dom', 'react-router-dom'],
    },
  };
  
  return config;
});

