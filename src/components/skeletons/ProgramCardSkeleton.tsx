import { Skeleton } from "@/components/ui/skeleton";

/**
 * Enhanced skeleton loader for Program Cards with responsive design
 * Adapts sizing and spacing based on screen size
 */
export function ProgramCardSkeleton() {
  return (
    <div className="bg-white rounded-lg overflow-hidden shadow-lg flex flex-col">
      {/* Image skeleton - Responsive height */}
      <Skeleton className="h-48 sm:h-56 lg:h-64 w-full" />
      
      {/* Content skeleton - Responsive padding */}
      <div className="p-4 sm:p-6 flex flex-col flex-grow">
        <div className="flex-grow">
          {/* Title and price - Responsive sizing */}
          <div className="flex justify-between items-start mb-3 sm:mb-4">
            <Skeleton className="h-5 sm:h-6 w-2/3 sm:w-3/4" />
            <Skeleton className="h-4 sm:h-5 w-12 sm:w-16 rounded-full" />
          </div>
          
          {/* Description - Responsive line height and spacing */}
          <div className="space-y-1.5 sm:space-y-2 mb-4 sm:mb-6">
            <Skeleton className="h-3 sm:h-4 w-full" />
            <Skeleton className="h-3 sm:h-4 w-11/12" />
            <Skeleton className="h-3 sm:h-4 w-4/5" />
          </div>
          
          {/* Features list - Responsive spacing */}
          <div className="mb-4 sm:mb-6 space-y-1.5 sm:space-y-2">
            <Skeleton className="h-3 sm:h-4 w-11/12" />
            <Skeleton className="h-3 sm:h-4 w-10/12" />
            <Skeleton className="h-3 sm:h-4 w-11/12" />
            <Skeleton className="h-3 sm:h-4 w-9/12" />
          </div>
          
          {/* Testimonial - Responsive height */}
          <div className="mb-4 sm:mb-6">
            <Skeleton className="h-20 sm:h-24 w-full rounded-lg" />
          </div>
        </div>
        
        {/* Button - Responsive height */}
        <Skeleton className="h-9 sm:h-10 lg:h-11 w-full rounded-md" />
      </div>
    </div>
  );
}

export default ProgramCardSkeleton;
