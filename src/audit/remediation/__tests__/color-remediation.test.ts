/**
 * Tests for Color Remediation Utilities
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { ColorRemediationEngine } from '../color-remediation';
import { DesignTokenGenerator } from '../../design-tokens/generator';
import { Issue, IssueType, Severity, Category, FileInfo } from '../../types/index';

describe('ColorRemediationEngine', () => {
  let remediationEngine: ColorRemediationEngine;
  let mockDesignTokens: any;
  let mockFiles: FileInfo[];
  let mockIssues: Issue[];

  beforeEach(() => {
    const generator = new DesignTokenGenerator();
    mockDesignTokens = generator.generateTokenSystem();
    remediationEngine = new ColorRemediationEngine(mockDesignTokens);

    mockFiles = [
      {
        path: 'src/components/Button.tsx',
        content: `
import React from 'react';

const Button = () => {
  return (
    <button 
      style={{ 
        backgroundColor: '#3b82f6',
        color: '#ffffff',
        border: '1px solid #2563eb'
      }}
    >
      Click me
    </button>
  );
};

export default Button;
        `.trim(),
        extension: '.tsx',
        size: 200,
        lastModified: new Date()
      },
      {
        path: 'src/styles/main.css',
        content: `
.primary-button {
  background-color: #3b82f6;
  color: white;
  border-color: #2563eb;
}

.error-text {
  color: #dc2626;
}
        `.trim(),
        extension: '.css',
        size: 150,
        lastModified: new Date()
      }
    ];

    mockIssues = [
      {
        id: 'color_1',
        type: IssueType.COLOR_INCONSISTENCY,
        severity: Severity.MEDIUM,
        category: Category.VISUAL,
        description: "Hardcoded color value '#3b82f6' found. Consider using design tokens for consistency.",
        location: {
          filePath: 'src/components/Button.tsx',
          lineNumber: 7,
          columnNumber: 25,
          context: "backgroundColor: '#3b82f6',"
        },
        suggestion: "Replace '#3b82f6' with a design token like 'var(--color-primary-500)'",
        autoFixable: true,
        impact: {
          userExperience: 4,
          maintenanceEffort: 6,
          implementationComplexity: 3
        }
      },
      {
        id: 'color_2',
        type: IssueType.COLOR_INCONSISTENCY,
        severity: Severity.MEDIUM,
        category: Category.VISUAL,
        description: "Hardcoded color value '#ffffff' found. Consider using design tokens for consistency.",
        location: {
          filePath: 'src/components/Button.tsx',
          lineNumber: 8,
          columnNumber: 16,
          context: "color: '#ffffff',"
        },
        suggestion: "Replace '#ffffff' with a design token like 'var(--color-neutral-50)'",
        autoFixable: true,
        impact: {
          userExperience: 3,
          maintenanceEffort: 5,
          implementationComplexity: 2
        }
      },
      {
        id: 'color_3',
        type: IssueType.COLOR_INCONSISTENCY,
        severity: Severity.HIGH,
        category: Category.VISUAL,
        description: "Hardcoded color value '#dc2626' found. Consider using design tokens for consistency.",
        location: {
          filePath: 'src/styles/main.css',
          lineNumber: 8,
          columnNumber: 9,
          context: "color: #dc2626;"
        },
        suggestion: "Replace '#dc2626' with a design token like 'var(--color-error-default)'",
        autoFixable: true,
        impact: {
          userExperience: 5,
          maintenanceEffort: 7,
          implementationComplexity: 3
        }
      }
    ];
  });

  describe('remediateColorIssues', () => {
    it('should successfully remediate color issues across multiple files', async () => {
      const result = await remediationEngine.remediateColorIssues(mockIssues, mockFiles);

      expect(result.success).toBe(true);
      expect(result.filesModified).toHaveLength(2);
      expect(result.replacements).toHaveLength(3);
      expect(result.errors).toHaveLength(0);
      expect(result.summary.totalReplacements).toBe(3);
      expect(result.summary.filesAffected).toBe(2);
    });

    it('should create correct color replacements', async () => {
      const result = await remediationEngine.remediateColorIssues(mockIssues, mockFiles);

      const primaryColorReplacement = result.replacements.find(r => 
        r.originalValue === '#3b82f6'
      );
      expect(primaryColorReplacement).toBeDefined();
      expect(primaryColorReplacement?.tokenValue).toBe('var(--color-primary-500)');

      const errorColorReplacement = result.replacements.find(r => 
        r.originalValue === '#dc2626'
      );
      expect(errorColorReplacement).toBeDefined();
      expect(errorColorReplacement?.tokenValue).toBe('var(--color-error-default)');
    });

    it('should modify file content with token replacements', async () => {
      const result = await remediationEngine.remediateColorIssues(mockIssues, mockFiles);

      const buttonFile = mockFiles.find(f => f.path === 'src/components/Button.tsx');
      expect(buttonFile?.content).toContain('var(--color-primary-500)');
      expect(buttonFile?.content).toContain('var(--color-neutral-50)');
      expect(buttonFile?.content).not.toContain('#3b82f6');
      expect(buttonFile?.content).not.toContain('#ffffff');

      const cssFile = mockFiles.find(f => f.path === 'src/styles/main.css');
      expect(cssFile?.content).toContain('var(--color-error-default)');
      expect(cssFile?.content).not.toContain('#dc2626');
    });

    it('should handle non-auto-fixable issues gracefully', async () => {
      const nonFixableIssue: Issue = {
        ...mockIssues[0],
        id: 'color_non_fixable',
        autoFixable: false
      };

      const result = await remediationEngine.remediateColorIssues(
        [nonFixableIssue], 
        mockFiles
      );

      expect(result.replacements).toHaveLength(0);
      expect(result.filesModified).toHaveLength(0);
    });

    it('should handle missing files gracefully', async () => {
      const issueWithMissingFile: Issue = {
        ...mockIssues[0],
        location: {
          ...mockIssues[0].location,
          filePath: 'src/missing/file.tsx'
        }
      };

      const result = await remediationEngine.remediateColorIssues(
        [issueWithMissingFile], 
        mockFiles
      );

      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].error).toBe('File not found');
      expect(result.errors[0].filePath).toBe('src/missing/file.tsx');
    });
  });

  describe('validateColorTokenUsage', () => {
    it('should validate correct color token usage', async () => {
      const fileWithValidTokens: FileInfo = {
        path: 'src/components/ValidButton.tsx',
        content: `
const ValidButton = () => (
  <button style={{
    backgroundColor: 'var(--color-primary-500)',
    color: 'var(--color-neutral-50)',
    borderColor: 'var(--color-primary-600)'
  }}>
    Valid Button
  </button>
);
        `.trim(),
        extension: '.tsx',
        size: 200,
        lastModified: new Date()
      };

      const result = await remediationEngine.validateColorTokenUsage([fileWithValidTokens]);

      expect(result.validUsages).toBe(3);
      expect(result.invalidUsages).toBe(0);
      expect(result.suggestions).toHaveLength(0);
    });

    it('should detect invalid color token usage', async () => {
      const fileWithInvalidTokens: FileInfo = {
        path: 'src/components/InvalidButton.tsx',
        content: `
const InvalidButton = () => (
  <button style={{
    backgroundColor: 'var(--color-invalid-token)',
    color: 'var(--wrong-format)',
    borderColor: 'var(--color-primary-500)'
  }}>
    Invalid Button
  </button>
);
        `.trim(),
        extension: '.tsx',
        size: 200,
        lastModified: new Date()
      };

      const result = await remediationEngine.validateColorTokenUsage([fileWithInvalidTokens]);

      expect(result.validUsages).toBe(1);
      expect(result.invalidUsages).toBe(1);
      expect(result.suggestions).toHaveLength(1);
      expect(result.suggestions[0]).toContain('Invalid color token');
    });
  });

  describe('color mapping and normalization', () => {
    it('should normalize hex colors correctly', () => {
      const engine = remediationEngine as any;
      
      // Test hex shorthand expansion
      expect(engine.normalizeColor('#fff')).toBe('#ffffff');
      expect(engine.normalizeColor('#000')).toBe('#000000');
      
      // Test RGB to hex conversion
      expect(engine.normalizeColor('rgb(255, 255, 255)')).toBe('#ffffff');
      expect(engine.normalizeColor('rgb(0, 0, 0)')).toBe('#000000');
    });

    it('should calculate color distance correctly', () => {
      const engine = remediationEngine as any;
      
      // Same colors should have 0 distance
      expect(engine.calculateColorDistance('#ffffff', '#ffffff')).toBe(0);
      
      // Black and white should have maximum distance
      const blackWhiteDistance = engine.calculateColorDistance('#000000', '#ffffff');
      expect(blackWhiteDistance).toBeGreaterThan(400);
      
      // Similar colors should have small distance
      const similarDistance = engine.calculateColorDistance('#3b82f6', '#2563eb');
      expect(similarDistance).toBeLessThan(100);
    });

    it('should find best token matches for common colors', () => {
      const engine = remediationEngine as any;
      
      // Test primary color matching
      expect(engine.findBestTokenMatch('#3b82f6')).toBe('var(--color-primary-500)');
      
      // Test error color matching
      expect(engine.findBestTokenMatch('#dc2626')).toBe('var(--color-error-default)');
      
      // Test neutral color matching
      expect(engine.findBestTokenMatch('#ffffff')).toBe('var(--color-neutral-50)');
      expect(engine.findBestTokenMatch('#000000')).toBe('var(--color-neutral-900)');
    });
  });

  describe('replacement application', () => {
    it('should apply replacements in different CSS contexts', () => {
      const engine = remediationEngine as any;
      
      const testCases = [
        {
          line: 'color: #3b82f6;',
          replacement: { originalValue: '#3b82f6', tokenValue: 'var(--color-primary-500)' },
          expected: 'color: var(--color-primary-500);'
        },
        {
          line: 'background-color: #dc2626;',
          replacement: { originalValue: '#dc2626', tokenValue: 'var(--color-error-default)' },
          expected: 'background-color: var(--color-error-default);'
        },
        {
          line: 'border-color: #ffffff;',
          replacement: { originalValue: '#ffffff', tokenValue: 'var(--color-neutral-50)' },
          expected: 'border-color: var(--color-neutral-50);'
        }
      ];

      testCases.forEach(({ line, replacement, expected }) => {
        const result = engine.applyColorReplacement(line, replacement);
        expect(result).toBe(expected);
      });
    });

    it('should handle inline style replacements', () => {
      const engine = remediationEngine as any;
      
      const line = 'style="color: #3b82f6; background: #ffffff"';
      const replacement = { originalValue: '#3b82f6', tokenValue: 'var(--color-primary-500)' };
      
      const result = engine.applyColorReplacement(line, replacement);
      expect(result).toContain('var(--color-primary-500)');
    });

    it('should handle JavaScript object property replacements', () => {
      const engine = remediationEngine as any;
      
      const line = "color: '#3b82f6',";
      const replacement = { originalValue: '#3b82f6', tokenValue: 'var(--color-primary-500)' };
      
      const result = engine.applyColorReplacement(line, replacement);
      expect(result).toBe("color: 'var(--color-primary-500)',");
    });
  });

  describe('error handling', () => {
    it('should handle malformed color values gracefully', async () => {
      const malformedIssue: Issue = {
        ...mockIssues[0],
        description: "Malformed color found",
        location: {
          ...mockIssues[0].location,
          context: "color: invalid-color-value;"
        }
      };

      const result = await remediationEngine.remediateColorIssues(
        [malformedIssue], 
        mockFiles
      );

      // Should not crash, but may not create replacements
      expect(result.success).toBe(true);
    });

    it('should handle files with parsing errors', async () => {
      const corruptFile: FileInfo = {
        path: 'src/corrupt.tsx',
        content: 'This is not valid code {{{',
        extension: '.tsx',
        size: 25,
        lastModified: new Date()
      };

      const issueForCorruptFile: Issue = {
        ...mockIssues[0],
        location: {
          ...mockIssues[0].location,
          filePath: 'src/corrupt.tsx'
        }
      };

      const result = await remediationEngine.remediateColorIssues(
        [issueForCorruptFile], 
        [corruptFile]
      );

      expect(result.success).toBe(true); // Should not crash
    });
  });
});