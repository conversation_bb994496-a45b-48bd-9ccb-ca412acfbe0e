/**
 * Design Token Generator Tests
 */

import { describe, it, expect } from 'vitest';
import { DesignTokenGenerator } from '../generator.js';

describe('DesignTokenGenerator', () => {
  let generator: DesignTokenGenerator;

  beforeEach(() => {
    generator = new DesignTokenGenerator();
  });

  describe('generateTokenSystem', () => {
    it('should generate a complete design token system', () => {
      const tokens = generator.generateTokenSystem();

      expect(tokens).toHaveProperty('colors');
      expect(tokens).toHaveProperty('typography');
      expect(tokens).toHaveProperty('spacing');
      expect(tokens).toHaveProperty('layout');
      expect(tokens).toHaveProperty('metadata');
    });

    it('should include metadata with version and timestamp', () => {
      const tokens = generator.generateTokenSystem();

      expect(tokens.metadata.version).toBe('1.0.0');
      expect(tokens.metadata.source).toBe('design-system-audit-tool');
      expect(tokens.metadata.generatedAt).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);
    });
  });

  describe('color tokens', () => {
    it('should generate comprehensive color palette', () => {
      const tokens = generator.generateTokenSystem();
      const colors = tokens.colors;

      expect(colors).toHaveProperty('primary');
      expect(colors).toHaveProperty('secondary');
      expect(colors).toHaveProperty('semantic');
      expect(colors).toHaveProperty('neutral');
      expect(colors).toHaveProperty('surface');
    });

    it('should generate complete color scales', () => {
      const tokens = generator.generateTokenSystem();
      const primaryScale = tokens.colors.primary;

      const expectedShades = ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900', '950'];
      expectedShades.forEach(shade => {
        expect(primaryScale).toHaveProperty(shade);
        expect(primaryScale[shade as keyof typeof primaryScale]).toMatch(/^#[0-9a-f]{6}$/i);
      });
    });

    it('should generate semantic colors with variants', () => {
      const tokens = generator.generateTokenSystem();
      const semantic = tokens.colors.semantic;

      ['success', 'warning', 'error', 'info'].forEach(type => {
        expect(semantic).toHaveProperty(type);
        const colorVariant = semantic[type as keyof typeof semantic];
        expect(colorVariant).toHaveProperty('light');
        expect(colorVariant).toHaveProperty('default');
        expect(colorVariant).toHaveProperty('dark');
        expect(colorVariant).toHaveProperty('contrast');
      });
    });

    it('should generate surface colors for UI elements', () => {
      const tokens = generator.generateTokenSystem();
      const surface = tokens.colors.surface;

      const expectedSurfaceColors = [
        'background', 'foreground', 'card', 'cardForeground',
        'popover', 'popoverForeground', 'muted', 'mutedForeground',
        'accent', 'accentForeground', 'border', 'input', 'ring'
      ];

      expectedSurfaceColors.forEach(color => {
        expect(surface).toHaveProperty(color);
        expect(surface[color as keyof typeof surface]).toMatch(/^#[0-9a-f]{6}$/i);
      });
    });
  });

  describe('typography tokens', () => {
    it('should generate comprehensive typography system', () => {
      const tokens = generator.generateTokenSystem();
      const typography = tokens.typography;

      expect(typography).toHaveProperty('fontFamilies');
      expect(typography).toHaveProperty('fontSizes');
      expect(typography).toHaveProperty('fontWeights');
      expect(typography).toHaveProperty('lineHeights');
      expect(typography).toHaveProperty('letterSpacing');
      expect(typography).toHaveProperty('typeScale');
    });

    it('should generate font families for different contexts', () => {
      const tokens = generator.generateTokenSystem();
      const fontFamilies = tokens.typography.fontFamilies;

      expect(fontFamilies).toHaveProperty('sans');
      expect(fontFamilies).toHaveProperty('serif');
      expect(fontFamilies).toHaveProperty('mono');
      expect(fontFamilies).toHaveProperty('display');

      expect(Array.isArray(fontFamilies.sans)).toBe(true);
      expect(fontFamilies.sans.length).toBeGreaterThan(0);
    });

    it('should generate consistent font size scale', () => {
      const tokens = generator.generateTokenSystem();
      const fontSizes = tokens.typography.fontSizes;

      const expectedSizes = ['xs', 'sm', 'base', 'lg', 'xl', '2xl', '3xl', '4xl', '5xl', '6xl', '7xl', '8xl', '9xl'];
      expectedSizes.forEach(size => {
        expect(fontSizes).toHaveProperty(size);
        expect(fontSizes[size as keyof typeof fontSizes]).toMatch(/^\d+(\.\d+)?rem$/);
      });
    });

    it('should generate font weights from thin to black', () => {
      const tokens = generator.generateTokenSystem();
      const fontWeights = tokens.typography.fontWeights;

      expect(fontWeights.thin).toBe(100);
      expect(fontWeights.normal).toBe(400);
      expect(fontWeights.bold).toBe(700);
      expect(fontWeights.black).toBe(900);
    });

    it('should generate type scale with hierarchy', () => {
      const tokens = generator.generateTokenSystem();
      const typeScale = tokens.typography.typeScale;

      const headingLevels = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
      headingLevels.forEach(level => {
        expect(typeScale).toHaveProperty(level);
        const style = typeScale[level as keyof typeof typeScale];
        expect(style).toHaveProperty('fontSize');
        expect(style).toHaveProperty('fontWeight');
        expect(style).toHaveProperty('lineHeight');
        expect(style).toHaveProperty('letterSpacing');
        expect(style).toHaveProperty('fontFamily');
      });

      // H1 should be larger than H2
      const h1Size = parseFloat(typeScale.h1.fontSize);
      const h2Size = parseFloat(typeScale.h2.fontSize);
      expect(h1Size).toBeGreaterThan(h2Size);
    });
  });

  describe('spacing tokens', () => {
    it('should generate comprehensive spacing system', () => {
      const tokens = generator.generateTokenSystem();
      const spacing = tokens.spacing;

      expect(spacing).toHaveProperty('scale');
      expect(spacing).toHaveProperty('semantic');
      expect(spacing).toHaveProperty('layout');
    });

    it('should generate consistent spacing scale', () => {
      const tokens = generator.generateTokenSystem();
      const scale = tokens.spacing.scale;

      expect(scale['0']).toBe('0px');
      expect(scale.px).toBe('1px');
      expect(scale['1']).toBe('0.25rem');
      expect(scale['4']).toBe('1rem');
      expect(scale['8']).toBe('2rem');
    });

    it('should generate semantic spacing for components', () => {
      const tokens = generator.generateTokenSystem();
      const semantic = tokens.spacing.semantic;

      expect(semantic).toHaveProperty('component');
      expect(semantic).toHaveProperty('layout');

      const component = semantic.component;
      expect(component).toHaveProperty('padding');
      expect(component).toHaveProperty('margin');
      expect(component).toHaveProperty('gap');

      ['xs', 'sm', 'md', 'lg', 'xl'].forEach(size => {
        expect(component.padding).toHaveProperty(size);
        expect(component.margin).toHaveProperty(size);
        expect(component.gap).toHaveProperty(size);
      });
    });

    it('should generate layout spacing and container sizes', () => {
      const tokens = generator.generateTokenSystem();
      const layout = tokens.spacing.layout;

      expect(layout).toHaveProperty('containerMaxWidth');
      expect(layout).toHaveProperty('containerPadding');

      const containerSizes = ['sm', 'md', 'lg', 'xl', '2xl'];
      containerSizes.forEach(size => {
        expect(layout.containerMaxWidth).toHaveProperty(size);
      });

      ['mobile', 'tablet', 'desktop'].forEach(device => {
        expect(layout.containerPadding).toHaveProperty(device);
      });
    });
  });

  describe('layout tokens', () => {
    it('should generate comprehensive layout system', () => {
      const tokens = generator.generateTokenSystem();
      const layout = tokens.layout;

      expect(layout).toHaveProperty('grid');
      expect(layout).toHaveProperty('breakpoints');
      expect(layout).toHaveProperty('containers');
      expect(layout).toHaveProperty('patterns');
      expect(layout).toHaveProperty('validation');
    });

    it('should generate grid system with columns and gutters', () => {
      const tokens = generator.generateTokenSystem();
      const grid = tokens.layout.grid;

      expect(grid).toHaveProperty('columns');
      expect(grid).toHaveProperty('gutters');
      expect(grid).toHaveProperty('margins');
      expect(grid).toHaveProperty('baseline');

      expect(grid.columns.default).toBe(12);
      expect(grid.baseline.unit).toBe('0.25rem');
    });

    it('should generate responsive breakpoints', () => {
      const tokens = generator.generateTokenSystem();
      const breakpoints = tokens.layout.breakpoints;

      const expectedBreakpoints = ['xs', 'sm', 'md', 'lg', 'xl', '2xl'];
      expectedBreakpoints.forEach(bp => {
        expect(breakpoints).toHaveProperty(bp);
      });

      expect(breakpoints).toHaveProperty('ranges');
      expect(breakpoints.ranges).toHaveProperty('mobile');
      expect(breakpoints.ranges).toHaveProperty('tablet');
      expect(breakpoints.ranges).toHaveProperty('desktop');
    });

    it('should generate container system with max widths', () => {
      const tokens = generator.generateTokenSystem();
      const containers = tokens.layout.containers;

      expect(containers).toHaveProperty('maxWidths');
      expect(containers).toHaveProperty('padding');
      expect(containers).toHaveProperty('centering');

      const containerSizes = ['xs', 'sm', 'md', 'lg', 'xl', '2xl', 'full'];
      containerSizes.forEach(size => {
        expect(containers.maxWidths).toHaveProperty(size);
      });
    });

    it('should generate layout patterns', () => {
      const tokens = generator.generateTokenSystem();
      const patterns = tokens.layout.patterns;

      const expectedPatterns = ['stack', 'cluster', 'sidebar', 'switcher', 'cover', 'grid'];
      expectedPatterns.forEach(pattern => {
        expect(patterns).toHaveProperty(pattern);
      });

      // Test stack pattern structure
      expect(patterns.stack).toHaveProperty('gap');
      expect(patterns.stack).toHaveProperty('alignment');
      
      // Test grid pattern structure
      expect(patterns.grid).toHaveProperty('minItemWidth');
      expect(patterns.grid).toHaveProperty('gap');
      expect(patterns.grid).toHaveProperty('autoFit');
    });

    it('should generate validation rules and accessibility guidelines', () => {
      const tokens = generator.generateTokenSystem();
      const validation = tokens.layout.validation;

      expect(validation).toHaveProperty('rules');
      expect(validation).toHaveProperty('patterns');
      expect(validation).toHaveProperty('accessibility');

      expect(Array.isArray(validation.rules)).toBe(true);
      expect(validation.rules.length).toBeGreaterThan(0);

      expect(validation.accessibility).toHaveProperty('focusManagement');
      expect(validation.accessibility).toHaveProperty('semanticStructure');
      expect(validation.accessibility).toHaveProperty('responsiveDesign');
    });
  });

  describe('exportTokens', () => {
    it('should export tokens as JSON by default', () => {
      const tokens = generator.generateTokenSystem();
      const exported = generator.exportTokens(tokens);

      expect(() => JSON.parse(exported)).not.toThrow();
      const parsed = JSON.parse(exported);
      expect(parsed).toHaveProperty('colors');
      expect(parsed).toHaveProperty('typography');
      expect(parsed).toHaveProperty('spacing');
    });

    it('should export tokens as CSS custom properties', () => {
      const tokens = generator.generateTokenSystem();
      const css = generator.exportTokens(tokens, 'css');

      expect(css).toContain(':root {');
      expect(css).toContain('--color-primary-500');
      expect(css).toContain('--font-size-base');
      expect(css).toContain('--spacing-4');
      expect(css).toContain('}');
    });

    it('should export tokens as JavaScript module', () => {
      const tokens = generator.generateTokenSystem();
      const js = generator.exportTokens(tokens, 'js');

      expect(js).toContain('export const designTokens =');
      expect(js).toContain('"colors"');
      expect(js).toContain('"typography"');
      expect(js).toContain('"spacing"');
    });

    it('should export tokens as SCSS variables', () => {
      const tokens = generator.generateTokenSystem();
      const scss = generator.exportTokens(tokens, 'scss');

      expect(scss).toContain('$colors:');
      expect(scss).toContain('primary:');
    });
  });
});