/**
 * Tests for Validation Engine
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { ValidationEngine, validateDesignSystemCompliance, testRemediationImpact } from '../index';
import { ValidationFile, FileType, ValidationCategory } from '../types';
import { DesignTokenSystem } from '../../design-tokens/types';

describe('ValidationEngine', () => {
  let engine: ValidationEngine;
  let mockFiles: ValidationFile[];
  let mockTokens: DesignTokenSystem;

  beforeEach(() => {
    engine = new ValidationEngine();

    mockFiles = [
      {
        path: 'src/components/Button.tsx',
        content: `
          import React from 'react';
          
          const Button = ({ children, onClick }) => {
            return (
              <button 
                style={{
                  backgroundColor: '#ff0000',
                  color: '#ffffff',
                  padding: '12px',
                  fontSize: '16px'
                }}
                onClick={onClick}
              >
                {children}
              </button>
            );
          };
          
          export default Button;
        `,
        type: FileType.COMPONENT,
        lastModified: new Date()
      },
      {
        path: 'src/styles/button.css',
        content: `
          .button {
            background-color: #ff0000;
            color: #ffffff;
            padding: 12px;
            font-size: 16px;
            border: none;
            cursor: pointer;
          }
          
          .invalid-token {
            color: var(--invalid-token-name);
          }
        `,
        type: FileType.STYLE,
        lastModified: new Date()
      }
    ];

    mockTokens = {
      colors: {
        primary: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#0ea5e9',
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
          950: '#082f49'
        },
        secondary: {
          50: '#fafafa',
          100: '#f4f4f5',
          200: '#e4e4e7',
          300: '#d4d4d8',
          400: '#a1a1aa',
          500: '#71717a',
          600: '#52525b',
          700: '#3f3f46',
          800: '#27272a',
          900: '#18181b',
          950: '#09090b'
        },
        semantic: {
          success: {
            light: '#dcfce7',
            default: '#16a34a',
            dark: '#15803d',
            contrast: '#ffffff'
          },
          warning: {
            light: '#fef3c7',
            default: '#d97706',
            dark: '#92400e',
            contrast: '#ffffff'
          },
          error: {
            light: '#fee2e2',
            default: '#dc2626',
            dark: '#991b1b',
            contrast: '#ffffff'
          },
          info: {
            light: '#dbeafe',
            default: '#2563eb',
            dark: '#1d4ed8',
            contrast: '#ffffff'
          }
        },
        neutral: {
          50: '#fafafa',
          100: '#f4f4f5',
          200: '#e4e4e7',
          300: '#d4d4d8',
          400: '#a1a1aa',
          500: '#71717a',
          600: '#52525b',
          700: '#3f3f46',
          800: '#27272a',
          900: '#18181b',
          950: '#09090b'
        },
        surface: {
          background: '#ffffff',
          foreground: '#09090b',
          card: '#ffffff',
          cardForeground: '#09090b',
          popover: '#ffffff',
          popoverForeground: '#09090b',
          muted: '#f4f4f5',
          mutedForeground: '#71717a',
          accent: '#f4f4f5',
          accentForeground: '#18181b',
          border: '#e4e4e7',
          input: '#e4e4e7',
          ring: '#18181b'
        }
      },
      typography: {
        fontFamilies: {
          sans: ['Inter', 'system-ui', 'sans-serif'],
          serif: ['Georgia', 'serif'],
          mono: ['Monaco', 'monospace'],
          display: ['Inter', 'system-ui', 'sans-serif']
        },
        fontSizes: {
          xs: '0.75rem',
          sm: '0.875rem',
          base: '1rem',
          lg: '1.125rem',
          xl: '1.25rem',
          '2xl': '1.5rem',
          '3xl': '1.875rem',
          '4xl': '2.25rem',
          '5xl': '3rem',
          '6xl': '3.75rem',
          '7xl': '4.5rem',
          '8xl': '6rem',
          '9xl': '8rem'
        },
        fontWeights: {
          thin: 100,
          extralight: 200,
          light: 300,
          normal: 400,
          medium: 500,
          semibold: 600,
          bold: 700,
          extrabold: 800,
          black: 900
        },
        lineHeights: {
          none: 1,
          tight: 1.25,
          snug: 1.375,
          normal: 1.5,
          relaxed: 1.625,
          loose: 2
        },
        letterSpacing: {
          tighter: '-0.05em',
          tight: '-0.025em',
          normal: '0em',
          wide: '0.025em',
          wider: '0.05em',
          widest: '0.1em'
        },
        typeScale: {
          h1: {
            fontSize: '2.25rem',
            fontWeight: 700,
            lineHeight: 1.2,
            letterSpacing: '-0.025em',
            fontFamily: ['Inter', 'system-ui', 'sans-serif']
          },
          h2: {
            fontSize: '1.875rem',
            fontWeight: 600,
            lineHeight: 1.3,
            letterSpacing: '-0.025em',
            fontFamily: ['Inter', 'system-ui', 'sans-serif']
          },
          h3: {
            fontSize: '1.5rem',
            fontWeight: 600,
            lineHeight: 1.4,
            letterSpacing: '0em',
            fontFamily: ['Inter', 'system-ui', 'sans-serif']
          },
          h4: {
            fontSize: '1.25rem',
            fontWeight: 600,
            lineHeight: 1.4,
            letterSpacing: '0em',
            fontFamily: ['Inter', 'system-ui', 'sans-serif']
          },
          h5: {
            fontSize: '1.125rem',
            fontWeight: 600,
            lineHeight: 1.5,
            letterSpacing: '0em',
            fontFamily: ['Inter', 'system-ui', 'sans-serif']
          },
          h6: {
            fontSize: '1rem',
            fontWeight: 600,
            lineHeight: 1.5,
            letterSpacing: '0em',
            fontFamily: ['Inter', 'system-ui', 'sans-serif']
          },
          body: {
            fontSize: '1rem',
            fontWeight: 400,
            lineHeight: 1.5,
            letterSpacing: '0em',
            fontFamily: ['Inter', 'system-ui', 'sans-serif']
          },
          bodyLarge: {
            fontSize: '1.125rem',
            fontWeight: 400,
            lineHeight: 1.5,
            letterSpacing: '0em',
            fontFamily: ['Inter', 'system-ui', 'sans-serif']
          },
          bodySmall: {
            fontSize: '0.875rem',
            fontWeight: 400,
            lineHeight: 1.5,
            letterSpacing: '0em',
            fontFamily: ['Inter', 'system-ui', 'sans-serif']
          },
          caption: {
            fontSize: '0.75rem',
            fontWeight: 400,
            lineHeight: 1.4,
            letterSpacing: '0.025em',
            fontFamily: ['Inter', 'system-ui', 'sans-serif']
          },
          overline: {
            fontSize: '0.75rem',
            fontWeight: 600,
            lineHeight: 1.4,
            letterSpacing: '0.1em',
            fontFamily: ['Inter', 'system-ui', 'sans-serif']
          }
        }
      },
      spacing: {
        scale: {
          0: '0px',
          px: '1px',
          0.5: '0.125rem',
          1: '0.25rem',
          1.5: '0.375rem',
          2: '0.5rem',
          2.5: '0.625rem',
          3: '0.75rem',
          3.5: '0.875rem',
          4: '1rem',
          5: '1.25rem',
          6: '1.5rem',
          7: '1.75rem',
          8: '2rem',
          9: '2.25rem',
          10: '2.5rem',
          11: '2.75rem',
          12: '3rem',
          14: '3.5rem',
          16: '4rem',
          20: '5rem',
          24: '6rem',
          28: '7rem',
          32: '8rem',
          36: '9rem',
          40: '10rem',
          44: '11rem',
          48: '12rem',
          52: '13rem',
          56: '14rem',
          60: '15rem',
          64: '16rem',
          72: '18rem',
          80: '20rem',
          96: '24rem'
        },
        semantic: {
          component: {
            padding: {
              xs: '0.25rem',
              sm: '0.5rem',
              md: '1rem',
              lg: '1.5rem',
              xl: '2rem'
            },
            margin: {
              xs: '0.25rem',
              sm: '0.5rem',
              md: '1rem',
              lg: '1.5rem',
              xl: '2rem'
            },
            gap: {
              xs: '0.25rem',
              sm: '0.5rem',
              md: '1rem',
              lg: '1.5rem',
              xl: '2rem'
            }
          },
          layout: {
            section: '4rem',
            container: '2rem',
            content: '1rem'
          }
        },
        layout: {
          containerMaxWidth: {
            sm: '640px',
            md: '768px',
            lg: '1024px',
            xl: '1280px',
            '2xl': '1536px'
          },
          containerPadding: {
            mobile: '1rem',
            tablet: '2rem',
            desktop: '3rem'
          }
        }
      },
      layout: {
        grid: {
          columns: {
            default: 12,
            sm: 4,
            md: 8,
            lg: 12,
            xl: 12
          },
          gutters: {
            xs: '0.5rem',
            sm: '1rem',
            md: '1.5rem',
            lg: '2rem',
            xl: '3rem'
          },
          margins: {
            mobile: '1rem',
            tablet: '2rem',
            desktop: '3rem',
            wide: '4rem'
          },
          baseline: {
            unit: '0.25rem',
            scale: 4
          }
        },
        breakpoints: {
          xs: '475px',
          sm: '640px',
          md: '768px',
          lg: '1024px',
          xl: '1280px',
          '2xl': '1536px',
          ranges: {
            mobile: '0-767px',
            tablet: '768-1023px',
            desktop: '1024-1279px',
            wide: '1280px+'
          }
        },
        containers: {
          maxWidths: {
            xs: '475px',
            sm: '640px',
            md: '768px',
            lg: '1024px',
            xl: '1280px',
            '2xl': '1536px',
            full: '100%'
          },
          padding: {
            mobile: '1rem',
            tablet: '2rem',
            desktop: '3rem'
          },
          centering: {
            auto: 'margin: 0 auto',
            flex: {
              horizontal: 'justify-content: center',
              vertical: 'align-items: center',
              both: 'justify-content: center; align-items: center'
            },
            grid: {
              horizontal: 'justify-items: center',
              vertical: 'align-items: center',
              both: 'place-items: center'
            }
          }
        },
        patterns: {
          stack: {
            gap: {
              xs: '0.25rem',
              sm: '0.5rem',
              md: '1rem',
              lg: '1.5rem',
              xl: '2rem'
            },
            alignment: {
              start: 'align-items: flex-start',
              center: 'align-items: center',
              end: 'align-items: flex-end',
              stretch: 'align-items: stretch'
            }
          },
          cluster: {
            gap: {
              xs: '0.25rem',
              sm: '0.5rem',
              md: '1rem',
              lg: '1.5rem',
              xl: '2rem'
            },
            justify: {
              start: 'justify-content: flex-start',
              center: 'justify-content: center',
              end: 'justify-content: flex-end',
              between: 'justify-content: space-between',
              around: 'justify-content: space-around',
              evenly: 'justify-content: space-evenly'
            },
            align: {
              start: 'align-items: flex-start',
              center: 'align-items: center',
              end: 'align-items: flex-end',
              baseline: 'align-items: baseline'
            }
          },
          sidebar: {
            sidebarWidth: {
              narrow: '200px',
              default: '250px',
              wide: '300px'
            },
            gap: '1rem',
            breakpoint: '768px',
            contentMinWidth: '50%'
          },
          switcher: {
            threshold: '768px',
            gap: '1rem',
            limit: 4
          },
          cover: {
            minHeight: {
              viewport: '100vh',
              container: '100%',
              content: 'auto'
            },
            padding: {
              top: '2rem',
              bottom: '2rem',
              sides: '1rem'
            }
          },
          grid: {
            minItemWidth: {
              xs: '200px',
              sm: '250px',
              md: '300px',
              lg: '350px'
            },
            gap: {
              xs: '0.5rem',
              sm: '1rem',
              md: '1.5rem',
              lg: '2rem',
              xl: '3rem'
            },
            autoFit: true,
            autoFill: false
          }
        },
        validation: {
          rules: [],
          patterns: [],
          accessibility: {
            focusManagement: {
              tabOrder: true,
              focusVisible: true,
              skipLinks: true
            },
            semanticStructure: {
              headingHierarchy: true,
              landmarkRoles: true,
              listStructure: true
            },
            responsiveDesign: {
              minTouchTarget: '44px',
              maxLineLength: '75ch',
              scalableText: true
            }
          }
        }
      },
      metadata: {
        version: '1.0.0',
        generatedAt: '2024-01-01T00:00:00Z',
        source: 'design-system-audit'
      }
    };
  });

  describe('validateCompliance', () => {
    it('should run comprehensive validation across all categories', async () => {
      const report = await engine.validateCompliance(mockFiles, mockTokens);

      expect(report).toBeDefined();
      expect(report.overall).toBeDefined();
      expect(report.byCategory).toBeDefined();
      expect(report.byFile).toBeDefined();
      expect(report.recommendations).toBeDefined();
      expect(report.generatedAt).toBeInstanceOf(Date);
    });

    it('should validate all available categories', async () => {
      const report = await engine.validateCompliance(mockFiles, mockTokens);

      const categories = Object.keys(report.byCategory);
      expect(categories).toContain(ValidationCategory.DESIGN_TOKENS);
      expect(categories).toContain(ValidationCategory.COMPONENT_CONSISTENCY);
      expect(categories).toContain(ValidationCategory.VISUAL_REGRESSION);
    });

    it('should generate overall result from all categories', async () => {
      const report = await engine.validateCompliance(mockFiles, mockTokens);

      expect(report.overall.violations.length).toBeGreaterThan(0);
      expect(report.overall.summary.totalViolations).toBe(report.overall.violations.length);
      expect(report.overall.isValid).toBe(false); // Should have violations from mock files
    });

    it('should generate file breakdown', async () => {
      const report = await engine.validateCompliance(mockFiles, mockTokens);

      expect(Object.keys(report.byFile).length).toBeGreaterThan(0);
      
      // Check that file breakdown contains our mock files
      const fileKeys = Object.keys(report.byFile);
      expect(fileKeys.some(key => key.includes('Button.tsx'))).toBe(true);
    });

    it('should generate actionable recommendations', async () => {
      const report = await engine.validateCompliance(mockFiles, mockTokens);

      expect(report.recommendations.length).toBeGreaterThan(0);
      
      report.recommendations.forEach(rec => {
        expect(rec.id).toBeTruthy();
        expect(rec.title).toBeTruthy();
        expect(rec.description).toBeTruthy();
        expect(rec.category).toBeTruthy();
        expect(rec.priority).toBeGreaterThan(0);
        expect(rec.effort).toBeGreaterThan(0);
        expect(Array.isArray(rec.violations)).toBe(true);
        expect(typeof rec.autoFixable).toBe('boolean');
      });
    });

    it('should sort recommendations by priority', async () => {
      const report = await engine.validateCompliance(mockFiles, mockTokens);

      if (report.recommendations.length > 1) {
        for (let i = 1; i < report.recommendations.length; i++) {
          expect(report.recommendations[i - 1].priority).toBeGreaterThanOrEqual(
            report.recommendations[i].priority
          );
        }
      }
    });
  });

  describe('validateCategory', () => {
    it('should validate specific category', async () => {
      const result = await engine.validateCategory(
        ValidationCategory.DESIGN_TOKENS,
        mockFiles,
        mockTokens
      );

      expect(result).toBeDefined();
      expect(result.violations.length).toBeGreaterThan(0);
      expect(result.summary).toBeDefined();
    });

    it('should throw error for invalid category', async () => {
      await expect(
        engine.validateCategory('invalid-category' as ValidationCategory, mockFiles, mockTokens)
      ).rejects.toThrow('No validator found for category');
    });
  });

  describe('testRemediationImpact', () => {
    it('should test remediation impact', async () => {
      const beforeFiles = mockFiles;
      const afterFiles = mockFiles.map(file => ({
        ...file,
        content: file.content.replace(/#ff0000/g, 'var(--color-primary-500)')
      }));

      const result = await engine.testRemediationImpact(beforeFiles, afterFiles, mockTokens);

      expect(result).toBeDefined();
      expect(result.functional).toBeDefined();
      expect(result.accessibility).toBeDefined();
      expect(result.performance).toBeDefined();
      expect(result.overall).toBeDefined();
    });
  });

  describe('getAvailableCategories', () => {
    it('should return all available validation categories', () => {
      const categories = engine.getAvailableCategories();

      expect(categories).toContain(ValidationCategory.DESIGN_TOKENS);
      expect(categories).toContain(ValidationCategory.COMPONENT_CONSISTENCY);
      expect(categories).toContain(ValidationCategory.VISUAL_REGRESSION);
    });
  });

  describe('registerValidator', () => {
    it('should allow registering custom validators', async () => {
      const mockValidator = {
        name: 'Custom Validator',
        category: ValidationCategory.ACCESSIBILITY,
        rules: [],
        validate: async () => ({
          isValid: true,
          violations: [],
          summary: {
            totalViolations: 0,
            severityBreakdown: {},
            ruleBreakdown: {},
            fileBreakdown: {}
          },
          executionTime: 0
        })
      };

      engine.registerValidator(ValidationCategory.ACCESSIBILITY, mockValidator);

      const categories = engine.getAvailableCategories();
      expect(categories).toContain(ValidationCategory.ACCESSIBILITY);

      const result = await engine.validateCategory(
        ValidationCategory.ACCESSIBILITY,
        mockFiles,
        mockTokens
      );

      expect(result.isValid).toBe(true);
      expect(result.violations.length).toBe(0);
    });
  });
});

describe('convenience functions', () => {
  let mockFiles: ValidationFile[];
  let mockTokens: DesignTokenSystem;

  beforeEach(() => {
    mockFiles = [
      {
        path: 'src/test.tsx',
        content: 'const test = { color: "#ff0000" };',
        type: FileType.COMPONENT,
        lastModified: new Date()
      }
    ];

    mockTokens = {
      colors: {
        primary: { 500: '#0ea5e9' } as any,
        secondary: { 500: '#71717a' } as any,
        semantic: {
          success: { light: '#dcfce7', default: '#16a34a', dark: '#15803d', contrast: '#ffffff' },
          warning: { light: '#fef3c7', default: '#d97706', dark: '#92400e', contrast: '#ffffff' },
          error: { light: '#fee2e2', default: '#dc2626', dark: '#991b1b', contrast: '#ffffff' },
          info: { light: '#dbeafe', default: '#2563eb', dark: '#1d4ed8', contrast: '#ffffff' }
        },
        neutral: { 500: '#71717a' } as any,
        surface: {
          background: '#ffffff',
          foreground: '#09090b',
          card: '#ffffff',
          cardForeground: '#09090b',
          popover: '#ffffff',
          popoverForeground: '#09090b',
          muted: '#f4f4f5',
          mutedForeground: '#71717a',
          accent: '#f4f4f5',
          accentForeground: '#18181b',
          border: '#e4e4e7',
          input: '#e4e4e7',
          ring: '#18181b'
        }
      },
      typography: {} as any,
      spacing: {} as any,
      layout: {} as any,
      metadata: {
        version: '1.0.0',
        generatedAt: '2024-01-01T00:00:00Z',
        source: 'test'
      }
    };
  });

  describe('validateDesignSystemCompliance', () => {
    it('should validate design system compliance', async () => {
      const report = await validateDesignSystemCompliance(mockFiles, mockTokens);

      expect(report).toBeDefined();
      expect(report.overall).toBeDefined();
      expect(report.byCategory).toBeDefined();
      expect(report.recommendations).toBeDefined();
    });
  });

  describe('testRemediationImpact', () => {
    it('should test remediation impact', async () => {
      const beforeFiles = mockFiles;
      const afterFiles = mockFiles.map(file => ({
        ...file,
        content: file.content.replace('#ff0000', 'var(--color-primary-500)')
      }));

      const result = await testRemediationImpact(beforeFiles, afterFiles, mockTokens);

      expect(result).toBeDefined();
      expect(result.functional).toBeDefined();
      expect(result.accessibility).toBeDefined();
      expect(result.performance).toBeDefined();
    });
  });
});