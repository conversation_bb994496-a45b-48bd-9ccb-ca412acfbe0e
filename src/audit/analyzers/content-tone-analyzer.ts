// Content tone analyzer for detecting tone inconsistencies, terminology violations, and messaging pattern issues

import { 
  <PERSON><PERSON><PERSON>, 
  AnalyzerResult, 
  FileInfo, 
  Issue, 
  IssueType, 
  Severity, 
  Category, 
  FileLocation, 
  ImpactAssessment 
} from '../types';
import { auditLogger } from '../core/logger';

interface ToneMatch {
  text: string;
  toneIndicators: string[];
  sentiment: 'positive' | 'negative' | 'neutral' | 'mixed';
  formality: 'formal' | 'informal' | 'mixed';
  line: number;
  column: number;
  context: string;
  type: 'ui_text' | 'error_message' | 'help_text' | 'button_text' | 'heading' | 'description';
}

interface TerminologyMatch {
  term: string;
  variations: string[];
  context: string;
  line: number;
  column: number;
  category: 'technical' | 'business' | 'ui' | 'action';
}

interface MessagingPattern {
  pattern: string;
  type: 'error' | 'success' | 'warning' | 'info' | 'validation';
  consistency: 'consistent' | 'inconsistent';
  examples: string[];
  locations: FileLocation[];
}

export class ContentToneAnalyzer implements Analyzer {
  name = 'content-tone-analyzer';

  // Tone indicators for different sentiment analysis
  private toneIndicators = {
    positive: [
      'great', 'excellent', 'awesome', 'fantastic', 'wonderful', 'amazing',
      'perfect', 'success', 'congratulations', 'well done', 'good job',
      'thank you', 'thanks', 'appreciate', 'love', 'enjoy'
    ],
    negative: [
      'error', 'failed', 'failure', 'wrong', 'incorrect', 'invalid', 'bad',
      'terrible', 'awful', 'horrible', 'disaster', 'broken', 'problem',
      'issue', 'trouble', 'difficulty', 'unfortunately', 'sorry'
    ],
    formal: [
      'please', 'kindly', 'would you', 'could you', 'we recommend',
      'it is recommended', 'please note', 'please be aware', 'furthermore',
      'however', 'therefore', 'consequently', 'additionally'
    ],
    informal: [
      'hey', 'hi there', 'what\'s up', 'cool', 'neat', 'awesome',
      'gonna', 'wanna', 'gotta', 'yeah', 'yep', 'nope', 'ok', 'okay'
    ]
  };

  // Common terminology that should be consistent
  private terminologyDictionary = {
    technical: {
      'log in': ['login', 'sign in', 'sign-in', 'signin'],
      'log out': ['logout', 'sign out', 'sign-out', 'signout'],
      'username': ['user name', 'user-name', 'login name'],
      'password': ['passcode', 'pass word', 'pass-word'],
      'email': ['e-mail', 'email address', 'e-mail address'],
      'website': ['web site', 'web-site', 'site'],
      'homepage': ['home page', 'home-page', 'landing page'],
      'database': ['data base', 'data-base', 'db'],
      'setup': ['set up', 'set-up'],
      'backup': ['back up', 'back-up']
    },
    business: {
      'customer': ['client', 'user', 'member'],
      'purchase': ['buy', 'order', 'transaction'],
      'subscription': ['plan', 'membership', 'service'],
      'account': ['profile', 'user account'],
      'dashboard': ['control panel', 'admin panel', 'overview'],
      'settings': ['preferences', 'configuration', 'options'],
      'support': ['help', 'assistance', 'customer service']
    },
    ui: {
      'button': ['btn', 'link', 'action'],
      'dropdown': ['select', 'picker', 'menu'],
      'checkbox': ['check box', 'tick box'],
      'textbox': ['text box', 'input field', 'text field'],
      'dialog': ['modal', 'popup', 'overlay'],
      'tooltip': ['hint', 'help text', 'info bubble'],
      'breadcrumb': ['navigation', 'path', 'trail']
    },
    action: {
      'click': ['press', 'tap', 'select'],
      'submit': ['send', 'save', 'confirm'],
      'cancel': ['close', 'dismiss', 'abort'],
      'delete': ['remove', 'erase', 'clear'],
      'edit': ['modify', 'change', 'update'],
      'view': ['see', 'display', 'show'],
      'download': ['get', 'fetch', 'retrieve']
    }
  };

  // Message pattern templates for consistency checking
  private messagePatterns = {
    error: [
      /^Error: .+$/,
      /^.+ failed\.?$/i,
      /^Unable to .+$/i,
      /^Could not .+$/i,
      /^.+ is required\.?$/i,
      /^Invalid .+$/i,
      /^Please .+ and try again\.?$/i
    ],
    success: [
      /^Success!? .+$/i,
      /^.+ completed successfully\.?$/i,
      /^.+ has been .+\.?$/i,
      /^Thank you.+$/i,
      /^Congratulations.+$/i
    ],
    warning: [
      /^Warning: .+$/i,
      /^Caution: .+$/i,
      /^Please note.+$/i,
      /^Be aware.+$/i
    ],
    info: [
      /^Info: .+$/i,
      /^Note: .+$/i,
      /^Tip: .+$/i,
      /^Did you know.+$/i
    ],
    validation: [
      /^.+ must be .+$/i,
      /^.+ should be .+$/i,
      /^.+ cannot be .+$/i,
      /^.+ is not .+$/i
    ]
  };

  // Text extraction patterns for different content types
  private textExtractionPatterns = [
    // React/JSX text content
    { pattern: />([^<>{]+)</g, type: 'ui_text' as const },
    // String literals in quotes (longer ones likely to be user-facing)
    { pattern: /['"`]([^'"`\n]{8,})['"`]/g, type: 'ui_text' as const },
    // Error message patterns - more specific
    { pattern: /['"`]Error:\s*([^'"`\n]+)['"`]/gi, type: 'error_message' as const },
    { pattern: /errorMessage\s*[=:]\s*['"`]([^'"`\n]+)['"`]/gi, type: 'error_message' as const },
    { pattern: /error\s*[=:]\s*['"`]([^'"`\n]+)['"`]/gi, type: 'error_message' as const },
    // Alert/toast messages
    { pattern: /(?:alert|toast|message|notification)[:\s]*['"`]([^'"`\n]+)['"`]/gi, type: 'error_message' as const },
    // Button text
    { pattern: /(?:button|btn)[^>]*>([^<]+)</gi, type: 'button_text' as const },
    // Placeholder text
    { pattern: /placeholder\s*=\s*['"`]([^'"`]+)['"`]/gi, type: 'help_text' as const },
    // Title and aria-label attributes
    { pattern: /(?:title|aria-label)\s*=\s*['"`]([^'"`]+)['"`]/gi, type: 'help_text' as const },
    // Heading content
    { pattern: /<h[1-6][^>]*>([^<]+)</gi, type: 'heading' as const },
    // Alt text
    { pattern: /alt\s*=\s*['"`]([^'"`]+)['"`]/gi, type: 'description' as const }
  ];

  async analyze(files: FileInfo[]): Promise<AnalyzerResult> {
    const startTime = Date.now();
    auditLogger.info('Starting content tone analysis', { fileCount: files.length });

    const issues: Issue[] = [];
    const toneMatches: ToneMatch[] = [];
    const terminologyMatches: TerminologyMatch[] = [];
    const messagingPatterns: MessagingPattern[] = [];

    // Filter files that might contain user-facing content
    const relevantFiles = files.filter(file => 
      ['.tsx', '.jsx', '.ts', '.js', '.json', '.md', '.txt'].includes(file.extension) ||
      file.path.includes('component') ||
      file.path.includes('page') ||
      file.path.includes('content') ||
      file.path.includes('text') ||
      file.path.includes('message') ||
      file.path.includes('error') ||
      file.path.includes('i18n') ||
      file.path.includes('locale')
    );

    for (const file of relevantFiles) {
      try {
        const fileIssues = await this.analyzeFile(file, toneMatches, terminologyMatches, messagingPatterns);
        issues.push(...fileIssues);
      } catch (error) {
        auditLogger.error('Failed to analyze file for content tone', error as Error, { 
          filePath: file.path 
        });
      }
    }

    // Analyze tone consistency across files
    const toneIssues = this.analyzeToneConsistency(toneMatches);
    issues.push(...toneIssues);

    // Analyze terminology consistency
    const terminologyIssues = this.analyzeTerminologyConsistency(terminologyMatches);
    issues.push(...terminologyIssues);

    // Analyze messaging pattern consistency
    const messagingIssues = this.analyzeMessagingPatterns(messagingPatterns);
    issues.push(...messagingIssues);

    const executionTime = Date.now() - startTime;
    
    const result: AnalyzerResult = {
      analyzerName: this.name,
      issues,
      summary: {
        totalIssues: issues.length,
        severityBreakdown: this.calculateSeverityBreakdown(issues),
        categoryBreakdown: this.calculateCategoryBreakdown(issues)
      },
      executionTime
    };

    auditLogger.info('Content tone analysis completed', { 
      issuesFound: issues.length,
      executionTime 
    });

    return result;
  }

  private async analyzeFile(
    file: FileInfo, 
    toneMatches: ToneMatch[],
    terminologyMatches: TerminologyMatch[],
    messagingPatterns: MessagingPattern[]
  ): Promise<Issue[]> {
    const issues: Issue[] = [];
    const lines = file.content.split('\n');
    
    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex];
      const lineNumber = lineIndex + 1;

      // Skip comments and code-only lines
      if (this.isCommentLine(line) || this.isCodeOnlyLine(line)) {
        continue;
      }

      // Extract text content from the line
      const textMatches = this.extractTextContent(line, lineNumber);
      
      // Analyze tone for each text match
      for (const textMatch of textMatches) {
        const toneAnalysis = this.analyzeTone(textMatch.text, textMatch.type);
        if (toneAnalysis) {
          const toneMatch: ToneMatch = {
            ...toneAnalysis,
            line: lineNumber,
            column: textMatch.column,
            context: line.trim(),
            type: textMatch.type
          };
          toneMatches.push(toneMatch);

          // Create issues for problematic tone
          const toneIssue = this.createToneIssue(file, toneMatch, lineNumber, line);
          if (toneIssue) {
            issues.push(toneIssue);
          }
        }

        // Check terminology consistency
        const terminologyIssues = this.checkTerminology(file, textMatch.text, lineNumber, textMatch.column, line);
        issues.push(...terminologyIssues);
        
        // Track terminology usage
        const terminology = this.extractTerminology(textMatch.text, lineNumber, textMatch.column);
        terminologyMatches.push(...terminology);
      }

      // Check for messaging patterns
      const messagePattern = this.identifyMessagePattern(line, lineNumber, file.path);
      if (messagePattern) {
        messagingPatterns.push(messagePattern);
      }
    }

    return issues;
  }

  private isCommentLine(line: string): boolean {
    const trimmed = line.trim();
    return trimmed.startsWith('//') || 
           trimmed.startsWith('/*') || 
           trimmed.startsWith('*') ||
           trimmed.startsWith('<!--') ||
           trimmed.startsWith('#');
  }

  private isCodeOnlyLine(line: string): boolean {
    const trimmed = line.trim();
    // Skip lines that are purely code (imports, exports, function declarations, etc.)
    return trimmed.startsWith('import ') ||
           trimmed.startsWith('export ') ||
           trimmed.startsWith('const ') ||
           trimmed.startsWith('let ') ||
           trimmed.startsWith('var ') ||
           trimmed.startsWith('function ') ||
           trimmed.startsWith('class ') ||
           trimmed.startsWith('interface ') ||
           trimmed.startsWith('type ') ||
           trimmed.startsWith('{') ||
           trimmed.startsWith('}') ||
           trimmed === '' ||
           /^[\s\w\(\)\[\]\{\}=<>!&|;,.:]*$/.test(trimmed) && !/"[^"]*[a-zA-Z]{3,}[^"]*"/.test(trimmed);
  }

  private extractTextContent(line: string, lineNumber: number): Array<{text: string, column: number, type: ToneMatch['type']}> {
    const matches: Array<{text: string, column: number, type: ToneMatch['type']}> = [];

    for (const { pattern, type } of this.textExtractionPatterns) {
      let match;
      const regex = new RegExp(pattern.source, pattern.flags);
      
      while ((match = regex.exec(line)) !== null) {
        const text = match[1]?.trim();
        if (text && text.length > 3 && this.isUserFacingText(text)) {
          matches.push({
            text,
            column: match.index,
            type
          });
        }
      }
    }

    // Also check for simple string literals that might contain user-facing text
    const stringLiteralPattern = /['"`]([^'"`\n]{8,})['"`]/g;
    let stringMatch;
    while ((stringMatch = stringLiteralPattern.exec(line)) !== null) {
      const text = stringMatch[1]?.trim();
      if (text && this.isUserFacingText(text)) {
        matches.push({
          text,
          column: stringMatch.index,
          type: 'ui_text'
        });
      }
    }

    return matches;
  }

  private isUserFacingText(text: string): boolean {
    // Filter out code-like strings, variable names, etc.
    if (text.length < 3) return false;
    if (/^[A-Z_][A-Z0-9_]*$/.test(text)) return false; // Constants
    if (/^[a-z][a-zA-Z0-9]*$/.test(text) && text.length < 8) return false; // Variable names
    if (/^[\w.-]+\.(js|ts|jsx|tsx|css|scss|json|html)$/.test(text)) return false; // File names
    if (/^https?:\/\//.test(text)) return false; // URLs
    if (/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(text)) return false; // Emails
    if (/^\d+(\.\d+)*$/.test(text)) return false; // Version numbers
    if (text.includes('()') || text.includes('{}') || text.includes('[]')) return false; // Code snippets
    
    return true;
  }

  private analyzeTone(text: string, type: ToneMatch['type']): Omit<ToneMatch, 'line' | 'column' | 'context' | 'type'> | null {
    const lowerText = text.toLowerCase();
    const words = lowerText.split(/\s+/);
    
    const toneIndicators: string[] = [];
    let positiveCount = 0;
    let negativeCount = 0;
    let formalCount = 0;
    let informalCount = 0;

    // Count tone indicators - check for partial matches too
    words.forEach(word => {
      // Check for exact matches first
      if (this.toneIndicators.positive.includes(word)) {
        toneIndicators.push(`positive:${word}`);
        positiveCount++;
      }
      if (this.toneIndicators.negative.includes(word)) {
        toneIndicators.push(`negative:${word}`);
        negativeCount++;
      }
      if (this.toneIndicators.formal.includes(word)) {
        toneIndicators.push(`formal:${word}`);
        formalCount++;
      }
      if (this.toneIndicators.informal.includes(word)) {
        toneIndicators.push(`informal:${word}`);
        informalCount++;
      }
      
      // Check for partial matches in longer phrases
      this.toneIndicators.positive.forEach(indicator => {
        if (indicator.includes(' ') && lowerText.includes(indicator)) {
          toneIndicators.push(`positive:${indicator}`);
          positiveCount++;
        }
      });
      
      this.toneIndicators.negative.forEach(indicator => {
        if (indicator.includes(' ') && lowerText.includes(indicator)) {
          toneIndicators.push(`negative:${indicator}`);
          negativeCount++;
        }
      });
      
      this.toneIndicators.formal.forEach(indicator => {
        if (indicator.includes(' ') && lowerText.includes(indicator)) {
          toneIndicators.push(`formal:${indicator}`);
          formalCount++;
        }
      });
      
      this.toneIndicators.informal.forEach(indicator => {
        if (indicator.includes(' ') && lowerText.includes(indicator)) {
          toneIndicators.push(`informal:${indicator}`);
          informalCount++;
        }
      });
    });

    // Determine overall sentiment
    let sentiment: ToneMatch['sentiment'] = 'neutral';
    if (positiveCount > negativeCount && positiveCount > 0) {
      sentiment = 'positive';
    } else if (negativeCount > positiveCount && negativeCount > 0) {
      sentiment = 'negative';
    } else if (positiveCount > 0 && negativeCount > 0) {
      sentiment = 'mixed';
    }

    // Determine formality
    let formality: ToneMatch['formality'] = 'formal';
    if (informalCount > formalCount && informalCount > 0) {
      formality = 'informal';
    } else if (formalCount > 0 && informalCount > 0) {
      formality = 'mixed';
    }

    // Return analysis even if no tone indicators found, but with neutral values
    // This allows us to track all text for consistency analysis
    if (toneIndicators.length === 0) {
      // Only return null if the text is too short or not meaningful
      if (text.length < 8 || !this.isUserFacingText(text)) {
        return null;
      }
      // Return neutral analysis for tracking
      return {
        text,
        toneIndicators: [],
        sentiment: 'neutral',
        formality: 'formal' // Default to formal
      };
    }

    return {
      text,
      toneIndicators,
      sentiment,
      formality
    };
  }

  private createToneIssue(
    file: FileInfo, 
    toneMatch: ToneMatch, 
    lineNumber: number, 
    line: string
  ): Issue | null {
    // Flag mixed tone as inconsistent
    if (toneMatch.sentiment === 'mixed' || toneMatch.formality === 'mixed') {
      return {
        id: `tone_mixed_${file.path}_${lineNumber}_${toneMatch.column}`,
        type: IssueType.CONTENT_INCONSISTENCY,
        severity: Severity.MEDIUM,
        category: Category.CONTENT,
        description: `Mixed tone detected in ${toneMatch.type}: "${toneMatch.text}". Contains both ${toneMatch.sentiment} sentiment and ${toneMatch.formality} language.`,
        location: {
          filePath: file.path,
          lineNumber,
          columnNumber: toneMatch.column,
          context: line.trim()
        },
        suggestion: `Maintain consistent tone throughout ${toneMatch.type}. Choose either formal or informal language, and consistent sentiment.`,
        autoFixable: false,
        impact: {
          userExperience: 6,
          maintenanceEffort: 4,
          implementationComplexity: 3
        }
      };
    }

    // Flag inappropriate tone for error messages
    if (toneMatch.type === 'error_message' && toneMatch.sentiment === 'positive') {
      return {
        id: `tone_inappropriate_${file.path}_${lineNumber}_${toneMatch.column}`,
        type: IssueType.CONTENT_INCONSISTENCY,
        severity: Severity.HIGH,
        category: Category.CONTENT,
        description: `Inappropriate positive tone in error message: "${toneMatch.text}". Error messages should be neutral or empathetic.`,
        location: {
          filePath: file.path,
          lineNumber,
          columnNumber: toneMatch.column,
          context: line.trim()
        },
        suggestion: 'Use neutral, helpful tone for error messages. Focus on what went wrong and how to fix it.',
        autoFixable: false,
        impact: {
          userExperience: 8,
          maintenanceEffort: 3,
          implementationComplexity: 2
        }
      };
    }

    return null;
  }

  private checkTerminology(
    file: FileInfo, 
    text: string, 
    lineNumber: number, 
    column: number, 
    line: string
  ): Issue[] {
    const issues: Issue[] = [];
    const lowerText = text.toLowerCase();

    // Check each terminology category
    Object.entries(this.terminologyDictionary).forEach(([category, terms]) => {
      Object.entries(terms).forEach(([preferred, variations]) => {
        variations.forEach(variation => {
          if (lowerText.includes(variation.toLowerCase()) && !lowerText.includes(preferred.toLowerCase())) {
            const issue: Issue = {
              id: `terminology_${category}_${file.path}_${lineNumber}_${column}`,
              type: IssueType.CONTENT_INCONSISTENCY,
              severity: Severity.MEDIUM,
              category: Category.CONTENT,
              description: `Inconsistent terminology: "${variation}" found. Preferred term is "${preferred}".`,
              location: {
                filePath: file.path,
                lineNumber,
                columnNumber: column,
                context: line.trim()
              },
              suggestion: `Replace "${variation}" with "${preferred}" for consistency.`,
              autoFixable: true,
              impact: {
                userExperience: 5,
                maintenanceEffort: 6,
                implementationComplexity: 2
              }
            };
            issues.push(issue);
          }
        });
      });
    });

    return issues;
  }

  private extractTerminology(text: string, lineNumber: number, column: number): TerminologyMatch[] {
    const matches: TerminologyMatch[] = [];
    const lowerText = text.toLowerCase();

    Object.entries(this.terminologyDictionary).forEach(([category, terms]) => {
      Object.entries(terms).forEach(([preferred, variations]) => {
        const allTerms = [preferred, ...variations];
        
        allTerms.forEach(term => {
          if (lowerText.includes(term.toLowerCase())) {
            matches.push({
              term,
              variations: variations,
              context: text,
              line: lineNumber,
              column,
              category: category as TerminologyMatch['category']
            });
          }
        });
      });
    });

    return matches;
  }

  private identifyMessagePattern(line: string, lineNumber: number, filePath: string): MessagingPattern | null {
    const textMatches = this.extractTextContent(line, lineNumber);
    
    for (const textMatch of textMatches) {
      const text = textMatch.text;
      
      // Check against each message pattern type
      for (const [type, patterns] of Object.entries(this.messagePatterns)) {
        for (const pattern of patterns) {
          if (pattern.test(text)) {
            return {
              pattern: pattern.source,
              type: type as MessagingPattern['type'],
              consistency: 'consistent', // Will be determined in pattern analysis
              examples: [text],
              locations: [{
                filePath,
                lineNumber,
                columnNumber: textMatch.column,
                context: line.trim()
              }]
            };
          }
        }
      }
    }

    return null;
  }

  private analyzeToneConsistency(toneMatches: ToneMatch[]): Issue[] {
    const issues: Issue[] = [];
    
    // Group by content type
    const toneByType = new Map<ToneMatch['type'], ToneMatch[]>();
    toneMatches.forEach(match => {
      if (!toneByType.has(match.type)) {
        toneByType.set(match.type, []);
      }
      toneByType.get(match.type)!.push(match);
    });

    // Check consistency within each type
    toneByType.forEach((matches, type) => {
      if (matches.length < 2) return;

      const sentiments = new Set(matches.map(m => m.sentiment));
      const formalities = new Set(matches.map(m => m.formality));

      // Flag inconsistent sentiment within type
      if (sentiments.size > 1 && !sentiments.has('mixed')) {
        const issue: Issue = {
          id: `tone_inconsistent_sentiment_${type}`,
          type: IssueType.CONTENT_INCONSISTENCY,
          severity: Severity.MEDIUM,
          category: Category.CONTENT,
          description: `Inconsistent sentiment in ${type} content. Found: ${Array.from(sentiments).join(', ')}.`,
          location: matches[0].line ? {
            filePath: 'multiple files',
            lineNumber: 0,
            columnNumber: 0,
            context: `${matches.length} instances of ${type} content`
          } : {
            filePath: 'unknown',
            lineNumber: 0,
            columnNumber: 0,
            context: 'Tone analysis'
          },
          suggestion: `Maintain consistent sentiment across all ${type} content. Choose one primary sentiment and stick to it.`,
          autoFixable: false,
          impact: {
            userExperience: 7,
            maintenanceEffort: 6,
            implementationComplexity: 4
          }
        };
        issues.push(issue);
      }

      // Flag inconsistent formality within type
      if (formalities.size > 1 && !formalities.has('mixed')) {
        const issue: Issue = {
          id: `tone_inconsistent_formality_${type}`,
          type: IssueType.CONTENT_INCONSISTENCY,
          severity: Severity.MEDIUM,
          category: Category.CONTENT,
          description: `Inconsistent formality in ${type} content. Found: ${Array.from(formalities).join(', ')}.`,
          location: {
            filePath: 'multiple files',
            lineNumber: 0,
            columnNumber: 0,
            context: `${matches.length} instances of ${type} content`
          },
          suggestion: `Maintain consistent formality level across all ${type} content. Choose either formal or informal tone.`,
          autoFixable: false,
          impact: {
            userExperience: 6,
            maintenanceEffort: 5,
            implementationComplexity: 4
          }
        };
        issues.push(issue);
      }
    });

    return issues;
  }

  private analyzeTerminologyConsistency(terminologyMatches: TerminologyMatch[]): Issue[] {
    const issues: Issue[] = [];
    
    // Group terminology by category and preferred term
    const terminologyUsage = new Map<string, Map<string, TerminologyMatch[]>>();
    
    terminologyMatches.forEach(match => {
      const key = `${match.category}`;
      if (!terminologyUsage.has(key)) {
        terminologyUsage.set(key, new Map());
      }
      
      const categoryMap = terminologyUsage.get(key)!;
      if (!categoryMap.has(match.term)) {
        categoryMap.set(match.term, []);
      }
      categoryMap.get(match.term)!.push(match);
    });

    // Check for inconsistent usage within categories
    terminologyUsage.forEach((categoryMap, category) => {
      // Find terms that have multiple variations used
      Object.entries(this.terminologyDictionary[category as keyof typeof this.terminologyDictionary] || {}).forEach(([preferred, variations]) => {
        const usedVariations = new Set<string>();
        let totalUsage = 0;
        
        [preferred, ...variations].forEach(term => {
          if (categoryMap.has(term)) {
            usedVariations.add(term);
            totalUsage += categoryMap.get(term)!.length;
          }
        });

        if (usedVariations.size > 1 && totalUsage >= 3) {
          const issue: Issue = {
            id: `terminology_inconsistent_${category}_${preferred.replace(/\s+/g, '_')}`,
            type: IssueType.CONTENT_INCONSISTENCY,
            severity: Severity.MEDIUM,
            category: Category.CONTENT,
            description: `Inconsistent terminology for "${preferred}". Found variations: ${Array.from(usedVariations).join(', ')}.`,
            location: {
              filePath: 'multiple files',
              lineNumber: 0,
              columnNumber: 0,
              context: `${totalUsage} total usages across variations`
            },
            suggestion: `Standardize on "${preferred}" throughout the application. Replace all variations with the preferred term.`,
            autoFixable: true,
            impact: {
              userExperience: 6,
              maintenanceEffort: 7,
              implementationComplexity: 3
            }
          };
          issues.push(issue);
        }
      });
    });

    return issues;
  }

  private analyzeMessagingPatterns(messagingPatterns: MessagingPattern[]): Issue[] {
    const issues: Issue[] = [];
    
    // Group patterns by type
    const patternsByType = new Map<MessagingPattern['type'], MessagingPattern[]>();
    messagingPatterns.forEach(pattern => {
      if (!patternsByType.has(pattern.type)) {
        patternsByType.set(pattern.type, []);
      }
      patternsByType.get(pattern.type)!.push(pattern);
    });

    // Check consistency within each message type
    patternsByType.forEach((patterns, type) => {
      if (patterns.length < 2) return;

      // Group by actual pattern structure
      const patternGroups = new Map<string, MessagingPattern[]>();
      patterns.forEach(pattern => {
        if (!patternGroups.has(pattern.pattern)) {
          patternGroups.set(pattern.pattern, []);
        }
        patternGroups.get(pattern.pattern)!.push(pattern);
      });

      // If we have multiple different patterns for the same message type, flag inconsistency
      if (patternGroups.size > 2) {
        const issue: Issue = {
          id: `messaging_pattern_inconsistent_${type}`,
          type: IssueType.CONTENT_INCONSISTENCY,
          severity: Severity.MEDIUM,
          category: Category.CONTENT,
          description: `Inconsistent ${type} message patterns. Found ${patternGroups.size} different patterns.`,
          location: {
            filePath: 'multiple files',
            lineNumber: 0,
            columnNumber: 0,
            context: `${patterns.length} ${type} messages with varying patterns`
          },
          suggestion: `Standardize ${type} message format. Choose one consistent pattern and apply it to all ${type} messages.`,
          autoFixable: false,
          impact: {
            userExperience: 7,
            maintenanceEffort: 6,
            implementationComplexity: 4
          }
        };
        issues.push(issue);
      }
    });

    return issues;
  }

  getSeverity(issue: Omit<Issue, 'severity'>): Severity {
    if (issue.type === IssueType.CONTENT_INCONSISTENCY) {
      if (issue.description.includes('error message') && issue.description.toLowerCase().includes('inappropriate')) {
        return Severity.HIGH;
      }
      if (issue.description.includes('inconsistent') && issue.description.includes('multiple files')) {
        return Severity.MEDIUM;
      }
      if (issue.description.includes('terminology') || issue.description.includes('messaging pattern')) {
        return Severity.MEDIUM;
      }
      if (issue.description.includes('mixed tone')) {
        return Severity.MEDIUM;
      }
    }
    
    return Severity.LOW;
  }

  private calculateSeverityBreakdown(issues: Issue[]): Record<Severity, number> {
    const breakdown: Record<Severity, number> = {
      [Severity.LOW]: 0,
      [Severity.MEDIUM]: 0,
      [Severity.HIGH]: 0,
      [Severity.CRITICAL]: 0
    };

    issues.forEach(issue => {
      breakdown[issue.severity]++;
    });

    return breakdown;
  }

  private calculateCategoryBreakdown(issues: Issue[]): Record<Category, number> {
    const breakdown: Record<Category, number> = {
      [Category.VISUAL]: 0,
      [Category.CONTENT]: 0,
      [Category.COMPONENT]: 0,
      [Category.ACCESSIBILITY]: 0
    };

    issues.forEach(issue => {
      breakdown[issue.category]++;
    });

    return breakdown;
  }
}