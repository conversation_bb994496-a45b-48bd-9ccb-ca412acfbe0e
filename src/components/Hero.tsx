
// SECTION: Imports
// ==================================================================================
import { Button } from "@/components/ui/button";
import NavigationButton from "@/components/ui/NavigationButton";
import GlitchText from "@/components/ui/GlitchText";
import { getContainerClass, getResponsiveTextSize, getResponsivePadding, getTouchTargetClass, getFlexResponsiveClass } from "@/utils/responsive-utilities";
import { cn } from "@/lib/utils";

// SECTION: Hero Component
// ==================================================================================
// Component for the hero section with responsive design and background video
const Hero = () => {
  // --- Helper Functions ---
  // Function to scroll to contact form section
function scrollToContactForm() {

    document.getElementById("contact-form")?.scrollIntoView({ behavior: "smooth" });
  };

  // Function to scroll to programs section
function scrollToPrograms() {

    document.getElementById("programs")?.scrollIntoView({ behavior: "smooth" });
  };

  // --- Component JSX ---
  return (
    <div className="relative overflow-hidden bg-elev-navy h-[60vh] md:h-[65vh] lg:h-[70vh] flex items-center">
      {/* Skip to main content link */}
      <a 
        href="#main-content" 
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-white text-elev-navy px-4 py-2 rounded-md z-50 focus:outline-none focus:ring-2 focus:ring-elev-blue focus:ring-offset-2"
      >
        Skip to main content
      </a>

      {/* --- Background Video & Overlay --- */}
      <div className="absolute inset-0 bg-elev-navy opacity-70 z-0" aria-hidden="true"></div>
      <div className="absolute inset-0 overflow-hidden" aria-hidden="true">
        <div className="relative w-full h-full">
          <iframe 
            className="absolute w-[177.78vh] h-[100vw] md:w-[200%] md:h-[200%] lg:w-[300%] lg:h-[300%] top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 min-w-full min-h-full object-cover"
            src="https://www.youtube.com/embed/HSUdZ9sOQRU?si=bqInnHcesPSZc8Uf&start=6&autoplay=1&mute=1&controls=1&showinfo=0&loop=1&playlist=HSUdZ9sOQRU&disablekb=0&modestbranding=1&playsinline=1&rel=0&enablejsapi=1"
            title="Hockey training at ELEV802 Vegas - Background video"
            aria-describedby="video-description"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          ></iframe>
        </div>
      </div>

      {/* Screen reader description for video */}
      <p id="video-description" className="sr-only">
        Background video showing hockey training sessions at ELEV802 Vegas facility, featuring players practicing various skills and drills on the ice.
      </p>
      
      {/* --- Content --- */}
      <div className={cn(getContainerClass('responsive'), "relative z-10", getResponsivePadding("4", "6", "8"))}>
        <div className="max-w-4xl animate-fade-in">
          <div className={cn("mb-6", getResponsivePadding("6", "8", "10"))}>
            <h1 className={cn("font-extrabold mb-4 leading-tight", getResponsiveTextSize("2xl", "4xl", "6xl"))}>
              <GlitchText text="Elevate Your Hockey Performance in Las Vegas" className={cn("font-extrabold", getResponsiveTextSize("2xl", "4xl", "6xl"))} />
            </h1>
            <p className={cn("text-elev-text-light mb-6 leading-relaxed", getResponsiveTextSize("base", "xl", "2xl"), getResponsivePadding("6", "8", "10"))}>
              <GlitchText text="Access expert coaching, individualized programs, and a state-of-the-art facility with year-round custom ice designed for hockey athletes." className={cn("text-elev-text-light", getResponsiveTextSize("base", "xl", "2xl"))} />
            </p>
          </div>
          
          <div className={cn(getFlexResponsiveClass('col-row'), "gap-4")}>
            <NavigationButton 
              to="/scheduling"
              variant="default"
              className={cn(
                "bg-white hover:bg-elev-background-muted text-elev-navy font-bold rounded-lg shadow-lg transition-all duration-200 hover:scale-105 focus:scale-105 focus:outline-none focus:ring-4 focus:ring-elev-blue",
                getResponsiveTextSize("base", "lg", "xl"),
                getResponsivePadding("3", "4", "6"),
                getTouchTargetClass()
              )}
            >
              Book Now
            </NavigationButton>
            
          </div>
        </div>
      </div>
      
      {/* --- Bottom Diagonal Stripe --- */}
      <div className="absolute bottom-0 left-0 w-full h-16 bg-white transform -skew-y-2 translate-y-8"></div>
    </div>
  );
};

export default Hero;
