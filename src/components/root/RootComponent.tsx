import React, { StrictMode, useEffect } from 'react';
import App from '../../App';
import { register } from '../../utils/registerServiceWorker';

/**
 * Root component that wraps the main application
 * Handles service worker registration in production
 */
export function RootComponent() {
  useEffect(() => {
    // Register service worker in production
    if (process.env.NODE_ENV === 'production') {
      register({
        onSuccess: (registration) => {
          console.log('ServiceWorker registration successful with scope: ', registration.scope);
        },
        onUpdate: (registration) => {
          console.log('New content is available; please refresh.');
          // You can show a notification to the user here
          if (window.confirm('A new version is available! Would you like to update now?')) {
            window.location.reload();
          }
        },
      });
    }
  }, []);

  return (
    <StrictMode>
      <App />
    </StrictMode>
  );
}
