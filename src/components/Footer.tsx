
// SECTION: Imports
// ==================================================================================
import { cn } from "@/lib/utils";
import { getResponsiveTextSize, getResponsivePadding, getTouchTargetClass } from "@/utils/responsive-utilities";

// SECTION: Footer Component
// ==================================================================================
const Footer = () => {
  // --- Component JSX ---
  return (
    <footer className={cn("bg-elev-navy text-white", getResponsivePadding("section"))}>
      <div className={cn("container mx-auto", getResponsivePadding("container"))}>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-10">
          <div>
            <h3 className={cn("font-bold mb-4", getResponsiveTextSize("heading-sm"))}>702HOCKEY</h3>
            <p className={cn("text-elev-text-muted mb-4", getResponsiveTextSize("body"))}>
              Premier hockey and performance training in Las Vegas designed to elevate every aspect of your game.
            </p>
            <div className="flex items-center space-x-3 sm:space-x-4">
              <a href="https://www.facebook.com/p/Elev802-Vegas-100092532255106/" target="_blank" rel="noopener noreferrer" className={cn("text-elev-text-light hover:text-elev-blue transition-colors touch-manipulation", getTouchTargetClass())}>
                <span className="sr-only">Facebook</span>
                <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
                </svg>
              </a>
              <a href="https://www.instagram.com/elev802_vegas/" target="_blank" rel="noopener noreferrer" className={cn("text-elev-text-light hover:text-elev-blue transition-colors touch-manipulation", getTouchTargetClass())}>
                <span className="sr-only">Instagram</span>
                <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path fillRule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772a4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clipRule="evenodd" />
                </svg>
              </a>

            </div>
          </div>

          <div>
            <h3 className={cn("font-bold mb-4", getResponsiveTextSize("heading-sm"))}>Contact</h3>
            <address className={cn("not-italic text-elev-text-light", getResponsiveTextSize("body"))}>
              <p className="mb-2 text-white">5031 Wagon Trail Ave STE 100</p>
              <p className="mb-2 text-white">Las Vegas, NV 89118</p>
              <p className="mb-2">
                <a href="tel:(*************" className={cn("hover:text-elev-text-muted transition-colors touch-manipulation", getTouchTargetClass())}>
                  (*************
                </a>
              </p>
              <p>
                <a href="mailto:<EMAIL>" className={cn("hover:text-elev-text-muted transition-colors touch-manipulation", getTouchTargetClass())}>
                  <EMAIL>
                </a>
              </p>
            </address>
          </div>

          <div>
            <h3 className={cn("font-bold mb-4", getResponsiveTextSize("heading-sm"))}>Quick Links</h3>
            <nav>
              <ul className="space-y-2 sm:space-y-3">
                <li>
                  <a
                    href="/#why-choose-us"
                    className={cn("text-white hover:text-elev-text-light transition-colors touch-manipulation", getResponsiveTextSize("body"), getTouchTargetClass())}
                  >
                    Why Choose Us
                  </a>
                </li>
                <li>
                  <a
                    href="/#facility"
                    className={cn("text-white hover:text-elev-text-light transition-colors touch-manipulation", getResponsiveTextSize("body"), getTouchTargetClass())}
                  >
                    Our Facility
                  </a>
                </li>
                <li>
                  <a
                    href="/#programs"
                    className={cn("text-white hover:text-elev-text-light transition-colors touch-manipulation", getResponsiveTextSize("body"), getTouchTargetClass())}
                  >
                    Training Programs
                  </a>
                </li>
                <li>
                  <a
                    href="/#coaches"
                    className={cn("text-white hover:text-elev-text-light transition-colors touch-manipulation", getResponsiveTextSize("body"), getTouchTargetClass())}
                  >
                    Our Coaches
                  </a>
                </li>
                <li>
                  <a
                    href="/#contact-form"
                    className={cn("text-white hover:text-elev-text-light transition-colors touch-manipulation", getResponsiveTextSize("body"), getTouchTargetClass())}
                  >
                    Contact Us
                  </a>
                </li>
              </ul>
            </nav>
          </div>
        </div>

        <div className={cn("mt-8 sm:mt-10 pt-4 sm:pt-6 border-t border-elev-background-accent text-center text-elev-text-muted", getResponsiveTextSize("small"))}>
          <p>© {new Date().getFullYear()} 702HOCKEY. All rights reserved.</p>
          <p className="mt-2 flex flex-col sm:flex-row justify-center items-center gap-2 sm:gap-4">
            <a href="/privacy-policy" className={cn("text-elev-text-light hover:text-white transition-colors touch-manipulation", getTouchTargetClass())}>Privacy Policy</a>
            <a href="/terms-of-service" className={cn("text-elev-text-light hover:text-white transition-colors touch-manipulation", getTouchTargetClass())}>Terms of Service</a>
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
