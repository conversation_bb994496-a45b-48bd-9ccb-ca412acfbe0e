import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (replaces cacheTime in v5)
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
});

// Helper function to prefetch data for a specific route
export const prefetchData = async <T,>(
  queryKey: string[] | readonly string[],
  queryFn: () => Promise<T>
): Promise<void> => {
  // Ensure we have a mutable copy of the query key
  const mutableQueryKey = [...queryKey];
  
  await queryClient.prefetchQuery({
    queryKey: mutableQueryKey,
    queryFn,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
