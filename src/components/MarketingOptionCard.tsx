// SECTION: Imports
// ==================================================================================
import React from 'react';
import { Button } from '@/components/ui/button';

// SECTION: Interface Definition
// ==================================================================================
// Define a type for marketing options (should match the one in MarketingPackageBuilderPage.tsx)
interface MarketingOption {
  id: string;
  name: string;
  price: string;
  description: string;
  details: string[];
  type: 'checkbox' | 'quantity';
  cost: number;
  selected?: boolean; // Optional for checkbox type
  quantity?: number; // Optional for quantity type
}

// SECTION: MarketingOptionCard Component
// ==================================================================================
const MarketingOptionCard = ({ option, onSelect, onQuantityChange }: { option: MarketingOption, onSelect: (id: string) => void, onQuantityChange: (id: string, delta: number) => void }) => {
  // --- Component JSX ---
  return (
    <div className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
      {/* --- Audience Stats Banner (Conditional) --- */}
      <div className="mb-4 bg-elev-blue/10 p-3 rounded-lg">
        <div className="flex justify-between text-sm text-elev-text-primary">
          {option.id === 'center-ice-logo' && (
            <>
              <div className="flex items-center">
                <svg className="h-5 w-5 text-elev-blue mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <span>130,000+ Annual Visitors</span>
              </div>

              <div className="flex items-center">
                <svg className="h-5 w-5 text-elev-blue mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                <span>90% Local Residents</span>
              </div>
            </>
          )}
          {option.id === 'prime-dasherboard' && (
            <>
              <div className="flex items-center">
                <svg className="h-5 w-5 text-elev-blue mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <span>95,000+ Monthly Views</span>
              </div>
              <div className="flex items-center">
                <svg className="h-5 w-5 text-elev-blue mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>$125K+ Avg. Income</span>
              </div>

            </>
          )}
          {option.id === 'standard-dasherboard' && (
            <>
              <div className="flex items-center">
                <svg className="h-5 w-5 text-elev-blue mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <span>80,000+ Event Attendees</span>
              </div>

              <div className="flex items-center">
                <svg className="h-5 w-5 text-elev-blue mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                <span>80% Return Visitors</span>
              </div>
            </>
          )}
          {option.id === 'digital-presence' && (
            <>

              <div className="flex items-center">
                <svg className="h-5 w-5 text-elev-blue mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>85% Age 25-45</span>
              </div>
              <div className="flex items-center">
                <svg className="h-5 w-5 text-elev-blue mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                <span>92% Digital Engagement</span>
              </div>
            </>
          )}
        </div>
      </div>
      {/* --- Option Icon, Name, and Price --- */}
      <div className="flex items-center mb-6">
        {/* --- Conditional Icons --- */}
        <div className="w-12 h-12 flex items-center justify-center mr-4 bg-gradient-to-r from-elev-blue to-elev-blue/80 rounded-full">
          {option.id === 'center-ice-logo' && (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          )}
          {option.id === 'prime-dasherboard' && (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
            </svg>
          )}
          {option.id === 'standard-dasherboard' && (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          )}
          {option.id === 'digital-presence' && (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
            </svg>
          )}
        </div>
        <div>
          <h3 className="text-2xl font-bold text-elev-text-primary">{option.name}</h3>
          <p className="text-lg font-semibold text-elev-blue">{option.price}</p>
        </div>
      </div>
      <p className="text-elev-text-primary mb-6 text-lg">{option.description}</p>
      {/* --- Engagement Metrics (Conditional) --- */}
      <div className="grid grid-cols-3 gap-4 mb-6 bg-elev-background-muted p-4 rounded-lg">
        {option.id === 'center-ice-logo' && (
          <>
            <div className="text-center">
              <div className="text-2xl font-bold text-elev-blue">2,500+</div>
              <div className="text-sm text-elev-text-secondary">Weekly Impressions</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-elev-blue">130K+</div>
              <div className="text-sm text-elev-text-secondary">Annual Views</div>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-elev-blue">90%</div>
              <div className="text-sm text-elev-text-secondary">Local Market Reach</div>
            </div>
          </>
        )}
        {option.id === 'prime-dasherboard' && (
          <>
            <div className="text-center">
              <div className="text-2xl font-bold text-elev-blue">1,800+</div>
              <div className="text-sm text-elev-text-secondary">Weekly Impressions</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-elev-blue">95K+</div>
              <div className="text-sm text-elev-text-secondary">Monthly Views</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-elev-blue">125K+</div>
              <div className="text-sm text-elev-text-secondary">Avg. Household Income</div>
            </div>

          </>
        )}
        {option.id === 'standard-dasherboard' && (
          <>
            <div className="text-center">
              <div className="text-2xl font-bold text-elev-blue">1,500+</div>
              <div className="text-sm text-elev-text-secondary">Weekly Impressions</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-elev-blue">80K+</div>
              <div className="text-sm text-elev-text-secondary">Event Attendees</div>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-elev-blue">80%</div>
              <div className="text-sm text-elev-text-secondary">Return Rate</div>
            </div>
          </>
        )}
        {option.id === 'digital-presence' && (
          <>

            <div className="text-center">
              <div className="text-2xl font-bold text-elev-blue">85%</div>
              <div className="text-sm text-elev-text-secondary">Age 25-45</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-elev-blue">92%</div>
              <div className="text-sm text-elev-text-secondary">Digital Engagement</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-elev-blue">3K+</div>
              <div className="text-sm text-elev-text-secondary">Monthly Interactions</div>
            </div>
          </>
        )}
      </div>
      {/* --- Details/Features List --- */}
      <div className="mb-6 bg-elev-background-muted p-4 rounded-lg">
        <ul className="space-y-3">
          {option.details.map((detail, index) => (
            <li key={index} className="flex items-start">
              <svg className="h-6 w-6 text-elev-blue mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span className="text-elev-text-primary">{detail}</span>
            </li>
          ))}
        </ul>
      </div>
      {/* --- Checkbox Type Input --- */}
      {option.type === 'checkbox' && (
        <label className="flex items-center bg-elev-background-muted p-4 rounded-lg cursor-pointer hover:bg-elev-background-accent transition-colors duration-200">
          <input
            type="checkbox"
            className="w-5 h-5 text-elev-blue rounded border-elev-background-accent focus:ring-elev-blue"
            checked={option.selected}
            onChange={() => onSelect(option.id)}
            aria-label={`Include ${option.name} in package`}
          />
          <span className="ml-3 text-elev-text-primary font-medium">Include {option.name}</span>
        </label>
      )}
      {/* --- Quantity Type Input --- */}
      {option.type === 'quantity' && (
        <div className="bg-elev-background-muted p-4 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-elev-text-primary font-medium">Select Quantity:</span>
            <div className="flex items-center space-x-2">
              <button
                className="w-8 h-8 flex items-center justify-center bg-white border border-elev-background-accent rounded-full hover:bg-elev-background-muted focus:outline-none focus:ring-2 focus:ring-elev-blue"
                onClick={() => onQuantityChange(option.id, -1)}
                aria-label={`Decrease quantity for ${option.name}`}
              >
                <svg className="w-4 h-4 text-elev-text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                </svg>
              </button>
              <span className="w-12 text-center font-semibold text-elev-text-primary">{option.quantity}</span>
              <button
                className="w-8 h-8 flex items-center justify-center bg-white border border-elev-background-accent rounded-full hover:bg-elev-background-muted focus:outline-none focus:ring-2 focus:ring-elev-blue"
                onClick={() => onQuantityChange(option.id, 1)}
                aria-label={`Increase quantity for ${option.name}`}
              >
                <svg className="w-4 h-4 text-elev-text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MarketingOptionCard;