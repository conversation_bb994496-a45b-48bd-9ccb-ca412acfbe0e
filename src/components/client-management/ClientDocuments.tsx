import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { FileText, Download, Eye } from 'lucide-react';

/**
 * ClientDocuments component displays and manages client documents
 * Provides functionality to view, download, and organize client files
 */
interface Document {
  id: string;
  name: string;
  type: string;
  size: string;
  uploadDate: string;
  status: 'pending' | 'approved' | 'rejected';
}

interface ClientDocumentsProps {
  clientId?: string;
  documents?: Document[];
}

const ClientDocuments: React.FC<ClientDocumentsProps> = ({ 
  clientId, 
  documents = [] 
}) => {
  // Mock data for demonstration
  const mockDocuments: Document[] = [
    {
      id: '1',
      name: 'Training Agreement.pdf',
      type: 'PDF',
      size: '2.4 MB',
      uploadDate: '2024-01-15',
      status: 'approved'
    },
    {
      id: '2',
      name: 'Medical Clearance.pdf',
      type: 'PDF',
      size: '1.8 MB',
      uploadDate: '2024-01-10',
      status: 'pending'
    },
    {
      id: '3',
      name: 'Emergency Contact.pdf',
      type: 'PDF',
      size: '1.2 MB',
      uploadDate: '2024-01-08',
      status: 'approved'
    }
  ];

  const displayDocuments = documents.length > 0 ? documents : mockDocuments;

  /**
   * Get badge variant based on document status
   */
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'approved':
        return 'default';
      case 'pending':
        return 'secondary';
      case 'rejected':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  /**
   * Handle document download
   */
  const handleDownload = (documentId: string) => {
    // Implementation for document download
    console.log('Downloading document:', documentId);
  };

  /**
   * Handle document view
   */
  const handleView = (documentId: string) => {
    // Implementation for document view
    console.log('Viewing document:', documentId);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Client Documents
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {displayDocuments.length === 0 ? (
            <p className="text-elev-text-muted text-center py-8">
              No documents uploaded yet.
            </p>
          ) : (
            displayDocuments.map((document) => (
              <div
                key={document.id}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-elev-background-muted transition-colors"
              >
                <div className="flex items-center gap-3">
                  <FileText className="h-8 w-8 text-blue-500" />
                  <div>
                    <h4 className="font-medium text-elev-text-primary">
                      {document.name}
                    </h4>
                    <p className="text-sm text-elev-text-muted">
                      {document.type} • {document.size} • {document.uploadDate}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={getStatusBadgeVariant(document.status)}>
                    {document.status.charAt(0).toUpperCase() + document.status.slice(1)}
                  </Badge>
                  <div className="flex gap-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleView(document.id)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDownload(document.id)}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ClientDocuments;