// File system scanning utilities for component and style files

import { FileInfo } from '../types';
import { auditLogger } from './logger';

export interface ScanOptions {
  includePatterns: string[];
  excludePatterns: string[];
  maxFileSize: number; // in bytes
  followSymlinks: boolean;
}

export class FileScanner {
  private defaultOptions: ScanOptions = {
    includePatterns: [
      '**/*.tsx',
      '**/*.jsx',
      '**/*.ts',
      '**/*.js',
      '**/*.css',
      '**/*.scss',
      '**/*.module.css',
      '**/*.json'
    ],
    excludePatterns: [
      '**/node_modules/**',
      '**/dist/**',
      '**/build/**',
      '**/.git/**',
      '**/coverage/**',
      '**/*.test.*',
      '**/*.spec.*'
    ],
    maxFileSize: 1024 * 1024, // 1MB
    followSymlinks: false
  };

  constructor(private options: Partial<ScanOptions> = {}) {
    this.options = { ...this.defaultOptions, ...options };
  }

  async scanDirectory(directoryPath: string): Promise<FileInfo[]> {
    auditLogger.info('Starting directory scan', { directoryPath });
    
    try {
      const files: FileInfo[] = [];
      await this.scanDirectoryRecursive(directoryPath, files);
      
      auditLogger.info('Directory scan completed', { 
        totalFiles: files.length,
        directoryPath 
      });
      
      return files;
    } catch (error) {
      auditLogger.error('Failed to scan directory', error as Error, { directoryPath });
      throw error;
    }
  }

  private async scanDirectoryRecursive(dirPath: string, files: FileInfo[]): Promise<void> {
    try {
      // In a real implementation, we would use Node.js fs module
      // For now, we'll create a mock implementation that works with the browser environment
      auditLogger.debug('Scanning directory', { dirPath });
      
      // This is a placeholder - in a real implementation, you would:
      // 1. Use fs.readdir to get directory contents
      // 2. Use fs.stat to get file information
      // 3. Filter files based on include/exclude patterns
      // 4. Read file contents for matching files
      // 5. Recursively scan subdirectories
      
      // For demonstration, we'll return empty for now
      // The actual implementation would require Node.js environment
      
    } catch (error) {
      auditLogger.warn('Failed to scan directory', { dirPath, error: (error as Error).message });
    }
  }

  async scanFiles(filePaths: string[]): Promise<FileInfo[]> {
    auditLogger.info('Starting file scan', { fileCount: filePaths.length });
    
    const files: FileInfo[] = [];
    
    for (const filePath of filePaths) {
      try {
        const fileInfo = await this.scanFile(filePath);
        if (fileInfo) {
          files.push(fileInfo);
        }
      } catch (error) {
        auditLogger.warn('Failed to scan file', { filePath, error: (error as Error).message });
      }
    }
    
    auditLogger.info('File scan completed', { scannedFiles: files.length });
    return files;
  }

  private async scanFile(filePath: string): Promise<FileInfo | null> {
    try {
      // Check if file matches include/exclude patterns
      if (!this.shouldIncludeFile(filePath)) {
        auditLogger.debug('File excluded by patterns', { filePath });
        return null;
      }

      // In a real implementation, we would:
      // 1. Use fs.stat to get file stats
      // 2. Check file size against maxFileSize
      // 3. Use fs.readFile to read content
      // 4. Return FileInfo object
      
      // Mock implementation for browser environment
      const fileInfo: FileInfo = {
        path: filePath,
        content: '', // Would be actual file content
        extension: this.getFileExtension(filePath),
        size: 0, // Would be actual file size
        lastModified: new Date()
      };

      auditLogger.debug('File scanned successfully', { filePath });
      return fileInfo;
      
    } catch (error) {
      auditLogger.error('Failed to scan file', error as Error, { filePath });
      return null;
    }
  }

  private shouldIncludeFile(filePath: string): boolean {
    const { includePatterns, excludePatterns } = this.options;
    
    // Check exclude patterns first
    if (excludePatterns?.some(pattern => this.matchesPattern(filePath, pattern))) {
      return false;
    }
    
    // Check include patterns
    return includePatterns?.some(pattern => this.matchesPattern(filePath, pattern)) ?? false;
  }

  private matchesPattern(filePath: string, pattern: string): boolean {
    // Simple pattern matching - in a real implementation, you'd use a proper glob library
    const regexPattern = pattern
      .replace(/\*\*/g, '.*')
      .replace(/\*/g, '[^/]*')
      .replace(/\?/g, '.');
    
    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(filePath);
  }

  private getFileExtension(filePath: string): string {
    const lastDot = filePath.lastIndexOf('.');
    return lastDot > 0 ? filePath.substring(lastDot) : '';
  }

  getFileTypes(): string[] {
    return [
      '.tsx', '.jsx', '.ts', '.js',  // React/JavaScript files
      '.css', '.scss', '.module.css', // Style files
      '.json'  // Configuration files
    ];
  }

  isComponentFile(filePath: string): boolean {
    const componentExtensions = ['.tsx', '.jsx'];
    const extension = this.getFileExtension(filePath);
    return componentExtensions.includes(extension);
  }

  isStyleFile(filePath: string): boolean {
    const styleExtensions = ['.css', '.scss', '.module.css'];
    const extension = this.getFileExtension(filePath);
    return styleExtensions.includes(extension);
  }

  isConfigFile(filePath: string): boolean {
    const configExtensions = ['.json'];
    const extension = this.getFileExtension(filePath);
    const configFiles = ['tailwind.config', 'theme', 'design-tokens'];
    
    return configExtensions.includes(extension) && 
           configFiles.some(config => filePath.includes(config));
  }
}