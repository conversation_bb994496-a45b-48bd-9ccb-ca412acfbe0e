import * as React from "react"

/**
 * Sidebar context interface
 */
export interface SidebarContext {
  state: "expanded" | "collapsed"
  open: boolean
  setOpen: (open: boolean) => void
  openMobile: boolean
  setOpenMobile: (open: boolean) => void
  isMobile: boolean
  toggleSidebar: () => void
}

/**
 * Sidebar context for managing sidebar state
 */
export const SidebarContext = React.createContext<SidebarContext | null>(null)

/**
 * Custom hook for accessing sidebar context
 */
export const useSidebar = () => {
  const context = React.useContext(SidebarContext)
  if (!context) {
    throw new Error("useSidebar must be used within a SidebarProvider")
  }
  return context
}
