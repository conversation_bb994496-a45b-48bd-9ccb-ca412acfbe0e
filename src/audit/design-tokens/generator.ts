/**
 * Design Token Generator
 * Creates comprehensive design token system with color palettes, typography scales, and spacing systems
 */

import { 
  DesignTokenSystem, 
  ColorTokens, 
  TypographyTokens, 
  SpacingTokens,
  LayoutTokens,
  ColorScale,
  SemanticColors,
  SurfaceColors,
  TypeScale,
  TypographyStyle
} from './types.js';

export class DesignTokenGenerator {
  /**
   * Generates a complete design token system
   */
  generateTokenSystem(): DesignTokenSystem {
    return {
      colors: this.generateColorTokens(),
      typography: this.generateTypographyTokens(),
      spacing: this.generateSpacingTokens(),
      layout: this.generateLayoutTokens(),
      metadata: {
        version: '1.0.0',
        generatedAt: new Date().toISOString(),
        source: 'design-system-audit-tool'
      }
    };
  }

  /**
   * Generates comprehensive color palette with semantic tokens
   */
  private generateColorTokens(): ColorTokens {
    return {
      primary: this.generatePrimaryColorScale(),
      secondary: this.generateSecondaryColorScale(),
      semantic: this.generateSemanticColors(),
      neutral: this.generateNeutralColorScale(),
      surface: this.generateSurfaceColors()
    };
  }

  private generatePrimaryColorScale(): ColorScale {
    return {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
      950: '#172554'
    };
  }

  private generateSecondaryColorScale(): ColorScale {
    return {
      50: '#f0f9ff',
      100: '#e0f2fe',
      200: '#bae6fd',
      300: '#7dd3fc',
      400: '#38bdf8',
      500: '#0ea5e9',
      600: '#0284c7',
      700: '#0369a1',
      800: '#075985',
      900: '#0c4a6e',
      950: '#082f49'
    };
  }

  private generateNeutralColorScale(): ColorScale {
    return {
      50: '#f8fafc',
      100: '#f1f5f9',
      200: '#e2e8f0',
      300: '#cbd5e1',
      400: '#94a3b8',
      500: '#64748b',
      600: '#475569',
      700: '#334155',
      800: '#1e293b',
      900: '#0f172a',
      950: '#020617'
    };
  }

  private generateSemanticColors(): SemanticColors {
    return {
      success: {
        light: '#dcfce7',
        default: '#16a34a',
        dark: '#15803d',
        contrast: '#ffffff'
      },
      warning: {
        light: '#fef3c7',
        default: '#f59e0b',
        dark: '#d97706',
        contrast: '#ffffff'
      },
      error: {
        light: '#fee2e2',
        default: '#dc2626',
        dark: '#b91c1c',
        contrast: '#ffffff'
      },
      info: {
        light: '#dbeafe',
        default: '#3b82f6',
        dark: '#2563eb',
        contrast: '#ffffff'
      }
    };
  }

  private generateSurfaceColors(): SurfaceColors {
    return {
      background: '#ffffff',
      foreground: '#0f172a',
      card: '#ffffff',
      cardForeground: '#0f172a',
      popover: '#ffffff',
      popoverForeground: '#0f172a',
      muted: '#f1f5f9',
      mutedForeground: '#64748b',
      accent: '#f1f5f9',
      accentForeground: '#0f172a',
      border: '#e2e8f0',
      input: '#e2e8f0',
      ring: '#3b82f6'
    };
  }

  /**
   * Generates typography scale with consistent sizing and hierarchy
   */
  private generateTypographyTokens(): TypographyTokens {
    const fontFamilies = {
      sans: ['Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
      serif: ['Georgia', 'Cambria', 'Times New Roman', 'Times', 'serif'],
      mono: ['JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace'],
      display: ['Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif']
    };

    return {
      fontFamilies,
      fontSizes: {
        xs: '0.75rem',    // 12px
        sm: '0.875rem',   // 14px
        base: '1rem',     // 16px
        lg: '1.125rem',   // 18px
        xl: '1.25rem',    // 20px
        '2xl': '1.5rem',  // 24px
        '3xl': '1.875rem', // 30px
        '4xl': '2.25rem', // 36px
        '5xl': '3rem',    // 48px
        '6xl': '3.75rem', // 60px
        '7xl': '4.5rem',  // 72px
        '8xl': '6rem',    // 96px
        '9xl': '8rem'     // 128px
      },
      fontWeights: {
        thin: 100,
        extralight: 200,
        light: 300,
        normal: 400,
        medium: 500,
        semibold: 600,
        bold: 700,
        extrabold: 800,
        black: 900
      },
      lineHeights: {
        none: 1,
        tight: 1.25,
        snug: 1.375,
        normal: 1.5,
        relaxed: 1.625,
        loose: 2
      },
      letterSpacing: {
        tighter: '-0.05em',
        tight: '-0.025em',
        normal: '0em',
        wide: '0.025em',
        wider: '0.05em',
        widest: '0.1em'
      },
      typeScale: this.generateTypeScale(fontFamilies)
    };
  }

  private generateTypeScale(fontFamilies: any): TypeScale {
    return {
      h1: {
        fontSize: '3.75rem', // 60px
        fontWeight: 700,
        lineHeight: 1.2,
        letterSpacing: '-0.025em',
        fontFamily: fontFamilies.display
      },
      h2: {
        fontSize: '3rem', // 48px
        fontWeight: 600,
        lineHeight: 1.25,
        letterSpacing: '-0.025em',
        fontFamily: fontFamilies.display
      },
      h3: {
        fontSize: '2.25rem', // 36px
        fontWeight: 600,
        lineHeight: 1.3,
        letterSpacing: '0em',
        fontFamily: fontFamilies.display
      },
      h4: {
        fontSize: '1.875rem', // 30px
        fontWeight: 600,
        lineHeight: 1.35,
        letterSpacing: '0em',
        fontFamily: fontFamilies.sans
      },
      h5: {
        fontSize: '1.5rem', // 24px
        fontWeight: 600,
        lineHeight: 1.4,
        letterSpacing: '0em',
        fontFamily: fontFamilies.sans
      },
      h6: {
        fontSize: '1.25rem', // 20px
        fontWeight: 600,
        lineHeight: 1.4,
        letterSpacing: '0em',
        fontFamily: fontFamilies.sans
      },
      body: {
        fontSize: '1rem', // 16px
        fontWeight: 400,
        lineHeight: 1.5,
        letterSpacing: '0em',
        fontFamily: fontFamilies.sans
      },
      bodyLarge: {
        fontSize: '1.125rem', // 18px
        fontWeight: 400,
        lineHeight: 1.5,
        letterSpacing: '0em',
        fontFamily: fontFamilies.sans
      },
      bodySmall: {
        fontSize: '0.875rem', // 14px
        fontWeight: 400,
        lineHeight: 1.5,
        letterSpacing: '0em',
        fontFamily: fontFamilies.sans
      },
      caption: {
        fontSize: '0.75rem', // 12px
        fontWeight: 400,
        lineHeight: 1.4,
        letterSpacing: '0.025em',
        fontFamily: fontFamilies.sans
      },
      overline: {
        fontSize: '0.75rem', // 12px
        fontWeight: 600,
        lineHeight: 1.4,
        letterSpacing: '0.1em',
        fontFamily: fontFamilies.sans
      }
    };
  }

  /**
   * Generates spacing system with standardized values
   */
  private generateSpacingTokens(): SpacingTokens {
    return {
      scale: {
        0: '0px',
        px: '1px',
        0.5: '0.125rem',  // 2px
        1: '0.25rem',     // 4px
        1.5: '0.375rem',  // 6px
        2: '0.5rem',      // 8px
        2.5: '0.625rem',  // 10px
        3: '0.75rem',     // 12px
        3.5: '0.875rem',  // 14px
        4: '1rem',        // 16px
        5: '1.25rem',     // 20px
        6: '1.5rem',      // 24px
        7: '1.75rem',     // 28px
        8: '2rem',        // 32px
        9: '2.25rem',     // 36px
        10: '2.5rem',     // 40px
        11: '2.75rem',    // 44px
        12: '3rem',       // 48px
        14: '3.5rem',     // 56px
        16: '4rem',       // 64px
        20: '5rem',       // 80px
        24: '6rem',       // 96px
        28: '7rem',       // 112px
        32: '8rem',       // 128px
        36: '9rem',       // 144px
        40: '10rem',      // 160px
        44: '11rem',      // 176px
        48: '12rem',      // 192px
        52: '13rem',      // 208px
        56: '14rem',      // 224px
        60: '15rem',      // 240px
        64: '16rem',      // 256px
        72: '18rem',      // 288px
        80: '20rem',      // 320px
        96: '24rem'       // 384px
      },
      semantic: {
        component: {
          padding: {
            xs: '0.5rem',   // 8px
            sm: '0.75rem',  // 12px
            md: '1rem',     // 16px
            lg: '1.5rem',   // 24px
            xl: '2rem'      // 32px
          },
          margin: {
            xs: '0.5rem',   // 8px
            sm: '1rem',     // 16px
            md: '1.5rem',   // 24px
            lg: '2rem',     // 32px
            xl: '3rem'      // 48px
          },
          gap: {
            xs: '0.25rem',  // 4px
            sm: '0.5rem',   // 8px
            md: '1rem',     // 16px
            lg: '1.5rem',   // 24px
            xl: '2rem'      // 32px
          }
        },
        layout: {
          section: '4rem',    // 64px
          container: '2rem',  // 32px
          content: '1.5rem'   // 24px
        }
      },
      layout: {
        containerMaxWidth: {
          sm: '640px',
          md: '768px',
          lg: '1024px',
          xl: '1280px',
          '2xl': '1536px'
        },
        containerPadding: {
          mobile: '1rem',   // 16px
          tablet: '2rem',   // 32px
          desktop: '3rem'   // 48px
        }
      }
    };
  }

  /**
   * Generates layout and grid system with responsive breakpoints and patterns
   */
  private generateLayoutTokens(): LayoutTokens {
    return {
      grid: this.generateGridSystem(),
      breakpoints: this.generateBreakpoints(),
      containers: this.generateContainerSystem(),
      patterns: this.generateLayoutPatterns(),
      validation: this.generateLayoutValidation()
    };
  }

  private generateGridSystem() {
    return {
      columns: {
        default: 12,
        sm: 4,
        md: 8,
        lg: 12,
        xl: 12
      },
      gutters: {
        xs: '0.5rem',   // 8px
        sm: '1rem',     // 16px
        md: '1.5rem',   // 24px
        lg: '2rem',     // 32px
        xl: '3rem'      // 48px
      },
      margins: {
        mobile: '1rem',   // 16px
        tablet: '2rem',   // 32px
        desktop: '3rem',  // 48px
        wide: '4rem'      // 64px
      },
      baseline: {
        unit: '0.25rem', // 4px
        scale: 4
      }
    };
  }

  private generateBreakpoints() {
    return {
      xs: '0px',
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1280px',
      '2xl': '1536px',
      ranges: {
        mobile: '(max-width: 767px)',
        tablet: '(min-width: 768px) and (max-width: 1023px)',
        desktop: '(min-width: 1024px) and (max-width: 1535px)',
        wide: '(min-width: 1536px)'
      }
    };
  }

  private generateContainerSystem() {
    return {
      maxWidths: {
        xs: '100%',
        sm: '640px',
        md: '768px',
        lg: '1024px',
        xl: '1280px',
        '2xl': '1536px',
        full: '100%'
      },
      padding: {
        mobile: '1rem',   // 16px
        tablet: '2rem',   // 32px
        desktop: '3rem'   // 48px
      },
      centering: {
        auto: 'margin: 0 auto',
        flex: {
          horizontal: 'justify-content: center',
          vertical: 'align-items: center',
          both: 'justify-content: center; align-items: center'
        },
        grid: {
          horizontal: 'justify-items: center',
          vertical: 'align-items: center',
          both: 'place-items: center'
        }
      }
    };
  }

  private generateLayoutPatterns() {
    return {
      stack: {
        gap: {
          xs: '0.5rem',   // 8px
          sm: '1rem',     // 16px
          md: '1.5rem',   // 24px
          lg: '2rem',     // 32px
          xl: '3rem'      // 48px
        },
        alignment: {
          start: 'align-items: flex-start',
          center: 'align-items: center',
          end: 'align-items: flex-end',
          stretch: 'align-items: stretch'
        }
      },
      cluster: {
        gap: {
          xs: '0.5rem',   // 8px
          sm: '1rem',     // 16px
          md: '1.5rem',   // 24px
          lg: '2rem',     // 32px
          xl: '3rem'      // 48px
        },
        justify: {
          start: 'justify-content: flex-start',
          center: 'justify-content: center',
          end: 'justify-content: flex-end',
          between: 'justify-content: space-between',
          around: 'justify-content: space-around',
          evenly: 'justify-content: space-evenly'
        },
        align: {
          start: 'align-items: flex-start',
          center: 'align-items: center',
          end: 'align-items: flex-end',
          baseline: 'align-items: baseline'
        }
      },
      sidebar: {
        sidebarWidth: {
          narrow: '200px',
          default: '300px',
          wide: '400px'
        },
        gap: '2rem',
        breakpoint: '768px',
        contentMinWidth: '50%'
      },
      switcher: {
        threshold: '768px',
        gap: '1rem',
        limit: 4
      },
      cover: {
        minHeight: {
          viewport: '100vh',
          container: '100%',
          content: 'auto'
        },
        padding: {
          top: '2rem',
          bottom: '2rem',
          sides: '1rem'
        }
      },
      grid: {
        minItemWidth: {
          xs: '200px',
          sm: '250px',
          md: '300px',
          lg: '350px'
        },
        gap: {
          xs: '0.5rem',   // 8px
          sm: '1rem',     // 16px
          md: '1.5rem',   // 24px
          lg: '2rem',     // 32px
          xl: '3rem'      // 48px
        },
        autoFit: true,
        autoFill: false
      }
    };
  }

  private generateLayoutValidation() {
    return {
      rules: [
        {
          name: 'Container Max Width',
          description: 'Containers should not exceed maximum width tokens',
          selector: '.container, [class*="container"]',
          property: 'max-width',
          expectedValues: ['640px', '768px', '1024px', '1280px', '1536px'],
          severity: 'warning' as const
        },
        {
          name: 'Grid Gap Consistency',
          description: 'Grid gaps should use standardized spacing tokens',
          selector: '[style*="gap"], .grid',
          property: 'gap',
          expectedValues: ['0.5rem', '1rem', '1.5rem', '2rem', '3rem'],
          severity: 'warning' as const
        },
        {
          name: 'Responsive Breakpoints',
          description: 'Media queries should use standard breakpoint values',
          selector: '@media',
          property: 'min-width',
          expectedValues: ['640px', '768px', '1024px', '1280px', '1536px'],
          severity: 'info' as const
        }
      ],
      patterns: [
        {
          pattern: 'stack',
          requiredProperties: ['display: flex', 'flex-direction: column'],
          forbiddenProperties: ['float'],
          childConstraints: []
        },
        {
          pattern: 'cluster',
          requiredProperties: ['display: flex', 'flex-wrap: wrap'],
          forbiddenProperties: ['float'],
          childConstraints: []
        },
        {
          pattern: 'grid',
          requiredProperties: ['display: grid'],
          forbiddenProperties: ['float', 'display: table'],
          childConstraints: []
        }
      ],
      accessibility: {
        focusManagement: {
          tabOrder: true,
          focusVisible: true,
          skipLinks: true
        },
        semanticStructure: {
          headingHierarchy: true,
          landmarkRoles: true,
          listStructure: true
        },
        responsiveDesign: {
          minTouchTarget: '44px',
          maxLineLength: '75ch',
          scalableText: true
        }
      }
    };
  }

  /**
   * Exports design tokens in various formats
   */
  exportTokens(tokens: DesignTokenSystem, format: 'json' | 'css' | 'scss' | 'js' = 'json'): string {
    switch (format) {
      case 'css':
        return this.exportToCss(tokens);
      case 'scss':
        return this.exportToScss(tokens);
      case 'js':
        return this.exportToJs(tokens);
      default:
        return JSON.stringify(tokens, null, 2);
    }
  }

  private exportToCss(tokens: DesignTokenSystem): string {
    let css = ':root {\n';
    
    // Colors
    Object.entries(tokens.colors.primary).forEach(([key, value]) => {
      css += `  --color-primary-${key}: ${value};\n`;
    });
    
    Object.entries(tokens.colors.neutral).forEach(([key, value]) => {
      css += `  --color-neutral-${key}: ${value};\n`;
    });
    
    // Typography
    Object.entries(tokens.typography.fontSizes).forEach(([key, value]) => {
      css += `  --font-size-${key}: ${value};\n`;
    });
    
    // Spacing
    Object.entries(tokens.spacing.scale).forEach(([key, value]) => {
      css += `  --spacing-${key}: ${value};\n`;
    });
    
    // Layout - Breakpoints
    Object.entries(tokens.layout.breakpoints).forEach(([key, value]) => {
      if (key !== 'ranges') {
        css += `  --breakpoint-${key}: ${value};\n`;
      }
    });
    
    // Layout - Container Max Widths
    Object.entries(tokens.layout.containers.maxWidths).forEach(([key, value]) => {
      css += `  --container-${key}: ${value};\n`;
    });
    
    // Layout - Grid Gutters
    Object.entries(tokens.layout.grid.gutters).forEach(([key, value]) => {
      css += `  --grid-gutter-${key}: ${value};\n`;
    });
    
    css += '}\n';
    
    // Add layout pattern utility classes
    css += this.generateLayoutPatternCss(tokens);
    
    return css;
  }

  private generateLayoutPatternCss(tokens: DesignTokenSystem): string {
    let css = '\n/* Layout Pattern Utilities */\n';
    
    // Stack pattern
    css += '.stack {\n';
    css += '  display: flex;\n';
    css += '  flex-direction: column;\n';
    css += `  gap: var(--spacing-4);\n`;
    css += '}\n\n';
    
    // Cluster pattern
    css += '.cluster {\n';
    css += '  display: flex;\n';
    css += '  flex-wrap: wrap;\n';
    css += `  gap: var(--spacing-4);\n`;
    css += '}\n\n';
    
    // Grid pattern
    css += '.auto-grid {\n';
    css += '  display: grid;\n';
    css += `  grid-template-columns: repeat(auto-fit, minmax(${tokens.layout.patterns.grid.minItemWidth.md}, 1fr));\n`;
    css += `  gap: var(--spacing-4);\n`;
    css += '}\n\n';
    
    // Container pattern
    css += '.container {\n';
    css += '  width: 100%;\n';
    css += '  margin: 0 auto;\n';
    css += `  max-width: var(--container-xl);\n`;
    css += `  padding: 0 var(--spacing-4);\n`;
    css += '}\n\n';
    
    return css;
  }

  private exportToScss(tokens: DesignTokenSystem): string {
    let scss = '// Design Tokens\n\n';
    
    // Color maps
    scss += '$colors: (\n';
    scss += '  primary: (\n';
    Object.entries(tokens.colors.primary).forEach(([key, value]) => {
      scss += `    ${key}: ${value},\n`;
    });
    scss += '  ),\n';
    scss += ');\n\n';
    
    return scss;
  }

  private exportToJs(tokens: DesignTokenSystem): string {
    return `export const designTokens = ${JSON.stringify(tokens, null, 2)};`;
  }
}