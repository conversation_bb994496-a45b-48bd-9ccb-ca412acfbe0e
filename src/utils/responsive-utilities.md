# Responsive Utilities Documentation

This document provides guidance on using the responsive utilities system implemented for consistent spacing, typography, and layout across all screen sizes.

## Overview

The responsive utilities follow a mobile-first approach using Tailwind CSS breakpoints:
- **Mobile**: < 640px (base styles)
- **Small (sm)**: ≥ 640px
- **Medium (md)**: ≥ 768px
- **Large (lg)**: ≥ 1024px
- **Extra Large (xl)**: ≥ 1280px

## Container Classes

### Basic Containers
```css
.container-responsive  /* Standard responsive container with max-width: 1280px */
.container-fluid      /* Full-width container with responsive padding */
.container-narrow     /* Narrow container with max-width: 768px */
.container-wide       /* Wide container with max-width: 1440px */
```

### Usage Example
```tsx
<div className="container-responsive">
  <h1>Content with responsive container</h1>
</div>
```

## Typography Utilities

### Responsive Text Sizes
```css
.text-responsive-xs    /* text-xs sm:text-sm */
.text-responsive-sm    /* text-sm sm:text-base */
.text-responsive-base  /* text-base sm:text-lg */
.text-responsive-lg    /* text-lg sm:text-xl md:text-2xl */
.text-responsive-xl    /* text-xl sm:text-2xl md:text-3xl */
.text-responsive-2xl   /* text-2xl sm:text-3xl md:text-4xl lg:text-5xl */
.text-responsive-3xl   /* text-3xl sm:text-4xl md:text-5xl lg:text-6xl */
```

### Responsive Headings
```css
.heading-responsive-h1  /* Large responsive heading */
.heading-responsive-h2  /* Medium-large responsive heading */
.heading-responsive-h3  /* Medium responsive heading */
.heading-responsive-h4  /* Small-medium responsive heading */
```

### Usage Example
```tsx
<h1 className="heading-responsive-h1">Main Title</h1>
<p className="text-responsive-base">Body text that scales appropriately</p>
```

## Spacing Utilities

### Section Padding
```css
.section-padding-sm  /* py-8 md:py-12 lg:py-16 */
.section-padding-md  /* py-12 md:py-16 lg:py-20 */
.section-padding-lg  /* py-16 md:py-20 lg:py-24 */
.section-padding-xl  /* py-20 md:py-24 lg:py-32 */
```

### Responsive Spacing
```css
.space-responsive-xs   /* space-y-2 sm:space-y-3 md:space-y-4 */
.space-responsive-sm   /* space-y-4 sm:space-y-6 md:space-y-8 */
.space-responsive-md   /* space-y-6 sm:space-y-8 md:space-y-12 */
.space-responsive-lg   /* space-y-8 sm:space-y-12 md:space-y-16 */
.space-responsive-xl   /* space-y-12 sm:space-y-16 md:space-y-20 */
```

### Responsive Margins and Padding
```css
/* Margins */
.margin-responsive-xs  /* m-2 sm:m-3 md:m-4 */
.margin-responsive-sm  /* m-4 sm:m-6 md:m-8 */
.margin-responsive-md  /* m-6 sm:m-8 md:m-12 */
.margin-responsive-lg  /* m-8 sm:m-12 md:m-16 */
.margin-responsive-xl  /* m-12 sm:m-16 md:m-20 */

/* Padding */
.padding-responsive-xs /* p-2 sm:p-3 md:p-4 */
.padding-responsive-sm /* p-4 sm:p-6 md:p-8 */
.padding-responsive-md /* p-6 sm:p-8 md:p-12 */
.padding-responsive-lg /* p-8 sm:p-12 md:p-16 */
.padding-responsive-xl /* p-12 sm:p-16 md:p-20 */
```

### Responsive Gaps
```css
.gap-responsive-xs  /* gap-2 sm:gap-3 md:gap-4 */
.gap-responsive-sm  /* gap-4 sm:gap-6 md:gap-8 */
.gap-responsive-md  /* gap-6 sm:gap-8 md:gap-12 */
.gap-responsive-lg  /* gap-8 sm:gap-12 md:gap-16 */
.gap-responsive-xl  /* gap-12 sm:gap-16 md:gap-20 */
```

## Layout Utilities

### Responsive Grids
```css
.grid-responsive-1-2-3  /* grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 */
.grid-responsive-1-2-4  /* grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 */
.grid-responsive-1-3-4  /* grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-4 */
.grid-responsive-2-4-6  /* grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 */
```

### Responsive Flexbox
```css
.flex-responsive-col-row     /* flex flex-col sm:flex-row */
.flex-responsive-col-row-md  /* flex flex-col md:flex-row */
.flex-responsive-wrap        /* flex flex-wrap gap-4 sm:gap-6 md:gap-8 */
```

### Mobile-First Layout Utilities
```css
.mobile-stack        /* flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4 */
.mobile-center       /* text-center sm:text-left */
.mobile-full-width   /* w-full sm:w-auto */
```

## Component Utilities

### Responsive Buttons
```css
.btn-responsive-sm  /* px-3 py-2 text-sm sm:px-4 sm:py-2 sm:text-base */
.btn-responsive-md  /* px-4 py-2 text-base sm:px-6 sm:py-3 sm:text-lg */
.btn-responsive-lg  /* px-6 py-3 text-lg sm:px-8 sm:py-4 sm:text-xl */
```

### Responsive Cards
```css
.card-responsive         /* p-4 sm:p-6 md:p-8 rounded-lg shadow-sm hover:shadow-md */
.card-responsive-compact /* p-3 sm:p-4 md:p-6 rounded-md shadow-sm hover:shadow-md */
```

### Touch Targets
```css
.touch-target     /* min-h-[44px] min-w-[44px] */
.touch-target-lg  /* min-h-[48px] min-w-[48px] */
```

### Responsive Images
```css
.img-responsive        /* w-full h-auto object-cover */
.img-responsive-square /* w-full aspect-square object-cover */
.img-responsive-video  /* w-full aspect-video object-cover */
```

## Visibility Utilities

```css
.mobile-only     /* block sm:hidden */
.tablet-up       /* hidden sm:block */
.desktop-only    /* hidden lg:block */
.mobile-tablet   /* block lg:hidden */
```

## TypeScript Integration

Import and use the utility functions for type-safe class generation:

```tsx
import { 
  getContainerClass, 
  getHeadingResponsiveClass, 
  getGridResponsiveClass,
  getCurrentBreakpoint,
  isBreakpoint
} from '@/utils/responsive-utilities';

// Usage in components
const MyComponent = () => {
  return (
    <div className={getContainerClass('responsive')}>
      <h1 className={getHeadingResponsiveClass('h1')}>
        Responsive Title
      </h1>
      <div className={getGridResponsiveClass('1-2-3')}>
        {/* Grid content */}
      </div>
    </div>
  );
};

// Check current breakpoint
const currentBreakpoint = getCurrentBreakpoint();
const isDesktop = isBreakpoint('lg');
```

## Usage Examples

### Section Layout
```tsx
<section className="section-padding-md bg-white">
  <div className="container-responsive">
    <div className="space-responsive-md">
      <h2 className="heading-responsive-h2 mobile-center">
        Section Title
      </h2>
      <div className="grid-responsive-1-2-3 gap-responsive-md">
        {/* Content cards */}
      </div>
    </div>
  </div>
</section>
```

### Card Component
```tsx
<div className="card-responsive">
  <h3 className="heading-responsive-h3">Card Title</h3>
  <p className="text-responsive-base">Card content</p>
  <button className="btn-responsive-md touch-target">
    Action Button
  </button>
</div>
```

### Form Layout
```tsx
<form className="space-responsive-sm">
  <div className="flex-responsive-col-row gap-responsive-sm">
    <input className="mobile-full-width touch-target" />
    <button className="btn-responsive-md touch-target">
      Submit
    </button>
  </div>
</form>
```

## Best Practices

1. **Mobile-First**: Always design for mobile first, then enhance for larger screens
2. **Touch Targets**: Use `.touch-target` class for interactive elements on mobile
3. **Consistent Spacing**: Use the responsive spacing utilities instead of arbitrary values
4. **Typography Scale**: Use responsive typography classes for consistent text scaling
5. **Container Usage**: Always wrap content in appropriate container classes
6. **Grid Patterns**: Use predefined grid patterns for common layouts
7. **Visibility**: Use visibility utilities sparingly and only when necessary

## Migration Guide

To migrate existing components to use these utilities:

1. Replace custom padding/margin with responsive utilities
2. Update typography to use responsive text classes
3. Convert grid layouts to use responsive grid utilities
4. Add touch targets to interactive elements
5. Use container classes for consistent layout bounds

Example migration:
```tsx
// Before
<div className="px-4 md:px-8 py-12 md:py-16">
  <h1 className="text-2xl md:text-4xl font-bold">Title</h1>
</div>

// After
<div className="container-responsive section-padding-md">
  <h1 className="heading-responsive-h2">Title</h1>
</div>
```