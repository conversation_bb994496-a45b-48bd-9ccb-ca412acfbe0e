// Main audit system exports

// Core engine and utilities
export { DesignSystemAuditEngine } from './core/audit-engine';
export { FileScanner } from './core/file-scanner';
export { auditLogger, AuditLogger, LogLevel } from './core/logger';

// Analyzers
export { ColorAnalyzer } from './analyzers/color-analyzer';
export { SpacingAnalyzer } from './analyzers/spacing-analyzer';
export { TypographyAnalyzer } from './analyzers/typography-analyzer';

// Remediation Engines
export { TypographyRemediationEngine } from './remediation/typography-remediation';
export type {
  TypographyRemediationResult,
  TypographyReplacement,
  TypographyHierarchyCorrection,
  LineHeightStandardization
} from './remediation/typography-remediation';

// Design Token System
export { DesignTokenGenerator } from './design-tokens/generator';
export { DesignTokenValidator } from './design-tokens/token-validator';
export { LayoutPatternValidator } from './design-tokens/layout-validator';
export type {
  DesignTokenSystem,
  ColorTokens,
  TypographyTokens,
  SpacingTokens,
  LayoutTokens,
  TokenValidationResult,
  TokenViolation,
  ComplianceReport,
  LayoutValidationResult,
  LayoutViolation,
  LayoutCompliance
} from './design-tokens/types';

// Documentation System
export {
  ComponentDocumentationGenerator,
  DesignTokenDocumentationGenerator,
  StyleGuidelineGenerator,
  InteractiveExampleGenerator
} from './documentation';
export type {
  ComponentDocumentation,
  ComponentSpec,
  DesignTokenDocumentation,
  StyleGuidelines,
  InteractiveExample
} from './documentation';

// Types and interfaces
export * from './types';

// Re-export for convenience
export type {
  AuditEngine,
  Analyzer,
  AuditResult,
  AuditReport,
  AnalyzerResult,
  FileInfo,
  Issue,
  AuditConfig
} from './types';