// Color usage analyzer for detecting hardcoded colors and inconsistent token usage

import { 
  <PERSON><PERSON><PERSON>, 
  AnalyzerResult, 
  FileInfo, 
  Issue, 
  IssueType, 
  Severity, 
  Category, 
  FileLocation, 
  ImpactAssessment 
} from '../types';
import { auditLogger } from '../core/logger';

interface ColorMatch {
  value: string;
  type: 'hex' | 'rgb' | 'rgba' | 'hsl' | 'hsla' | 'named' | 'token';
  line: number;
  column: number;
  context: string;
}

interface ColorTokenPattern {
  pattern: RegExp;
  description: string;
}

export class ColorAnalyzer implements Analyzer {
  name = 'color-analyzer';

  // Common color patterns to detect
  private colorPatterns = {
    hex: /#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})\b/g,
    rgb: /rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/g,
    rgba: /rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([0-9]*\.?[0-9]+)\s*\)/g,
    hsl: /hsl\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*\)/g,
    hsla: /hsla\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*,\s*([0-9]*\.?[0-9]+)\s*\)/g,
    named: /\b(red|blue|green|yellow|orange|purple|pink|brown|black|white|gray|grey|cyan|magenta|lime|navy|teal|olive|maroon|silver|aqua|fuchsia)\b/gi
  };

  // Common design token patterns
  private tokenPatterns: ColorTokenPattern[] = [
    { pattern: /var\(--[\w-]+\)/g, description: 'CSS custom properties' },
    { pattern: /theme\(['"`]colors\.[\w.-]+['"`]\)/g, description: 'Theme function calls' },
    { pattern: /colors\.[\w.-]+/g, description: 'Color object references' },
    { pattern: /\$[\w-]+/g, description: 'SCSS variables' },
    { pattern: /@[\w-]+/g, description: 'Less variables' },
    { pattern: /tailwind\.[\w.-]+/g, description: 'Tailwind config references' }
  ];

  // CSS properties that commonly use colors
  private colorProperties = [
    'color', 'background-color', 'background', 'border-color', 'border',
    'border-top-color', 'border-right-color', 'border-bottom-color', 'border-left-color',
    'outline-color', 'text-shadow', 'box-shadow', 'fill', 'stroke'
  ];

  async analyze(files: FileInfo[]): Promise<AnalyzerResult> {
    const startTime = Date.now();
    auditLogger.info('Starting color analysis', { fileCount: files.length });

    const issues: Issue[] = [];
    const colorUsageMap = new Map<string, ColorMatch[]>();

    // Filter files that might contain color definitions
    const relevantFiles = files.filter(file => 
      ['.tsx', '.jsx', '.ts', '.js', '.css', '.scss', '.sass', '.less'].includes(file.extension) ||
      file.path.includes('style') ||
      file.path.includes('theme') ||
      file.path.includes('color')
    );

    for (const file of relevantFiles) {
      try {
        const fileIssues = await this.analyzeFile(file, colorUsageMap);
        issues.push(...fileIssues);
      } catch (error) {
        auditLogger.error('Failed to analyze file for colors', error as Error, { 
          filePath: file.path 
        });
      }
    }

    // Analyze color usage patterns across files
    const patternIssues = this.analyzeColorPatterns(colorUsageMap);
    issues.push(...patternIssues);

    const executionTime = Date.now() - startTime;
    
    const result: AnalyzerResult = {
      analyzerName: this.name,
      issues,
      summary: {
        totalIssues: issues.length,
        severityBreakdown: this.calculateSeverityBreakdown(issues),
        categoryBreakdown: this.calculateCategoryBreakdown(issues)
      },
      executionTime
    };

    auditLogger.info('Color analysis completed', { 
      issuesFound: issues.length,
      executionTime 
    });

    return result;
  }

  private async analyzeFile(file: FileInfo, colorUsageMap: Map<string, ColorMatch[]>): Promise<Issue[]> {
    const issues: Issue[] = [];
    const lines = file.content.split('\n');
    
    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex];
      const lineNumber = lineIndex + 1;

      // Find hardcoded colors
      const hardcodedColors = this.findHardcodedColors(line, lineNumber);
      
      // Find token usage
      const tokenUsage = this.findTokenUsage(line, lineNumber);

      // Track color usage for pattern analysis
      const allColors = [...hardcodedColors, ...tokenUsage];
      allColors.forEach(color => {
        const key = `${file.path}:${color.value}`;
        if (!colorUsageMap.has(key)) {
          colorUsageMap.set(key, []);
        }
        colorUsageMap.get(key)!.push({
          ...color,
          context: line.trim()
        });
      });

      // Create issues for hardcoded colors
      for (const color of hardcodedColors) {
        if (this.shouldFlagHardcodedColor(color, line)) {
          const issue = this.createHardcodedColorIssue(file, color, lineNumber, line);
          issues.push(issue);
        }
      }

      // Check for inconsistent token usage patterns
      const tokenIssues = this.checkTokenConsistency(file, tokenUsage, lineNumber, line);
      issues.push(...tokenIssues);
    }

    return issues;
  }

  private findHardcodedColors(line: string, lineNumber: number): ColorMatch[] {
    const colors: ColorMatch[] = [];

    // Check each color pattern
    Object.entries(this.colorPatterns).forEach(([type, pattern]) => {
      let match;
      const regex = new RegExp(pattern.source, pattern.flags);
      
      while ((match = regex.exec(line)) !== null) {
        colors.push({
          value: match[0],
          type: type as ColorMatch['type'],
          line: lineNumber,
          column: match.index,
          context: line.trim()
        });
      }
    });

    return colors;
  }

  private findTokenUsage(line: string, lineNumber: number): ColorMatch[] {
    const tokens: ColorMatch[] = [];

    this.tokenPatterns.forEach(({ pattern }) => {
      let match;
      const regex = new RegExp(pattern.source, pattern.flags);
      
      while ((match = regex.exec(line)) !== null) {
        tokens.push({
          value: match[0],
          type: 'token',
          line: lineNumber,
          column: match.index,
          context: line.trim()
        });
      }
    });

    return tokens;
  }

  private shouldFlagHardcodedColor(color: ColorMatch, line: string): boolean {
    // Don't flag colors in comments
    if (line.trim().startsWith('//') || line.trim().startsWith('/*')) {
      return false;
    }

    // Don't flag transparent or common utility colors
    const commonUtilityColors = ['transparent', 'inherit', 'currentColor', 'initial', 'unset'];
    if (commonUtilityColors.includes(color.value.toLowerCase())) {
      return false;
    }

    // Don't flag very common colors that might be intentional
    const commonIntentionalColors = ['#000', '#fff', '#000000', '#ffffff', 'black', 'white'];
    if (commonIntentionalColors.includes(color.value.toLowerCase())) {
      // Only flag if it's in a CSS property context
      return this.colorProperties.some(prop => 
        line.toLowerCase().includes(`${prop}:`) || 
        line.toLowerCase().includes(`${prop} `)
      );
    }

    return true;
  }

  private createHardcodedColorIssue(
    file: FileInfo, 
    color: ColorMatch, 
    lineNumber: number, 
    line: string
  ): Issue {
    const severity = this.getSeverityForHardcodedColor(color, line);
    
    return {
      id: `color_${file.path}_${lineNumber}_${color.column}`,
      type: IssueType.COLOR_INCONSISTENCY,
      severity,
      category: Category.VISUAL,
      description: `Hardcoded color value '${color.value}' found. Consider using design tokens for consistency.`,
      location: {
        filePath: file.path,
        lineNumber,
        columnNumber: color.column,
        context: line.trim()
      },
      suggestion: this.generateColorSuggestion(color, line),
      autoFixable: this.isColorAutoFixable(color, line),
      impact: this.calculateColorImpact(color, line)
    };
  }

  private checkTokenConsistency(
    file: FileInfo, 
    tokens: ColorMatch[], 
    lineNumber: number, 
    line: string
  ): Issue[] {
    const issues: Issue[] = [];

    // Check for mixed token patterns in the same file
    const tokenTypes = new Set(tokens.map(token => this.getTokenType(token.value)));
    
    if (tokenTypes.size > 1) {
      // Multiple token patterns in same line might indicate inconsistency
      const issue: Issue = {
        id: `color_token_mix_${file.path}_${lineNumber}`,
        type: IssueType.COLOR_INCONSISTENCY,
        severity: Severity.MEDIUM,
        category: Category.VISUAL,
        description: 'Mixed color token patterns detected. Consider standardizing on one approach.',
        location: {
          filePath: file.path,
          lineNumber,
          columnNumber: 0,
          context: line.trim()
        },
        suggestion: 'Standardize on a single color token pattern throughout the codebase.',
        autoFixable: false,
        impact: {
          userExperience: 3,
          maintenanceEffort: 6,
          implementationComplexity: 4
        }
      };
      
      issues.push(issue);
    }

    return issues;
  }

  private analyzeColorPatterns(colorUsageMap: Map<string, ColorMatch[]>): Issue[] {
    const issues: Issue[] = [];
    const colorFrequency = new Map<string, number>();
    
    // Count color usage frequency
    colorUsageMap.forEach((matches, key) => {
      const colorValue = key.split(':')[1];
      colorFrequency.set(colorValue, (colorFrequency.get(colorValue) || 0) + matches.length);
    });

    // Find frequently used hardcoded colors that should be tokens
    colorFrequency.forEach((count, colorValue) => {
      if (count >= 3 && this.isHardcodedColor(colorValue)) {
        const locations = this.getColorLocations(colorValue, colorUsageMap);
        
        const issue: Issue = {
          id: `color_frequent_${colorValue.replace(/[^a-zA-Z0-9]/g, '_')}`,
          type: IssueType.COLOR_INCONSISTENCY,
          severity: count >= 5 ? Severity.HIGH : Severity.MEDIUM,
          category: Category.VISUAL,
          description: `Color '${colorValue}' is used ${count} times. Consider creating a design token.`,
          location: locations[0], // Use first location as primary
          suggestion: `Create a design token for '${colorValue}' and replace all ${count} instances.`,
          autoFixable: true,
          impact: {
            userExperience: 4,
            maintenanceEffort: 8,
            implementationComplexity: 3
          }
        };
        
        issues.push(issue);
      }
    });

    return issues;
  }

  private getColorLocations(colorValue: string, colorUsageMap: Map<string, ColorMatch[]>): FileLocation[] {
    const locations: FileLocation[] = [];
    
    colorUsageMap.forEach((matches, key) => {
      if (key.includes(colorValue)) {
        const filePath = key.split(':')[0];
        matches.forEach(match => {
          locations.push({
            filePath,
            lineNumber: match.line,
            columnNumber: match.column,
            context: match.context
          });
        });
      }
    });

    return locations;
  }

  private isHardcodedColor(value: string): boolean {
    return !value.startsWith('var(') && 
           !value.startsWith('theme(') && 
           !value.startsWith('$') && 
           !value.startsWith('@') &&
           !value.includes('colors.');
  }

  private getTokenType(tokenValue: string): string {
    if (tokenValue.startsWith('var(')) return 'css-custom-property';
    if (tokenValue.startsWith('theme(')) return 'theme-function';
    if (tokenValue.startsWith('$')) return 'scss-variable';
    if (tokenValue.startsWith('@')) return 'less-variable';
    if (tokenValue.includes('colors.')) return 'color-object';
    return 'unknown';
  }

  private getSeverityForHardcodedColor(color: ColorMatch, line: string): Severity {
    // Higher severity for colors in component files
    if (line.includes('.tsx') || line.includes('.jsx')) {
      return Severity.HIGH;
    }

    // Medium severity for CSS files
    if (color.type === 'hex' || color.type === 'rgb' || color.type === 'rgba') {
      return Severity.MEDIUM;
    }

    // Lower severity for named colors
    return Severity.LOW;
  }

  private generateColorSuggestion(color: ColorMatch, line: string): string {
    const suggestions = [
      `Replace '${color.value}' with a design token like 'var(--color-primary)'`,
      `Use theme colors: 'theme("colors.primary.500")'`,
      `Define as SCSS variable: '$primary-color: ${color.value}'`,
      `Add to color palette and reference as 'colors.primary'`
    ];

    // Return most appropriate suggestion based on file type and context
    if (line.includes('var(')) return suggestions[0];
    if (line.includes('theme(')) return suggestions[1];
    if (line.includes('$')) return suggestions[2];
    
    return suggestions[0]; // Default to CSS custom properties
  }

  private isColorAutoFixable(color: ColorMatch, line: string): boolean {
    // Simple hardcoded colors in CSS properties are auto-fixable
    return this.colorProperties.some(prop => 
      line.includes(`${prop}:`) && 
      (color.type === 'hex' || color.type === 'rgb' || color.type === 'rgba')
    );
  }

  private calculateColorImpact(color: ColorMatch, line: string): ImpactAssessment {
    let userExperience = 3;
    let maintenanceEffort = 5;
    let implementationComplexity = 2;

    // Higher impact for component files
    if (line.includes('tsx') || line.includes('jsx')) {
      userExperience += 2;
      maintenanceEffort += 2;
    }

    // Higher maintenance effort for complex color values
    if (color.type === 'rgba' || color.type === 'hsla') {
      maintenanceEffort += 1;
      implementationComplexity += 1;
    }

    return {
      userExperience: Math.min(10, userExperience),
      maintenanceEffort: Math.min(10, maintenanceEffort),
      implementationComplexity: Math.min(10, implementationComplexity)
    };
  }

  getSeverity(issue: Omit<Issue, 'severity'>): Severity {
    // Default severity calculation based on issue type and context
    if (issue.type === IssueType.COLOR_INCONSISTENCY) {
      if (issue.description.includes('frequently used')) {
        return Severity.HIGH;
      }
      if (issue.description.includes('hardcoded')) {
        return Severity.MEDIUM;
      }
    }
    
    return Severity.LOW;
  }

  private calculateSeverityBreakdown(issues: Issue[]): Record<Severity, number> {
    const breakdown: Record<Severity, number> = {
      [Severity.LOW]: 0,
      [Severity.MEDIUM]: 0,
      [Severity.HIGH]: 0,
      [Severity.CRITICAL]: 0
    };

    issues.forEach(issue => {
      breakdown[issue.severity]++;
    });

    return breakdown;
  }

  private calculateCategoryBreakdown(issues: Issue[]): Record<Category, number> {
    const breakdown: Record<Category, number> = {
      [Category.VISUAL]: 0,
      [Category.CONTENT]: 0,
      [Category.COMPONENT]: 0,
      [Category.ACCESSIBILITY]: 0
    };

    issues.forEach(issue => {
      breakdown[issue.category]++;
    });

    return breakdown;
  }
}