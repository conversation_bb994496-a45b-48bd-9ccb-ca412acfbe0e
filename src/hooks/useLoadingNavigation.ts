import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

/**
 * Custom hook for handling navigation with loading state
 * @param loadingTime - Minimum time to show loading state in ms (default: 500ms)
 * @returns Object with loading state and navigation function
 */
export function useLoadingNavigation(loadingTime = 500) {
  const [isLoading, setIsLoading] = useState(false);
  const [destination, setDestination] = useState<string | null>(null);
  const navigate = useNavigate();

  const navigateWithLoading = useCallback((to: string) => {
    setIsLoading(true);
    setDestination(to);
    
    // Navigate after a minimum delay to ensure loading state is visible
    setTimeout(() => {
      navigate(to);
      // Reset loading state after navigation
      setTimeout(() => {
        setIsLoading(false);
        setDestination(null);
      }, 100); // Small delay to ensure loading state is reset after navigation
    }, loadingTime);
  }, [navigate, loadingTime]);

  return { isLoading, destination, navigateWithLoading };
}

export default useLoadingNavigation;
