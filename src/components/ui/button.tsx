import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-elev-blue focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-elev-navy text-elev-text-light hover:bg-elev-navy/90 shadow-sm",
        destructive:
          "bg-red-600 text-white hover:bg-red-700 shadow-sm",
        outline:
          "border-2 border-elev-navy bg-white text-elev-text-primary hover:bg-elev-background-muted hover:text-elev-navy shadow-sm",
        secondary:
          "bg-elev-background-muted text-elev-text-primary hover:bg-elev-background-accent shadow-sm",
        ghost: "text-elev-text-primary hover:bg-elev-background-muted hover:text-elev-navy",
        link: "text-elev-blue hover:text-elev-blue/90 underline-offset-4 hover:underline",
        white: "bg-white text-elev-navy hover:bg-gray-100 border border-transparent shadow-sm",
        navy: "bg-elev-navy text-white hover:bg-elev-navy/90 shadow-sm",
      },
      size: {
        default: "min-h-[44px] px-4 py-2 sm:h-10",
        sm: "min-h-[40px] rounded-md px-3 text-sm sm:h-9",
        lg: "min-h-[48px] rounded-md px-8 text-base sm:h-12",
        xl: "min-h-[52px] rounded-lg px-10 text-lg sm:h-14",
        icon: "min-h-[44px] min-w-[44px] sm:h-10 sm:w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
