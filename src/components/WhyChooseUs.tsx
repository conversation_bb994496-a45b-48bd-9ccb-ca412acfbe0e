
// SECTION: Imports
// ==================================================================================
import { cn } from "@/lib/utils";
import { useEffect, useRef } from "react";
import { getResponsiveTextSize, getResponsivePadding, getTouchTargetClass } from "@/utils/responsive-utilities";

// SECTION: Interface Definition
// ==================================================================================
interface FeatureProps {
  title: string;
  description: string;
  icon: string;
  delay: number;
}

// SECTION: Feature Component
// ==================================================================================
const Feature = ({ title, description, icon, delay }: FeatureProps) => {
  // --- Hooks ---
  const featureRef = useRef<HTMLDivElement>(null);

  // Effect for reveal animation
  useEffect(() => {
    const currentFeatureRef = featureRef.current;
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setTimeout(() => {
              entry.target.classList.add("active");
            }, delay);
          }
        });
      },
      { threshold: 0.1 }
    );

    if (currentFeatureRef) {
      observer.observe(currentFeatureRef);
    }

    return () => {
      if (currentFeatureRef) {
        observer.unobserve(currentFeatureRef);
      }
    };
  }, [delay]);

  // --- Component JSX ---
  return (
    <div
      ref={featureRef}
      className={cn(
        "bg-white rounded-lg shadow-lg flex flex-col items-center text-center reveal",
        "hover:shadow-xl transition-all duration-300 hover:-translate-y-1",
        getResponsivePadding("6", "8", "8")
      )}
    >
      <div className={cn(
        "w-16 h-16 bg-elev-navy rounded-full flex items-center justify-center mb-4",
        getTouchTargetClass()
      )}>
        <span className={cn(
          "text-white",
          getResponsiveTextSize("xl", "2xl", "2xl")
        )} aria-hidden="true">{icon}</span>
      </div>
      <h3 className={cn(
        "font-bold mb-2 text-elev-navy",
        getResponsiveTextSize("lg", "xl", "xl")
      )}>{title}</h3>
      <p className={cn(
        "text-gray-600",
        getResponsiveTextSize("sm", "base", "base")
      )}>{description}</p>
    </div>
  );
};

// SECTION: WhyChooseUs Component (Main)
// ==================================================================================
const WhyChooseUs = () => {
  // --- Hooks ---
  const sectionRef = useRef<HTMLDivElement>(null);

  // Effect for section reveal animation
  useEffect(() => {
    const currentSectionRef = sectionRef.current;
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("active");
          }
        });
      },
      { threshold: 0.1 }
    );

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  // --- Component JSX ---
  return (
    <section className="bg-elev-background-muted section-padding" id="why-choose-us">
      <div className="container mx-auto">
        <div className="text-center mb-12 reveal" ref={sectionRef}>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-elev-navy">
            The <span className="text-elev-navy">ELEV802</span> Difference
          </h2>
          <p className="text-lg text-elev-text-secondary max-w-3xl mx-auto">
            We deliver premium hockey and performance training through a holistic, individualized approach designed to elevate every aspect of your game.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Feature
            title="Individualized Approach"
            description="Customized training programs designed specifically for your goals and performance needs."
            icon="👤"
            delay={100}
          />
          <Feature
            title="Holistic Training"
            description="Develop complete hockey players through integrated skill, strength, and movement training."
            icon="🏒"
            delay={200}
          />
          <Feature
            title="Expert Coaching"
            description="Learn from experienced professionals and former professional players with proven results."
            icon="🥇"
            delay={300}
          />
          <Feature
            title="Safe Environment"
            description="Training focused on proper mechanics and injury prevention for long-term development."
            icon="🛡️"
            delay={400}
          />
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;
