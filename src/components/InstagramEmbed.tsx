import { useCallback, useEffect, useRef, useState } from 'react';
import type { InstagramPostProps } from './instagram';

/**
 * InstagramEmbed component for embedding Instagram posts
 * Uses a safer approach to load the Instagram embed script and process embeds
 * Optimized for responsive design with mobile-first approach
 */
const InstagramEmbed: React.FC<InstagramPostProps> = ({ 
  postUrl = 'https://www.instagram.com/p/C78_gA7yZ6J/',
  captions = false 
}) => {
  const embedRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const processAttempts = useRef(0);
  const maxAttempts = 5;
  
  // Function to safely process embeds - wrapped in useCallback to prevent recreation on each render
  const processEmbeds = useCallback(() => {
    if (!embedRef.current) return;
    
    try {
      if (window.instgrm?.Embeds?.process) {
        window.instgrm.Embeds.process(embedRef.current);
        setIsLoading(false);
      } else if (processAttempts.current < maxAttempts) {
        // If instgrm is not available yet, try again shortly
        processAttempts.current += 1;
        setTimeout(processEmbeds, 500);
      } else {
        // Give up after max attempts
        console.warn('Instagram embed processing timed out');
        setHasError(true);
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Error processing Instagram embed:', error);
      setHasError(true);
      setIsLoading(false);
    }
  }, [maxAttempts, setIsLoading, setHasError]);

  useEffect(() => {
    // Check if the script already exists in the document
    const existingScript = document.querySelector('script[src*="instagram.com/embed.js"]');
    
    if (existingScript) {
      // If script exists, just process the embeds
      processEmbeds();
      return;
    }

    // Create and append the script if it doesn't exist
    const script = document.createElement('script');
    script.async = true;
    script.defer = true;
    script.src = 'https://www.instagram.com/embed.js';
    script.id = 'instagram-embed-script';
    
    script.onload = () => {
      console.log('Instagram embed script loaded');
      processEmbeds();
    };
    
    script.onerror = (error) => {
      console.error('Error loading Instagram embed script:', error);
      setHasError(true);
      setIsLoading(false);
    };
    
    document.body.appendChild(script);

    // Set up an observer to detect when the post is visible in the viewport
    if ('IntersectionObserver' in window) {
      // Store a reference to the current element for cleanup
      const currentRef = embedRef.current;
      
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting && window.instgrm?.Embeds?.process) {
            window.instgrm.Embeds.process(currentRef);
          }
        });
      }, { threshold: 0.1 });
      
      if (currentRef) {
        observer.observe(currentRef);
      }
      
      return () => {
        if (currentRef) {
          observer.unobserve(currentRef);
        }
      };
    }
  }, [processEmbeds]);

  return (
    <div className="w-full max-w-lg mx-auto px-2 sm:px-4">
      <div ref={embedRef} className="instagram-embed-container w-full">
        {isLoading && (
          <div className="flex items-center justify-center p-8 bg-gray-50 rounded-lg border border-gray-200">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-500 mx-auto mb-4"></div>
              <p className="text-gray-600 text-sm sm:text-base">Loading Instagram post...</p>
            </div>
          </div>
        )}
        
        {hasError && (
          <div className="flex items-center justify-center p-8 bg-red-50 rounded-lg border border-red-200">
            <div className="text-center">
              <div className="text-red-500 mb-2">
                <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <p className="text-red-700 text-sm sm:text-base">Could not load Instagram post. Please check your connection.</p>
            </div>
          </div>
        )}
        
        <blockquote
          className={`instagram-media w-full bg-white border-0 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 ${
            hasError ? 'hidden' : 'block'
          }`}
          data-instgrm-permalink={postUrl}
          data-instgrm-version="14"
          data-instgrm-captioned={captions ? "true" : undefined}
          style={{
            maxWidth: '100%',
            minWidth: '280px',
            margin: 0,
            padding: 0,
          }}
        >
          <div className="p-4">
            <a
              href={postUrl}
              className="block w-full text-center text-gray-700 hover:text-pink-600 transition-colors duration-200 no-underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              <div className="flex items-center justify-center space-x-2">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                </svg>
                <span className="text-sm sm:text-base">View this post on Instagram</span>
              </div>
            </a>
          </div>
        </blockquote>
      </div>
    </div>
  );
};

export default InstagramEmbed;

// Declare global instgrm to prevent TypeScript errors
declare global {
  interface Window {
    instgrm?: {
      Embeds: {
        process: (node?: HTMLElement | null) => void;
      };
    };
  }
}